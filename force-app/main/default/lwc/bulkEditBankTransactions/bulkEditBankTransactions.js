import { LightningElement, api, track } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';

// Apex Methods
import getTransactionFields from '@salesforce/apex/BankTransactionController.getTransactionFields';
import getTransactions from '@salesforce/apex/BankTransactionController.getTransactions';
import saveTransactions from '@salesforce/apex/BankTransactionController.saveTransactions';

// Constants
const DEFAULT_FIELDS = ['Name', 'Transaction_Date__c', 'Description__c', 'Credit__c', 'Debit__c', 'AI_Category__c', 'MF_Category__c', 'MF_Category_Manual_Override__c'];
const PAGE_SIZE_OPTIONS = [ { label: '10', value: 10 }, { label: '25', value: 25 }, { label: '50', value: 50 }, { label: '100', value: 100 }, { label: '500', value: 500 }, { label: 'All', value: 999999 } ];
const DEFAULT_SORT_DIRECTION = 'asc';
const DEFAULT_SORT_BY = 'Transaction_Date__c';

export default class BulkEditBankTransactions extends LightningElement {
    @api recordId;
    @api objectApiName;

    @track columns = [];
    @track draftValues = [];
    @track error;
    @track isLoading = true;
    @track isModalOpen = false;

    @track availableFieldOptions = [];
    @track selectedFieldValues = [];
    _allAvailableFieldsInfo = [];

    // Pagination state
    @track pagination = { currentPage: 1, pageSize: PAGE_SIZE_OPTIONS[3].value, totalRecords: 0, totalPages: 1, firstRecordNumber: 0, lastRecordNumber: 0 };
    pageSizeOptions = PAGE_SIZE_OPTIONS;
    _allData = [];
    _pagedDataCache = [];

    // *** ADDED: Sorting state ***
    @track sortBy = DEFAULT_SORT_BY;
    @track sortDirection = DEFAULT_SORT_DIRECTION;

    get falseVal() {
        return false;
    }


    connectedCallback() {
        console.log('Context Record ID:', this.recordId);
        console.log('Context Object API Name:', this.objectApiName);
        if (!this.recordId || !this.objectApiName) {
            this.error = 'This component must be placed on a standard record page (Account or Bank Account).';
            this.isLoading = false;
        } else {
            this.loadInitialData();
        }
    }

    loadInitialData() {
        this.isLoading = true;
        this.error = undefined;

        getTransactionFields()
            .then(fieldData => {
                this._allAvailableFieldsInfo = fieldData.map(field => ({ ...field }));
                this.availableFieldOptions = fieldData.map(field => ({ label: field.label, value: field.value }));

                const validApiNames = new Set(fieldData.map(f => f.value));
                this.selectedFieldValues = DEFAULT_FIELDS.filter(f => validApiNames.has(f));

                if (this.selectedFieldValues.length === 0 && this.availableFieldOptions.length > 0) {
                    this.selectedFieldValues = this.availableFieldOptions.slice(0, 5).map(f => f.value);
                }

                
                if (!this.selectedFieldValues.includes(DEFAULT_SORT_BY) && validApiNames.has(DEFAULT_SORT_BY)) {
                    this.selectedFieldValues.push(DEFAULT_SORT_BY);
                } else if (!validApiNames.has(DEFAULT_SORT_BY) && this.selectedFieldValues.length > 0) {
                    this.sortBy = this.selectedFieldValues[0];
                } else if (this.selectedFieldValues.length === 0) {
                    this.sortBy = undefined;
                }

                this.buildColumns();
                return this.fetchTransactionData();
            })
            .catch(error => {
                this.error = 'Error loading initial component data: ' + this.reduceErrors(error).join(', ');
                this._allAvailableFieldsInfo = [];
                this.availableFieldOptions = [];
                this.isLoading = false;
            });
    }

    fetchTransactionData() {
        return getTransactions({ recordId: this.recordId, objectApiName: this.objectApiName })
            .then(result => {
                this._allData = result.map(row => {
                    const flatRow = {...row};
                    if (row.Bank_Account__r) {
                        flatRow.BankAccountName = row.Bank_Account__r.Name;
                    }
                    // Ensure numeric fields are numbers for sorting
                    this.columns.forEach(col => {
                        if (['currency', 'number', 'percent'].includes(col.type) && flatRow[col.fieldName] !== null && flatRow[col.fieldName] !== undefined) {
                            flatRow[col.fieldName] = Number(flatRow[col.fieldName]);
                        }
                    });
                    return flatRow;
                });

                this.pagination.totalRecords = this._allData.length;
                this.pagination.currentPage = 1;
                this.draftValues = []; 
                this.sortData(this.sortBy, this.sortDirection);
                this.paginateData();
                this.error = undefined;
            })
            .catch(error => {
                this.error = 'Error loading transactions: ' + this.reduceErrors(error).join(', ');
                this._allData = [];
                this.pagedData = [];
                this.pagination.totalRecords = 0;
                this.paginateData();
                throw error;
            })
            .finally(() => {
                this.isLoading = false;
            });
    }

    buildColumns() {
        if (!this._allAvailableFieldsInfo || this._allAvailableFieldsInfo.length === 0 || !this.selectedFieldValues) {
            this.columns = [];
            return;
        }
        this.columns = this.selectedFieldValues.map(apiName => {
            const fieldInfo = this._allAvailableFieldsInfo.find(f => f.value === apiName);
            if (!fieldInfo) return null;

            let columnDef = {
                label: fieldInfo.label,
                fieldName: apiName,
                type: this.getDatatableType(fieldInfo.type),
                sortable: true,
                editable: true
            };

                if (apiName === 'Bank_Account__r.Name') {
                columnDef.fieldName = 'BankAccountName';
                columnDef.type = 'text';
                columnDef.editable = false;
                }
                if (columnDef.type === 'currency') { columnDef.typeAttributes = { currencyCode: 'USD', step: '0.01' }; }
                if (columnDef.type === 'date-local') { columnDef.typeAttributes = {}; }
                if (columnDef.type === 'number') { columnDef.typeAttributes = { maximumFractionDigits: fieldInfo.type === 'currency' || fieldInfo.type === 'double' ? 2 : 0 }; }

                const nonEditableFields = ['Name'];
                if (nonEditableFields.includes(apiName)) { columnDef.editable = false; }

            return columnDef;
        }).filter(col => col !== null);
    }

    getDatatableType(apexType) {
        switch (apexType?.toLowerCase()) {
            case 'string': case 'textarea': case 'picklist': case 'multipicklist': case 'phone': case 'url': case 'email': case 'id': case 'reference': return 'text';
            case 'boolean': return 'boolean';
            case 'date': return 'date-local';
            case 'datetime': return 'datetime';
            case 'integer': case 'long': case 'double': case 'percent': return 'number';
            case 'currency': return 'currency';
            default: console.warn(`Unsupported field type for datatable: ${apexType}`); return 'text';
        }
    }

     handleCellChange(event) {
    if (!Array.isArray(this.draftValues)) {
        this.draftValues = [];
    }

    const newDrafts = event.detail.draftValues;

    // Combine old + new, then dedupe by record Id so the most recent change wins
    const merged = [...this.draftValues, ...newDrafts];
    this.draftValues = merged.reduce((acc, curr) => {
        const idx = acc.findIndex(d => d.Id === curr.Id);
        if (idx > -1) {
            // Replace the older draft for this record with the newer one
            acc[idx] = curr;
        } else {
            acc.push(curr);
        }
        return acc;
    }, []);

    console.log('Draft values updated (deduped):', JSON.stringify(this.draftValues));
}

    handleSave() {
        const currentDrafts = this.draftValues;
        if (!currentDrafts || currentDrafts.length === 0) {
            this.showToast('No Changes', 'There are no pending changes to save.', 'info');
            return;
        }

        this.isLoading = true;
        this.error = undefined;

        const recordsToUpdate = [];
        const parsingErrors = [];

        for (const draft of currentDrafts) {
            const recordId = draft.Id;
            const updatedRecord = { Id: recordId };

            for (const fieldName in draft) {
                if (fieldName === 'Id') continue;
                const fieldInfo = this._allAvailableFieldsInfo.find(f => f.value === fieldName);
                if (!fieldInfo) continue;

                const originalValue = draft[fieldName];
                let parsedValue = originalValue;
                const apexType = fieldInfo.type?.toLowerCase();

                if (['currency', 'double', 'percent', 'integer', 'long'].includes(apexType)) {
                        if (originalValue === null || originalValue === '' || typeof originalValue === 'undefined') {
                        parsedValue = null;
                        } else {
                        const num = apexType === 'integer' || apexType === 'long' ? parseInt(originalValue, 10) : parseFloat(originalValue);
                        if (!isNaN(num)) {
                            parsedValue = num;
                        } else {
                            parsingErrors.push(`Invalid numeric format for field '${fieldInfo.label}' (Record ID ending ${recordId?.slice(-4)}): Found '${originalValue}'`);
                            parsedValue = 'PARSING_ERROR_SKIP';
                        }
                        }
                }

                if (parsedValue !== 'PARSING_ERROR_SKIP') {
                    updatedRecord[fieldName] = parsedValue;
                }
            }

            if (Object.keys(updatedRecord).length > 1) {
                    recordsToUpdate.push(updatedRecord);
            }
        }

        if (parsingErrors.length > 0) {
            this.showToast('Data Format Error', `Please correct invalid numeric inputs: ${parsingErrors.join('; ')}`, 'error', 'sticky');
            this.isLoading = false;
            this.error = 'Invalid data format detected.';
            return;
        }

        if (recordsToUpdate.length === 0) {
            this.showToast('No Changes', 'No valid changes detected after processing.', 'info');
            this.isLoading = false;
            return;
        }

        console.log('Sending PARSED data to Apex saveTransactions:', JSON.stringify(recordsToUpdate));

        saveTransactions({ transactionsToUpdate: recordsToUpdate })
            .then(result => {
                this.showToast('Success', result, 'success');
                this.draftValues = [];
                return this.fetchTransactionData();
            })
            .catch(error => {
                    const errorMsg = 'Error saving changes: ' + this.reduceErrors(error).join('; ');
                    this.showToast('Error Saving Records', errorMsg, 'error', 'sticky');
                    this.error = errorMsg;
                    this.isLoading = false;
            })
            .finally(() => {
                if (this.isLoading) {
                    this.isLoading = false;
                }
            });
    }

    get saveDisabled() {
        return !this.draftValues || this.draftValues.length === 0 || this.isLoading;
    }

    openFieldSelector() {
        this._tempSelectedFields = [...this.selectedFieldValues];
        this.isModalOpen = true;
    }
    closeModal() {
        this.isModalOpen = false;
        this._tempSelectedFields = undefined;
    }
    handleFieldSelectionChange(event) {
        this._tempSelectedFields = event.detail.value;
    }
    applyFieldSelection() {
            if (this._tempSelectedFields && this._tempSelectedFields.length > 0) {
            this.selectedFieldValues = [...this._tempSelectedFields];
            this.buildColumns();
            console.log('this.columns after field selection: ', JSON.stringify(this.columns));
            this.paginateData();
        } else {
                this.showToast('Selection Required', 'Please select at least one field.', 'warning');
            return;
            }
        this.isModalOpen = false;
        this._tempSelectedFields = undefined;
    }

    paginateData() {
        if (!this._allData) { this.pagedData = []; this.updatePaginationInfo(); return; }
        const pageNumber = this.pagination.currentPage;
        const pageSize = this.pagination.pageSize;
        this.pagination.totalRecords = this._allData.length;
        let startIndex, endIndex;
        if (pageSize >= this.pagination.totalRecords) {
            startIndex = 0;
            endIndex = this.pagination.totalRecords;
            this.pagedData = [...this._allData];
        } else {
            startIndex = (pageNumber - 1) * pageSize;
            endIndex = Math.min(startIndex + pageSize, this.pagination.totalRecords);
            this.pagedData = this._allData.slice(startIndex, endIndex);
        }
        this.updatePaginationInfo(startIndex, endIndex);
    }

    updatePaginationInfo(startIndex = 0, endIndex = 0) {
        this.pagination.totalPages = Math.ceil(this.pagination.totalRecords / this.pagination.pageSize);
            if (this.pagination.totalPages === 0) this.pagination.totalPages = 1;
            if (this.pagination.currentPage > this.pagination.totalPages) this.pagination.currentPage = this.pagination.totalPages;
            if (this.pagination.currentPage < 1) this.pagination.currentPage = 1;

        this.pagination.firstRecordNumber = this.pagination.totalRecords > 0 ? startIndex + 1 : 0;
        this.pagination.lastRecordNumber = endIndex;
    }

    handlePageSizeChange(event) { this.pagination.pageSize = parseInt(event.detail.value, 10); this.pagination.currentPage = 1; this.paginateData(); }
    handlePreviousPage() { if (this.pagination.currentPage > 1) { this.pagination.currentPage--; this.paginateData(); } }
    handleNextPage() { if (this.pagination.currentPage < this.pagination.totalPages) { this.pagination.currentPage++; this.paginateData(); } }
    get pagedData() { return this._pagedDataCache || []; }
    set pagedData(value) { this._pagedDataCache = value; }
    get previousDisabled() { return this.pagination.currentPage <= 1 || this.isLoading; }
    get nextDisabled() { return this.pagination.currentPage >= this.pagination.totalPages || this.isLoading; }
    get hasTransactions() { return this._allData && this._allData.length > 0; }


    handleSort(event) {
        const { fieldName: sortedBy, sortDirection } = event.detail;
        this.sortBy = sortedBy;
        this.sortDirection = sortDirection;
        this.sortData(sortedBy, sortDirection);
        this.pagination.currentPage = 1;
        this.paginateData();
    }

    sortData(fieldName, direction) {
        if (!this._allData || this._allData.length === 0 || !fieldName) {
            console.warn('SortData: No data or fieldName to sort by.');
            return; 
        }

        let parseData = [...this._allData];
        let keyValue = (a) => {
            return a[fieldName];
        };

        let isReverse = direction === 'asc' ? 1 : -1;

        parseData.sort((x, y) => {
            let valueX = keyValue(x) ? keyValue(x) : '';
            let valueY = keyValue(y) ? keyValue(y) : '';

            const columnInfo = this.columns.find(col => col.fieldName === fieldName);
            const dataType = columnInfo ? columnInfo.type : 'text'; 

            let comparison = 0;

            if (dataType === 'number' || dataType === 'currency' || dataType === 'percent') {
                const numX = valueX === '' || valueX === null ? null : Number(valueX);
                const numY = valueY === '' || valueY === null ? null : Number(valueY);

                if (numX === null && numY === null) comparison = 0;
                else if (numX === null) comparison = -1; 
                else if (numY === null) comparison = 1; 
                else comparison = numX - numY;

            }

            else if (dataType === 'date-local' || dataType === 'datetime') {

                const dateX = valueX ? new Date(valueX) : null;
                const dateY = valueY ? new Date(valueY) : null;

                if (dateX === null && dateY === null) comparison = 0;
                else if (dateX === null) comparison = -1;
                else if (dateY === null) comparison = 1;
                else if (dateX < dateY) comparison = -1;
                else if (dateX > dateY) comparison = 1;
                else comparison = 0;
            } else {
                valueX = typeof valueX === 'string' ? valueX.toLowerCase() : valueX;
                valueY = typeof valueY === 'string' ? valueY.toLowerCase() : valueY;
                if (valueX < valueY) comparison = -1;
                else if (valueX > valueY) comparison = 1;
                else comparison = 0;
            }

            return comparison * isReverse;
        });

        this._allData = parseData;
        console.log(`Data sorted by ${fieldName} (${direction})`);
    }

    showToast(title, message, variant, mode = 'dismissible') {
        this.dispatchEvent(new ShowToastEvent({ title, message, variant, mode }));
    }

    reduceErrors(errors) {
        if (!Array.isArray(errors)) errors = [errors];
        return errors.filter(error => !!error).map(error => {
            if (error.body && Array.isArray(error.body) && error.body.length > 0 && error.body[0].message) return error.body[0].message; // Handle AuraHandledException shape
            if (error.body && error.body.message) return error.body.message;
            if (error.message) return error.message;
            if (typeof error === 'string') return error;
            return 'Unknown error';
        }).filter((message, index, self) => self.indexOf(message) === index);
    }
}