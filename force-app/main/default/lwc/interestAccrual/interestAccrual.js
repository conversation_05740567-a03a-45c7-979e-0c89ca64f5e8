/* eslint-disable */

import { LightningElement, api } from "lwc";

export default class InterestAccrual extends LightningElement {
  @api transactions = [];
  @api disbursements = [];
  @api weekColumns = [];

  /** Grab the one and only Project__c from disbursements (if exists) */
  get projectId() {
    return Array.isArray(this.disbursements) && this.disbursements.length
      ? this.disbursements[0].Project__c
      : null;
  }

  /**
   * Given a JS Date, return the Date object for the Friday of that week.
   * If already Friday (getDay() === 5), return it (with time zeroed).
   * Otherwise, move forward to the next Friday.
   */
  getNextFriday(date) {
    const d = new Date(date);
    const dow = d.getDay(); // 0=Sun,1=Mon,…5=Fri,6=Sat
    const offset = (5 - dow + 7) % 7;
    d.setDate(d.getDate() + offset);
    d.setHours(0, 0, 0, 0);
    return d;
  }

  /**
   * Build rows for each week in weekColumns so that:
   *  - accrualSum = sum of Accrued_Interests_to_Date__c for all txns falling on that week
   *  - paymentSum = sum of Interest_Application__c for all txns falling on that week
   *  - currentOutstanding = prevOutstanding + accrualSum - paymentSum
   *  - week = 0 if currentOutstanding === 0, else increment from previous non-zero
   */
  get displayRows() {
    // 1) Filter transactions for this project
    const filteredTxns =
      Array.isArray(this.transactions) && this.projectId
        ? this.transactions.filter((tx) => tx.Project__c === this.projectId)
        : [];

    // 2) Group each transaction directly by its Friday ISO string
    //    so we never call getFullYear() on something undefined.
    const groupsByISO = filteredTxns.reduce((acc, tx) => {
      // a) Compute this tx’s Friday Date
      const created = new Date(tx.CreatedDate);
      const fridayDate = this.getNextFriday(created);

      // b) Turn that Friday into an ISO key "YYYY-MM-DD"
      const yyyy = fridayDate.getFullYear();
      const mm = String(fridayDate.getMonth() + 1).padStart(2, "0");
      const dd = String(fridayDate.getDate()).padStart(2, "0");
      const iso = `${yyyy}-${mm}-${dd}`; // e.g. "2025-06-06"

      // c) Pull out accrual and payment
      const accrual = tx.Accrued_Interests_to_Date__c || 0;
      const payment = tx.Interest_Application__c || 0;

      // d) Initialize or add to the sums for this iso
      if (!acc[iso]) {
        acc[iso] = { accrualSum: 0, paymentSum: 0 };
      }
      acc[iso].accrualSum += accrual;
      acc[iso].paymentSum += payment;

      return acc;
    }, {});

    // 3) Now iterate exactly over weekColumns to produce one row per week
    const rows = [];
    let prevOutstanding = 0;
    

    for (const wkCol of this.weekColumns) {
      // wkCol.date must be an ISO string like "YYYY-MM-DD"
      const isoDate = wkCol.date;
      // If no transactions fell this week, default sums to zero
      const sums = groupsByISO[isoDate] || { accrualSum: 0, paymentSum: 0 };

      // “Beg balance” = previous week’s outstanding
      const begBalance = prevOutstanding;

      // Current outstanding = begBalance + accrualSum - paymentSum
      const currentOutstanding =
        prevOutstanding + sums.accrualSum - sums.paymentSum;

     

      rows.push({
        id: isoDate, // or isoDate + '-' + weekNumber if you need guaranteed uniqueness
        date: new Date(isoDate).toLocaleDateString(),
        begBalance: `$ ${begBalance.toFixed(2)}`,
        accrual: `$ ${sums.accrualSum.toFixed(2)}`,
        payment: `$ ${sums.paymentSum.toFixed(2)}`,
        outstandingInt: `$ ${currentOutstanding.toFixed(2)}`,
      });

      // Carry forward this week’s outstanding for the next iteration
      prevOutstanding = currentOutstanding;
    }

    return rows;
  }
}