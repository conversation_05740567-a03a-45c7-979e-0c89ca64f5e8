<template>
    <div class="slds-theme_default repeater-container">
        <template for:each={_reactiveRequestItems} for:item="itemData" for:index="index"> <div key={itemData.id} class="slds-card item-card slds-m-bottom_medium">
                <div class="slds-card__header slds-grid">
                    <header class="slds-media slds-media_center slds-has-flexi-truncate">
                        <div class="slds-media__body">
                            <h2 class="slds-card__header-title">
                                <span class="slds-text-heading_small item-section-title">{itemSectionTitle}{itemData.itemNumber}</span>
                            </h2>
                        </div>
                        <div class="slds-no-flex">
                            <lightning-button-icon
                                icon-name="utility:delete"
                                alternative-text="Remove Item"
                                title="Remove Item"
                                onclick={handleRemoveItem}
                                data-id={itemData.id}
                                disabled={isRemoveDisabled}
                                class="slds-m-left_small remove-item-button"
                                if:true={showRemoveButton}
                            ></lightning-button-icon>
                        </div>
                    </header>
                </div>

                <div class="slds-card__body slds-card__body_inner">
                    <lightning-layout multiple-rows="true" vertical-align="end">
                        <lightning-layout-item size="12" small-device-size="12" medium-device-size="12" large-device-size="12" padding="around-small">
                            <lightning-input
                                type="text"
                                label="Item"
                                name="Name" 
                                value={itemData.Name}
                                onchange={handleInputChange}
                                data-id={itemData.id}
                                required
                                class="custom-input"
                                message-when-value-missing="Item name is required."
                            ></lightning-input>
                        </lightning-layout-item>
                        <lightning-layout-item size="12" small-device-size="12" medium-device-size="12" large-device-size="12" padding="around-small">
                            <lightning-input
                                type="text"
                                label="Description/Work"
                                name="descriptionWork" 
                                value={itemData.descriptionWork}
                                onchange={handleInputChange}
                                data-id={itemData.id}
                                required
                                class="custom-input"
                                message-when-value-missing="Description/Work is required."
                            ></lightning-input>
                        </lightning-layout-item>
                        <lightning-layout-item size="12" small-device-size="12" medium-device-size="6" large-device-size="6" padding="around-small">
                            <lightning-input
                                type="text"
                                label="Invoice #"
                                name="invoice" 
                                value={itemData.invoice}
                                onchange={handleInputChange}
                                data-id={itemData.id}
                                required
                                class="custom-input"
                                message-when-value-missing="Invoice # is required."
                            ></lightning-input>
                        </lightning-layout-item>
                        <lightning-layout-item size="12" small-device-size="12" medium-device-size="6" large-device-size="6" padding="around-small">
                            <lightning-input
                                type="number"
                                label="Invoice Amount"
                                name="invoiceAmount" 
                                value={itemData.invoiceAmount}
                                formatter="currency"
                                step="0.01"
                                onchange={handleInputChange}
                                data-id={itemData.id}
                                required
                                class="custom-input"
                                message-when-value-missing="Invoice Amount is required."
                            ></lightning-input>
                        </lightning-layout-item>
                        
                        <lightning-layout-item size="12" small-device-size="12" medium-device-size="6" large-device-size="6" padding="around-small">
                            <lightning-input
                                type="date"
                                label="Invoice Date"
                                name="invoiceDate" 
                                value={itemData.invoiceDate}
                                onchange={handleInputChange}
                                data-id={itemData.id}
                                required
                                class="custom-input"
                                message-when-value-missing="Invoice Date is required."
                            ></lightning-input>
                        </lightning-layout-item>
                        <lightning-layout-item size="12" small-device-size="12" medium-device-size="6" large-device-size="6" padding="around-small">
                            <lightning-input
                                type="date"
                                label="Invoice Due Date"
                                name="invoiceDueDate" 
                                value={itemData.invoiceDueDate}
                                onchange={handleInputChange}
                                data-id={itemData.id}
                                required
                                class="custom-input"
                                message-when-value-missing="Invoice Due Date is required."
                            ></lightning-input>
                        </lightning-layout-item>
                    </lightning-layout>

                    <!-- <div class="slds-m-top_medium file-upload-section">
                        <h3 class="slds-text-heading_small file-upload-title slds-m-bottom_small" if:false={removeFileUploadTitle}>{fileUploadSectionTitle}</h3>
                        <lightning-file-upload
                            label={fileUploadLabel}
                            name="fileUploader"
                            accept={acceptedFileFormatsArray}
                            onuploadfinished={handleUploadFinished}
                            data-id={itemData.id} 
                            multiple
                        ></lightning-file-upload>
                         <div class="slds-m-top_small uploaded-files-container" if:true={itemData.uploadedFileNames.length}> 
                            <p class="slds-text-body_small">Uploaded files for this item:</p>
                            <ul class="slds-list_dotted">
                                <template for:each={itemData.uploadedFileNames} for:item="fileNameInfo" for:idx="fileIdx"> 
                                    <li key={fileNameInfo.documentId} class="slds-p-vertical_xx-small file-name-item">
                                        <span>{fileNameInfo.name}</span>
                                        <lightning-button-icon
                                            icon-name="utility:delete"
                                            alternative-text="Remove File"
                                            title="Remove File"
                                            variant="bare"
                                            onclick={handleRemoveUploadedFile}
                                            data-itemid={itemData.id} 
                                            data-documentid={fileNameInfo.documentId}
                                            class="slds-m-left_small remove-file-button"
                                        ></lightning-button-icon>
                                    </li>
                                </template>
                            </ul>
                        </div>
                    </div> -->
                </div>
            </div>
        </template>

        <div class="slds-m-top_medium slds-text-align_right">
            <lightning-button
                label="Add Item"
                title="Add New Item"
                variant="neutral"
                icon-name="utility:add"
                onclick={handleAddItem}
                class="add-item-button"
            ></lightning-button>
        </div>
        <div if:true={errorMessages.length} class="slds-m-top_medium">
            <template for:each={errorMessages} for:item="error"> 
                <div key={error} class="slds-text-color_error slds-m-bottom_xx-small">{error}</div>
            </template>
        </div>
    </div>
</template>