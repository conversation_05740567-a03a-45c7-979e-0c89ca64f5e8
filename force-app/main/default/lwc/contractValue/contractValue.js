/* eslint-disable */
import { LightningElement, api } from "lwc";

export default class ContractValue extends LightningElement {
  @api transactions = [];
  @api payApplications = [];
  @api projectData = [];
  @api weekColumns = [];

  connectedCallback() {
    console.log(
      "API – payApplications:",
      JSON.stringify(this.payApplications)
    );
    console.log("API – transactions:", JSON.stringify(this.transactions));
    console.log("API – projectData:", JSON.stringify(this.projectData));
    console.log("weekColumns:  ", JSON.stringify(this.weekColumns));
  }

  /**
   * Given a JS Date, return the Date object corresponding to that week’s Friday.
   * If the date is already a Friday (getDay() === 5), return it (with hours zeroed).
   * Otherwise, move forward to the next Friday.
   */
  getNextFriday(date) {
    const d = new Date(date);
    const dow = d.getDay(); // 0=Sun,1=Mon,…,5=Fri,6=Sat
    const offset = (5 - dow + 7) % 7;
    d.setDate(d.getDate() + offset);
    d.setHours(0, 0, 0, 0);
    return d;
  }

  /**
   * Helper: format an ISO datetime string into MM/DD/YYYY
   */
  formatDate(isoString) {
    if (!isoString) return "";
    const dt = new Date(isoString);
    const year = dt.getFullYear();
    const month = `0${dt.getMonth() + 1}`.slice(-2);
    const day = `0${dt.getDate()}`.slice(-2);
    return `${month}/${day}/${year}`;
  }

  /**
   * Build one row per weekColumns entry so that:
   * 1) We start with an initial “beg” = Remaining_Contract_Value__c * (1 – retainageFrac).
   * 2) For each transaction:
   *      • Compute grossPayApp = projectedPayment * (1 – retainageFrac)
   *      • Compute this transaction’s “Friday ISO” = YYYY-MM-DD via getNextFriday.
   *      • Skip any tx where CreatedDate is invalid (i.e., Date(...) yields NaN).
   *    Then group directly in one reduce into { [isoDate]: grossSum }.
   * 3) Finally, loop in order over this.weekColumns:
   *      • If a given week’s ISO string isn’t in the map, treat its grossSum = 0.
   *      • Carry forward ending = beg – payApp. For the first week, beg = initialBeg.
   *      • “week” column is index + 1 so that you get exactly as many rows as weekColumns.
   */
  get displayRows() {
    // 1) Normalize inputs to arrays
    const payApps = Array.isArray(this.payApplications)
      ? this.payApplications
      : this.payApplications
      ? [this.payApplications]
      : [];

    const txns = Array.isArray(this.transactions)
      ? this.transactions
      : this.transactions
      ? [this.transactions]
      : [];

    const projArr = Array.isArray(this.projectData)
      ? this.projectData
      : this.projectData
      ? [this.projectData]
      : [];

    // 2) Grab the one project record (if it exists), else default to zeros
    const project = projArr.length > 0 ? projArr[0] : {};
    const rawRemain =
      typeof project.Remaining_Contract_Value__c === "number"
        ? project.Remaining_Contract_Value__c
        : 0;
    const rawRetainage =
      typeof project.Retainage__c === "number" ? project.Retainage__c : 0;
    const retainageFrac = rawRetainage / 100; // e.g. 5% → 0.05

    // 3) Compute initial “beg” before any transactions
    const initialBeg = rawRemain * (1 - retainageFrac);

    // 4) Group all transactions by the Friday ISO string in one step
    //    – If a transaction’s CreatedDate is invalid, skip it.
    //    – Each tx’s grossPayApp = projectedPayment * (1 – retainageFrac).
    //    – We accumulate grossSum by ISO key: "YYYY-MM-DD".
    const groupsByISO = txns.reduce((acc, tx) => {
      // a) Find the related PayApplication record to get projectedPayment
      const payAppId = tx.Related_Pay_Application__c;
      let projectedPayment = 0;
      if (payAppId) {
        const pa = payApps.find((p) => p.Id === payAppId);
        if (pa && typeof pa.Projected_payment__c === "number") {
          projectedPayment = pa.Projected_payment__c;
        }
      }

      // b) Compute this transaction’s grossPayApp
      const grossPayApp = projectedPayment * (1 - retainageFrac);

      // c) Determine the Friday date for this transaction’s CreatedDate
      const created = new Date(tx.CreatedDate);
      if (isNaN(created.getTime())) {
        // skip any txn with invalid CreatedDate
        return acc;
      }
      const weekFri = this.getNextFriday(created);
      if (isNaN(weekFri.getTime())) {
        // in the rare case getNextFriday() yields an invalid Date
        return acc;
      }

      // d) Turn that Friday into an ISO string "YYYY-MM-DD"
      const yyyy = weekFri.getFullYear();
      const mm = String(weekFri.getMonth() + 1).padStart(2, "0");
      const dd = String(weekFri.getDate()).padStart(2, "0");
      const iso = `${yyyy}-${mm}-${dd}`;

      // e) Accumulate into acc[iso].grossSum
      if (!acc[iso]) {
        acc[iso] = { grossSum: 0 };
      }
      acc[iso].grossSum += grossPayApp;
      return acc;
    }, {});

    // 5) Iterate exactly over weekColumns to produce one row per entry
    const rows = [];
    let prevEnding = initialBeg;

    this.weekColumns.forEach((wkCol, idx) => {
      // wkCol.date must be an ISO string like "YYYY-MM-DD"
      const isoDate = wkCol.date;
      const sums = groupsByISO[isoDate] || { grossSum: 0 };

      // a) “beg” is whatever the previous ending was (initialBeg for the very first)
      const beg = prevEnding;

      // b) “payApp” for this week = grossSum (or 0 if no transactions)
      const payAppSum = sums.grossSum;

      // c) “ending” = beg − payAppSum
      const ending = beg - payAppSum;

      rows.push({
        id: isoDate, // or isoDate + "-" + (idx+1) if you need a unique key
        date: new Date(isoDate).toLocaleDateString(), // e.g. "06/06/2025"
        week: idx + 1, // first element in weekColumns → week=1, second → week=2, etc.
        beg: `$ ${beg.toFixed(2)}`,
        payApp: `$ ${payAppSum.toFixed(2)}`,
        ending: `$ ${ending.toFixed(2)}`,
      });

      prevEnding = ending;
    });

    return rows;
  }
}