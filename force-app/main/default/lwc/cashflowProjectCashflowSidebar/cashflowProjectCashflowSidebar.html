<template>
    <div class="sidebar-container">
        <!-- Toggle Button (unchanged) -->
        <button
            class={toggleClass}
            title={toggleTitle}
            onclick={handleToggle}
            aria-label={toggleTitle}
        >
            <lightning-icon
                icon-name={currentIcon}
                size="small"
                alternative-text={toggleTitle}
            ></lightning-icon>
        </button>

        <!-- Sliding Panel (unchanged) -->
        <div class={panelClass}>
            <header class="panel-header">
                <h2 class="slds-text-heading_medium">Floating Panel</h2>
            </header>

            <div class="panel-body">
                <!-- Editable Start Date -->
                <lightning-input
                    type="date"
                    name="startDate"
                    label="Start Date"
                    value={startDate}
                    onchange={handleStartDateChange}
                ></lightning-input>
                <!-- <template if:true={startDateFormatted}>
                    <lightning-formatted-text 
                        class="slds-m-vertical_xx-small slds-text-body_small" 
                        value={startLabel}>
                    </lightning-formatted-text>
                </template> -->

                <!-- Editable End Date -->
                <lightning-input
                    type="date"
                    name="endDate"
                    label="End Date"
                    value={endDate}
                    onchange={handleEndDateChange}
                ></lightning-input>
                <!-- <template if:true={endDateFormatted}>
                    <lightning-formatted-text 
                        class="slds-m-vertical_xx-small slds-text-body_small" 
                        value={endLabel}>
                    </lightning-formatted-text>
                </template> -->

                <!-- ==== NEW FIELDS IN A STACKED “TWO-LINE” LAYOUT ==== -->
                <div class="slds-m-top_medium">
                    <!-- Cash Flow Verification -->
                    <div class="slds-p-vertical_x-small slds-border_bottom">
                        <span class="slds-text-title_bold slds-truncate">Cash Flow Verification</span><br/>
                        <span class="slds-text-body_regular slds-truncate">{cashFlowVerification}</span>
                    </div>

                    <!-- Cost Check -->
                    <div class="slds-p-vertical_x-small slds-border_bottom">
                        <span class="slds-text-title_bold slds-truncate">Cost Check</span><br/>
                        <span class="slds-text-body_regular slds-truncate">{costCheck}</span>
                    </div>

                    <!-- Loan Paid Off in Model -->
                    <div class="slds-p-vertical_x-small slds-border_bottom">
                        <span class="slds-text-title_bold slds-truncate">Loan Paid Off in Model</span><br/>
                        <span class="slds-text-body_regular slds-truncate">{loanPaidOffInModel}</span>
                    </div>

                    <!-- Invoices Balance -->
                    <div class="slds-p-vertical_x-small">
                        <span class="slds-text-title_bold slds-truncate">Invoices Balance</span><br/>
                        <span class="slds-text-body_regular slds-truncate">{invoicesBalance}</span>
                    </div>
                </div>
                <!-- ==== END OF NEW STACKED FIELDS ==== -->

                     <!-- ==== ADDITIONAL TOTALS ==== -->
                <div class="slds-m-top_medium">
                <!-- Total Pay Application -->
                <div class="slds-p-vertical_x-small slds-border_bottom">
                    <span class="slds-text-title_bold slds-truncate">Total Pay Application</span><br/>
                    <span class="slds-text-body_regular slds-truncate">{totalPayApp}</span>
                </div>

                <!-- Total Less Retainage -->
                <div class="slds-p-vertical_x-small slds-border_bottom">
                    <span class="slds-text-title_bold slds-truncate">Total Less Retainage</span><br/>
                    <span class="slds-text-body_regular slds-truncate">{totalLessRetainage}</span>
                </div>

                <!-- Total Projected Net Pay App -->
                <div class="slds-p-vertical_x-small slds-border_bottom">
                    <span class="slds-text-title_bold slds-truncate">Total Projected Net Pay App</span><br/>
                    <span class="slds-text-body_regular slds-truncate">{totalProjectedNetPayApp}</span>
                </div>

                <!-- Total Direct Payroll -->
                <div class="slds-p-vertical_x-small slds-border_bottom">
                    <span class="slds-text-title_bold slds-truncate">Total Direct Payroll</span><br/>
                    <span class="slds-text-body_regular slds-truncate">{totalDirectPayroll}</span>
                </div>

                <!-- Total Subcontract Labor -->
                <div class="slds-p-vertical_x-small slds-border_bottom">
                    <span class="slds-text-title_bold slds-truncate">Total Subcontract Labor</span><br/>
                    <span class="slds-text-body_regular slds-truncate">{totalSubcontractLabor}</span>
                </div>

                <!-- Total Material -->
                <div class="slds-p-vertical_x-small slds-border_bottom">
                    <span class="slds-text-title_bold slds-truncate">Total Material</span><br/>
                    <span class="slds-text-body_regular slds-truncate">{totalMaterial}</span>
                </div>

                <!-- Total Equipment Rental -->
                <div class="slds-p-vertical_x-small slds-border_bottom">
                    <span class="slds-text-title_bold slds-truncate">Total Equipment Rental</span><br/>
                    <span class="slds-text-body_regular slds-truncate">{totalEquipmentRental}</span>
                </div>

                <!-- Total Bond Premium -->
                <div class="slds-p-vertical_x-small slds-border_bottom">
                    <span class="slds-text-title_bold slds-truncate">Total Bond Premium</span><br/>
                    <span class="slds-text-body_regular slds-truncate">{totalBondPremium}</span>
                </div>

                <!-- Total Misc. Expenses -->
                <div class="slds-p-vertical_x-small slds-border_bottom">
                    <span class="slds-text-title_bold slds-truncate">Total Misc. Expenses</span><br/>
                    <span class="slds-text-body_regular slds-truncate">{totalMiscExpenses}</span>
                </div>

                <!-- Total Project Cost -->
                <div class="slds-p-vertical_x-small">
                    <span class="slds-text-title_bold slds-truncate">Total Project Cost</span><br/>
                    <span class="slds-text-body_regular slds-truncate">{totalProjectCost}</span>
                </div>
                </div>
    <!-- ==== END ADDITIONAL TOTALS ==== -->



                <!-- 1) Type of Loan (picklist) -->
                <lightning-combobox
                    name="loanType"
                    label="Type of Loan"
                    value={loanType}
                    placeholder="-- Select Loan Type --"
                    options={loanTypeOptions}
                    onchange={handleLoanTypeChange}
                ></lightning-combobox>

                <!-- 2) Days for Pay App to be Paid (picklist) -->
                <lightning-combobox
                    name="payAppDays"
                    label="Days for Pay App to be Paid"
                    value={payAppDays}
                    placeholder="-- Select Days --"
                    options={payAppDaysOptions}
                    onchange={handlePayAppDaysChange}
                ></lightning-combobox>

                <!-- 3) Gross Contract Value -->
                <div class="slds-p-top_small">
                    <label class="slds-form-element__label" for="grossContractValue">
                        Gross Contract Value
                    </label>
                    <div class="slds-form-element__control">
                        <lightning-formatted-number
                            value={grossContractValue}
                            format-style="currency"
                            currency-code="USD"
                            class="slds-text-body_regular"
                        ></lightning-formatted-number>
                    </div>
                </div>

                <!-- 4) Less: PAID to DATE -->
                <div class="slds-p-top_small">
                    <label class="slds-form-element__label" for="paidToDate">
                        Less: PAID to DATE
                    </label>
                    <div class="slds-form-element__control">
                        <lightning-formatted-number
                            value={paidToDate}
                            format-style="currency"
                            currency-code="USD"
                            class="slds-text-body_regular"
                        ></lightning-formatted-number>
                    </div>
                </div>

                <!-- 5) Plus: Change Orders -->
                <div class="slds-p-top_small">
                    <label class="slds-form-element__label" for="changeOrders">
                        Plus: Change Orders
                    </label>
                    <div class="slds-form-element__control">
                        <lightning-formatted-number
                            value={changeOrders}
                            format-style="currency"
                            currency-code="USD"
                            class="slds-text-body_regular"
                        ></lightning-formatted-number>
                    </div>
                </div>

                <!-- 6) Remaining Gross Contract Value -->
                <div class="slds-p-top_small">
                    <label class="slds-form-element__label" for="remainingGrossContractValue">
                        Remaining Gross Contract Value
                    </label>
                    <div class="slds-form-element__control">
                        <lightning-formatted-number
                            value={remainingGrossContractValue}
                            format-style="currency"
                            currency-code="USD"
                            class="slds-text-body_regular"
                        ></lightning-formatted-number>
                    </div>
                </div>

                <!-- 7) Retainage Percentage -->
                <div class="slds-p-top_small">
                    <label class="slds-form-element__label" for="retainagePercentage">
                        Retainage Percentage
                    </label>
                    <div class="slds-form-element__control">
                        <lightning-formatted-number
                            value={retainagePercentage}
                            format-style="percent"
                            maximum-fraction-digits="0"
                            class="slds-text-body_regular"
                        ></lightning-formatted-number>
                    </div>
                </div>

                <!-- 8) Less: Retainage on Gross Contract -->
                <div class="slds-p-top_small">
                    <label class="slds-form-element__label" for="retainageOnGrossContract">
                        Less: Retainage on Gross Contract
                    </label>
                    <div class="slds-form-element__control">
                        <lightning-formatted-number
                            value={retainageOnGrossContract}
                            format-style="currency"
                            currency-code="USD"
                            class="slds-text-body_regular"
                        ></lightning-formatted-number>
                    </div>
                </div>

                <!-- 9) Net Remaining Contract Value -->
                <div class="slds-p-top_small">
                    <label class="slds-form-element__label" for="netRemainingContractValue">
                        Net Remaining Contract Value
                    </label>
                    <div class="slds-form-element__control">
                        <lightning-formatted-number
                            value={netRemainingContractValue}
                            format-style="currency"
                            currency-code="USD"
                            class="slds-text-body_regular"
                        ></lightning-formatted-number>
                    </div>
                </div>

                <!-- 10) Maximum Loan To Value (LTV) Allowed -->
                <div class="slds-p-top_small">
                    <label class="slds-form-element__label" for="maxLtvAllowed">
                        Maximum Loan To Value (LTV) Allowed
                    </label>
                    <div class="slds-form-element__control">
                        <lightning-formatted-number
                            value={maxLtvAllowed}
                            format-style="percent"
                            maximum-fraction-digits="0"
                            class="slds-text-body_regular"
                        ></lightning-formatted-number>
                    </div>
                </div>

                <!-- 11) Suggested Maximum Loan Amount -->
                <div class="slds-p-top_small">
                    <label class="slds-form-element__label" for="suggestedMaxLoanAmount">
                        Suggested Maximum Loan Amount
                    </label>
                    <div class="slds-form-element__control">
                        <lightning-formatted-number
                            value={suggestedMaxLoanAmount}
                            format-style="currency"
                            currency-code="USD"
                            class="slds-text-body_regular"
                        ></lightning-formatted-number>
                    </div>
                </div>

                <!-- 12) Loan Principal Amount (editable by user) -->
                <div class="slds-p-top_small">
                    <label class="slds-form-element__label" for="loanPrincipalAmount">
                        Loan Principal Amount
                    </label>
                    <div class="slds-form-element__control">
                        <lightning-formatted-number
                            value={loanPrincipalAmount}
                            format-style="currency"
                            currency-code="USD"
                            class="slds-text-body_regular"
                        ></lightning-formatted-number>
                    </div>
                </div>

                

            </div>
        </div>
    </div>
</template>