/* eslint-disable */

import { api, LightningElement, track } from 'lwc';

export default class CashflowProjectCashflowSidebar extends LightningElement {

     
   @api projectData = [];
   @api weekColumns = [];
   @api activeCashflow = [];
   @api cashflowData = [];
   @track filteredWeekColumns = [];
   @track originalWeekColumns = [];

    
   @track startDate = '';
   @track endDate = '';

   connectedCallback(){
        console.log('projects ', JSON.stringify(this.projectData));
        console.log('weekColumns ', JSON.stringify(this.weekColumns));
        console.log('cashFlowData in sidebar ', JSON.stringify(this.cashflowData));
        console.log('activeCashflow on sidebar', JSON.stringify(this.activeCashflow));

        this.filteredWeekColumns = [...this.weekColumns];
        this.originalWeekColumns =  [...this.weekColumns];
        if (this.filteredWeekColumns.length) {
            this.startDate = this.filteredWeekColumns[0].date;
            this.endDate  = this.filteredWeekColumns[this.filteredWeekColumns.length - 1].date;
            console.log('initial start date 0 ',this.startDate);
            console.log('initial end date 0',this.endDate);
            this.filterWeekColumns();

            // console.log('First week ➞ date:', firstDate.date, ', formattedDate:', firstDate.formattedDate);
            // console.log('Last  week ➞ date:', lastDate.date,  ', formattedDate:', lastDate.formattedDate);

            // this.startDate = firstDate.date;
            // this.endDate   = lastDate.date;

            
        }

            console.log('initial start date ',this.startDate);
            console.log('initial end date ',this.endDate);
    }

    filterWeekColumns() {
        // If either date is missing, show the full range
        if (!this.startDate || !this.endDate) {
            this.filteredWeekColumns = [];
            console.log('originalWeekColumns weekColumns (full range):', JSON.stringify(this.originalWeekColumns));
            this.weekColumns =  [...this.originalWeekColumns];
            this.filteredWeekColumns = this.weekColumns;
            console.log('Filtered weekColumns (full range):', JSON.stringify(this.filteredWeekColumns));
            return;
        }

        const startTs = new Date(this.startDate).getTime();
        const endTs   = new Date(this.endDate).getTime();
         this.weekColumns =  [...this.originalWeekColumns];
        console.log('weekcolumns'+JSON.stringify(this.weekColumns));
        
        this.filteredWeekColumns = this.weekColumns.filter(col => {
            const wkTs = new Date(col.date).getTime();
            return wkTs >= startTs && wkTs <= endTs;
        });

        console.log('Filtered weekColumns (between', this.startDate, 'and', this.endDate, '):', JSON.stringify(this.filteredWeekColumns));


        this.dispatchEvent(new CustomEvent('weekcolumnschange', {
            detail: { weekColumns: this.filteredWeekColumns },
            bubbles: true,
            composed: true
        }));
    }



    
    
    handleStartDateChange(event) {
        const inputCmp = event.target;
        console.log('input date ',inputCmp.value);
        const chosenValue = inputCmp.value; // e.g. "2025-06-03"
        if (!chosenValue) {
            this.startDate = '';
            this.filterWeekColumns();
            return;
        }

        // 1) Turn into a Date object
        const dt = new Date(chosenValue);

        // 2) Compute how many days until the next Friday (getDay() === 5).
        //    Sunday=0, Monday=1, … Friday=5, Saturday=6.
        const todayIndex = dt.getDay();
        // If todayIndex <= 5 (Fri), offset = (5 - todayIndex). Else (Sat), offset = 7 - (todayIndex - 5).
        const offsetDays = (todayIndex <= 5)
            ? (5 - todayIndex)
            : (7 - todayIndex + 5);

        // 3) Add that many days to dt, to land on a Friday
        const nextFriday = new Date(dt.getTime() + offsetDays * 86400000);
        console.log('unformatted friday date ',nextFriday);

        // 4) Format YYYY-MM-DD
        const yyyy = nextFriday.getFullYear();
        const mm = String(nextFriday.getMonth() + 1).padStart(2, '0');
        const dd = String(nextFriday.getDate()).padStart(2, '0');
        const fridayStr = `${yyyy}-${mm}-${dd}`;
        console.log('fiday date ',fridayStr);

        // 5) Update both the tracked property and the UI element’s value
        this.startDate = fridayStr;
        inputCmp.value = fridayStr;
        this.filterWeekColumns();
    }

    handleEndDateChange(event) {
        const inputCmp = event.target;
        const chosenValue = inputCmp.value;
        if (!chosenValue) {
            this.endDate = '';
            this.filterWeekColumns();
            return;
        }

        const dt = new Date(chosenValue);
        const todayIndex = dt.getDay();
        const offsetDays = (todayIndex <= 5)
            ? (5 - todayIndex)
            : (7 - todayIndex + 5);
        const nextFriday = new Date(dt.getTime() + offsetDays * 86400000);

        const yyyy = nextFriday.getFullYear();
        const mm = String(nextFriday.getMonth() + 1).padStart(2, '0');
        const dd = String(nextFriday.getDate()).padStart(2, '0');
        const fridayStr = `${yyyy}-${mm}-${dd}`;

        this.endDate = fridayStr;
        inputCmp.value = fridayStr;
        this.filterWeekColumns();
    }


    // 1) Cash Flow Verification (empty string)
    @track cashFlowVerification = '';

    // 2) Cost Check (“MISSING COSTS”)
    @track costCheck = 'MISSING COSTS';

    // 3) Loan Paid Off in Model (“NO”)
    @track loanPaidOffInModel = 'NO';

    // 4) Invoices Balance (“GROSS Contract TIES OUT”)
    @track invoicesBalance = 'GROSS Contract TIES OUT';
    
    @track loanType = 'Mobilization'; // default
    get loanTypeOptions() {
        return [
            { label: 'Mobilization', value: 'Mobilization' },
            { label: 'P.O.', value: 'P.O.' },
            { label: 'AR Facility', value: 'AR Facility' },
            { label: 'Protected Advance - Invoice', value: 'Protected Advance - Invoice' }
        ];
    }
    handleLoanTypeChange(event) {
        this.loanType = event.detail.value;
    }

  
    @track payAppDays = '35'; // default as string
    get payAppDaysOptions() {
        const opts = [];
        for (let i = 0; i <= 91; i += 7) {

            opts.push({ label: `${i}`, value: `${i}` });
        }
        return opts;
    }
    handlePayAppDaysChange(event) {
        this.payAppDays = event.detail.value;
    }

   

    
    get remainingGrossContractValue() {
        // If projectData or field is undefined, default to 0
        return this.projectData.Remaining_Contract_Value__c || 0;
    }

    
    get retainagePercentage() {
        const raw = this.projectData.Retainage__c;
        return raw != null ? raw / 100 : 0;
    }

    
    get retainageOnGrossContract() {
        return this.remainingGrossContractValue * this.retainagePercentage;
    }

    
    get netRemainingContractValue() {
        return this.remainingGrossContractValue - this.retainageOnGrossContract;
    }

   
   

    get maxLtvAllowed(){
        const grossLtv = this.activeCashflow.Gross_LTV__c;
        
        
        // assume Gross_LTV__c is stored as a percent (e.g. 20 for 20%)
        return grossLtv / 100;
    }

    
    get suggestedMaxLoanAmount() {
        return Math.round(this.netRemainingContractValue * this.maxLtvAllowed);
    }

    
    // @track loanPrincipalAmount = 0;
    // handleLoanPrincipalChange(event) {
    //     this.loanPrincipalAmount = parseFloat(event.detail.value) || 0;
    // }

    get loanPrincipalAmount(){
        return this.projectData.Loan_Principal__c || 0;
    }

    
    @track isOpen = false;
    get panelClass() {
        return this.isOpen ? 'panel open' : 'panel';
    }
    get toggleClass() {
        return this.isOpen ? 'toggle-button open' : 'toggle-button';
    }
    get toggleTitle() {
        return this.isOpen ? 'Close Filters' : 'Open Filters';
    }
    get currentIcon() {
        // When open: chevronright (to close). When closed: chevronleft (to open).
        return this.isOpen ? 'utility:chevronright' : 'utility:chevronleft';
    }
    handleToggle() {
        this.isOpen = !this.isOpen;
    }


    sumCashflow = (rowId) =>{
    const row = this.cashflowData.find(r => r.id === rowId);
    return row
        ? row.weeks.reduce((sum, w) => sum + (w.value || 0), 0)
        : 0;
    }

    formatUSD = (amount)=> {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            maximumFractionDigits: 0
        }).format(amount);
    }

    get totalPayApp() {
        return this.formatUSD(this.sumCashflow('PayAppSubmitted'));
    }

    get totalLessRetainage() {
        return this.formatUSD(this.sumCashflow('LessRetainage'));
    }

    get totalProjectedNetPayApp() {
        return this.formatUSD(this.sumCashflow('ProjectedNetPayApp'));
    }

    get totalDirectPayroll() {
        return this.formatUSD(this.sumCashflow('expense-DirectPayroll'));
    }

    get totalSubcontractLabor() {
        return this.formatUSD(this.sumCashflow('expense-SubcontractorLabor'));
    }

    get totalMaterial() {
        return this.formatUSD(this.sumCashflow('expense-Material'));
    }

    get totalEquipmentRental() {
        return this.formatUSD(this.sumCashflow('expense-EquipmentRental'));
    }

    get totalBondPremium() {
        return this.formatUSD(this.sumCashflow('expense-BondPremium'));
    }

    get totalMiscExpenses() {
        return this.formatUSD(this.sumCashflow('expense-MiscellaneousExpense'));
    }

    get totalProjectCost() {
        return this.formatUSD(this.sumCashflow('TotalProjectCost'));
    }
}