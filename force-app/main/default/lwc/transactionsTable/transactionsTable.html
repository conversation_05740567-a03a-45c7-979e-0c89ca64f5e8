<template>
    <div class="transactions-table">
        <h2>Transactions</h2>
        <table>
            <thead>
            <tr>
                <th>Transaction Date</th>
                <th>Transaction Type</th>
                <th>Transaction Amount</th>
                <th>Principal Balance</th>
                <th>Accrued Interests to Date</th>
            </tr>
            </thead>
            <tbody>
            <template for:each={transactions} for:item="transaction">
                <tr key={transaction.id} class={transaction.rowClass}>
                    <td>{transaction.transactionDate}</td>
                    <td>
                        <div><b>{transaction.type}</b></div>
                        <template if:true={transaction.breakdown}>
                            <template if:true={transaction.breakdown.DefaultFee}>
                                <div>DEFAULT FEE: ${transaction.breakdown.DefaultFee}</div>
                            </template>
                            <template if:true={transaction.breakdown.LateFee}>
                                <div>LATE FEE: ${transaction.breakdown.LateFee}</div>
                            </template>
                            <template if:true={transaction.breakdown.Interest}>
                                <div>INTEREST: ${transaction.breakdown.Interest}</div>
                            </template>
                            <template if:true={transaction.breakdown.Principal}>
                                <div>PRINCIPAL: ${transaction.breakdown.Principal}</div>
                            </template>
                            <template if:true={transaction.breakdown.ClosingCosts}>
                                <div>CLOSING COSTS: ${transaction.breakdown.ClosingCosts}</div>
                            </template>
                            <template if:true={transaction.breakdown.LegalFees}>
                                <div>LEGAL FEES: ${transaction.breakdown.LegalFees}</div>
                            </template>
                            <template if:true={transaction.breakdown.DocStampFees}>
                                <div>DOC STAMP FEES: ${transaction.breakdown.DocStampFees}</div>
                            </template>
                        </template>
                    </td>
                    <td><span if:true={transaction.amount}>${transaction.amount}</span></td>
                    <td><span if:true={transaction.principalBalance}>${transaction.principalBalance}</span></td>
                    <td><span if:true={transaction.accruedInterests}>${transaction.accruedInterests}</span></td>
                </tr>
            </template>
            </tbody>
        </table>
    </div>
</template>