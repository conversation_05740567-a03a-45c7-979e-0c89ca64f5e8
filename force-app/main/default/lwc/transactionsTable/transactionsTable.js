/* eslint-disable */
import { LightningElement, track,api,wire } from 'lwc';
import getTransactions from '@salesforce/apex/TransactionController.getTransactions';
import CurrentPageReference from 'lightning/navigation';

export default class TransactionsTable extends LightningElement {
    @track transactions = [];
    @api recordId;
    pageRef;
    connectedCallback() {
        this.fetchTransactions();
    }

    @wire(CurrentPageReference)
    wiredPageRef(pageRef) {
        if (pageRef) {
            this.pageRef = pageRef;
            this.recordId = this.recordId || pageRef.state.recordId || pageRef.state.c__recordId; // Common community URL param
            console.log('recordId '+this.recordId);
        }
    }
    async fetchTransactions() {
        try {
            const data = await getTransactions({recordId : this.recordId});
            console.log('data '+JSON.stringify(data));
            this.transactions = data.map((txn) => ({
                ...txn,
                amount: txn.amount ? this.formatNumber(txn.amount) : '',
                principalBalance: txn.principalBalance ? this.formatNumber(txn.principalBalance) : '',
                accruedInterests: txn.accruedInterests ? this.formatNumber(txn.accruedInterests) : '',
                breakdown: this.formatBreakdown(txn.breakdown),
                transactionDate: this.formatDate(txn.transactionDate), // Format to date-only
                rowClass: txn.type === 'Disbursement' ?
                    'highlight-pink' : txn.type === 'Payment' ? 'highlight-blue' :
                        txn.type === 'Outstanding Fees' ? 'highlight-white' :
                            txn.type !== undefined ?  'highlight-yellow' : ''
            }));
        } catch (error) {
            console.error('Error fetching transactions', error);
        }
    }

    formatNumber(value) {
        // Format number with commas and 2 decimal places
        return new Intl.NumberFormat('en-US', {
            style: 'decimal',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(value);
    }

    // formatDate(dateString) {
    //     if (!dateString) return '';
    //     const date = new Date(dateString);
    //     const month = String(date.getMonth() + 1).padStart(2, '0');
    //     const day = String(date.getDate()).padStart(2, '0');
    //     const year = date.getFullYear();
    //     return `${month}/${day}/${year}`;
    // }

    formatDate(dateString) {
        if (!dateString) return '';

        const date = new Date(dateString);
        return new Intl.DateTimeFormat('en-US', { timeZone: 'UTC', year: 'numeric', month: '2-digit', day: '2-digit' }).format(date);
    }

    // Format all breakdown values
    formatBreakdown(breakdown) {
        if (!breakdown) return null;

        const formattedBreakdown = {};
        for (let key in breakdown) {
            if (Object.prototype.hasOwnProperty.call(breakdown, key)) {
                formattedBreakdown[key] = this.formatNumber(breakdown[key]);
            }
        }
        return formattedBreakdown;
    }
}