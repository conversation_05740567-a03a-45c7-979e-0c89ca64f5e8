/* eslint-disable */

import { LightningElement, track, api } from 'lwc';

export default class ProjectCashflowSidebar extends LightningElement {
   
    @api projectData = [];
    @api cashFlowData = [];
    @api weekColumns = [];
    

    
    @track startDate = '';
    @track endDate = '';

    connectedCallback() {
        
        console.log('all cashFlows -> ', JSON.stringify(this.cashFlowData));
        console.log('all projects: ', JSON.stringify(this.projectData));
        console.log('all weeks: ', JSON.stringify(this.weekColumns));
       
    }


    handleStartDateChange(event) {
        const inputCmp = event.target;
        const chosenValue = inputCmp.value;
        if (!chosenValue) {
            this.startDate = '';
            return;
        }
        const dt = new Date(chosenValue);
        const todayIndex = dt.getDay();
        const offsetDays = (todayIndex <= 5)
            ? (5 - todayIndex)
            : (7 - todayIndex + 5);
        const nextFriday = new Date(dt.getTime() + offsetDays * 86400000);
        const yyyy = nextFriday.getFullYear();
        const mm = String(nextFriday.getMonth() + 1).padStart(2, '0');
        const dd = String(nextFriday.getDate()).padStart(2, '0');
        const fridayStr = `${yyyy}-${mm}-${dd}`;
        this.startDate = fridayStr;
        inputCmp.value = fridayStr;
    }

    handleEndDateChange(event) {
        const inputCmp = event.target;
        const chosenValue = inputCmp.value;
        if (!chosenValue) {
            this.endDate = '';
            return;
        }
        const dt = new Date(chosenValue);
        const todayIndex = dt.getDay();
        const offsetDays = (todayIndex <= 5)
            ? (5 - todayIndex)
            : (7 - todayIndex + 5);
        const nextFriday = new Date(dt.getTime() + offsetDays * 86400000);
        const yyyy = nextFriday.getFullYear();
        const mm = String(nextFriday.getMonth() + 1).padStart(2, '0');
        const dd = String(nextFriday.getDate()).padStart(2, '0');
        const fridayStr = `${yyyy}-${mm}-${dd}`;
        this.endDate = fridayStr;
        inputCmp.value = fridayStr;
    }

    // 1) Cash Flow Verification (empty string)
    @track cashFlowVerification = '';

    // 2) Cost Check (“MISSING COSTS”)
    @track costCheck = 'MISSING COSTS';

    // 3) Loan Paid Off in Model (“NO”)
    @track loanPaidOffInModel = 'NO';

    // 4) Invoices Balance (“GROSS Contract TIES OUT”)
    @track invoicesBalance = 'GROSS Contract TIES OUT';
    
    @track loanType = 'Mobilization'; // default
    get loanTypeOptions() {
        return [
            { label: 'Mobilization', value: 'Mobilization' },
            { label: 'P.O.', value: 'P.O.' },
            { label: 'AR Facility', value: 'AR Facility' },
            { label: 'Protected Advance - Invoice', value: 'Protected Advance - Invoice' }
        ];
    }
    handleLoanTypeChange(event) {
        this.loanType = event.detail.value;
    }

  
    @track payAppDays = '35'; // default as string
    get payAppDaysOptions() {
        const opts = [];
        for (let i = 0; i <= 91; i += 7) {
            opts.push({ label: `${i}`, value: `${i}` });
        }
        return opts;
    }
    handlePayAppDaysChange(event) {
        this.payAppDays = event.detail.value;
    }

   

    
    get remainingGrossContractValue() {
        // If projectData or field is undefined, default to 0
        return this.projectData.Remaining_Contract_Value__c || 0;
    }

    
    get retainagePercentage() {
        const raw = this.projectData.Retainage__c;
        return raw != null ? raw / 100 : 0;
    }

    
    get retainageOnGrossContract() {
        return this.remainingGrossContractValue * this.retainagePercentage;
    }

    
    get netRemainingContractValue() {
        return this.remainingGrossContractValue - this.retainageOnGrossContract;
    }

   
    @track maxLtvAllowed = 0.20; // 20%

    
    get suggestedMaxLoanAmount() {
        return Math.round(this.netRemainingContractValue * this.maxLtvAllowed);
    }

    
    @track loanPrincipalAmount = 0;
    handleLoanPrincipalChange(event) {
        this.loanPrincipalAmount = parseFloat(event.detail.value) || 0;
    }

    
    @track isOpen = false;
    get panelClass() {
        return this.isOpen ? 'panel open' : 'panel';
    }
    get toggleClass() {
        return this.isOpen ? 'toggle-button open' : 'toggle-button';
    }
    get toggleTitle() {
        return this.isOpen ? 'Close Filters' : 'Open Filters';
    }
    get currentIcon() {
        // When open: chevronright (to close). When closed: chevronleft (to open).
        return this.isOpen ? 'utility:chevronright' : 'utility:chevronleft';
    }
    handleToggle() {
        this.isOpen = !this.isOpen;
    }
}