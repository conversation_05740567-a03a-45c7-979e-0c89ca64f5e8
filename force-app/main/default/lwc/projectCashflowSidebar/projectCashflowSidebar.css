.sidebar-container {
    position: fixed;
    top: 0;
    right: 0;
    height: 100%;
    z-index: 1000;
}



.panel {
    position: absolute;
    top: 0;
    right: -310px;                  /* hidden off‐screen */
    width: 310px;                   /* adjust width to your needs */
    height: 100%;
    background-color: #f3f3f3;
    border-left: 1px solid #d8dde6;
    box-shadow: -2px 0 4px rgba(0,0,0,0.1);
    transition: right 0.3s ease-in-out;
    display: flex;
    flex-direction: column;
    
}

.panel.open {
    right: 0;                       /* slide fully into view */
}



.toggle-button {
    position: absolute;
    top: 11.5rem;
    right: 0.77rem;                      /* when closed, at viewport’s right‐edge */
    background-color: #cee1f2;
    border: 1px solid #d8dde6;
    border-radius: 0.25rem 0rem 0rem 0.25rem; /* only round the right side */
    padding: 0.25rem;
    cursor: pointer;
    outline: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    z-index: 1000;                /* always on top of .panel */
    transition: right 0.3s ease-in-out;
}

.toggle-button.open {
    right: 310px;                  /* when panel open, shift it left by panel width */
}

.toggle-button:hover {
    background-color: #f4f6f9;
}



.panel-header {
    padding: 1rem;
    border-bottom: 1px solid #d8dde6;
}

.panel-body {
    padding: 1rem;
    overflow-y: scroll;
}