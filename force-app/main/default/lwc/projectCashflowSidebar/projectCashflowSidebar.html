<template>
    <div class="sidebar-container">
        <!-- Toggle Button (unchanged) -->
        <button
            class={toggleClass}
            title={toggleTitle}
            onclick={handleToggle}
            aria-label={toggleTitle}
        >
            <lightning-icon
                icon-name={currentIcon}
                size="small"
                alternative-text={toggleTitle}
            ></lightning-icon>
        </button>

        <!-- Sliding Panel (unchanged) -->
        <div class={panelClass}>
            <header class="panel-header">
                <h2 class="slds-text-heading_medium">Floating Panel</h2>
            </header>

            <div class="panel-body">
                <!-- Editable Start Date -->
                <lightning-input
                    type="date"
                    name="startDate"
                    label="Start Date"
                    value={startDate}
                    onchange={handleStartDateChange}
                ></lightning-input>

                <!-- Editable End Date -->
                <lightning-input
                    type="date"
                    name="endDate"
                    label="End Date"
                    value={endDate}
                    onchange={handleEndDateChange}
                ></lightning-input>

                <!-- ==== NEW FIELDS IN A STACKED “TWO-LINE” LAYOUT ==== -->
                <div class="slds-m-top_medium">
                    <!-- Cash Flow Verification -->
                    <div class="slds-p-vertical_x-small slds-border_bottom">
                        <span class="slds-text-title_bold slds-truncate">Cash Flow Verification</span><br/>
                        <span class="slds-text-body_regular slds-truncate">{cashFlowVerification}</span>
                    </div>

                    <!-- Cost Check -->
                    <div class="slds-p-vertical_x-small slds-border_bottom">
                        <span class="slds-text-title_bold slds-truncate">Cost Check</span><br/>
                        <span class="slds-text-body_regular slds-truncate">{costCheck}</span>
                    </div>

                    <!-- Loan Paid Off in Model -->
                    <div class="slds-p-vertical_x-small slds-border_bottom">
                        <span class="slds-text-title_bold slds-truncate">Loan Paid Off in Model</span><br/>
                        <span class="slds-text-body_regular slds-truncate">{loanPaidOffInModel}</span>
                    </div>

                    <!-- Invoices Balance -->
                    <div class="slds-p-vertical_x-small">
                        <span class="slds-text-title_bold slds-truncate">Invoices Balance</span><br/>
                        <span class="slds-text-body_regular slds-truncate">{invoicesBalance}</span>
                    </div>
                </div>
                <!-- ==== END OF NEW STACKED FIELDS ==== -->


                <!-- 1) Type of Loan (picklist) -->
                <lightning-combobox
                    name="loanType"
                    label="Type of Loan"
                    value={loanType}
                    placeholder="-- Select Loan Type --"
                    options={loanTypeOptions}
                    onchange={handleLoanTypeChange}
                ></lightning-combobox>

                <!-- 2) Days for Pay App to be Paid (picklist) -->
                <lightning-combobox
                    name="payAppDays"
                    label="Days for Pay App to be Paid"
                    value={payAppDays}
                    placeholder="-- Select Days --"
                    options={payAppDaysOptions}
                    onchange={handlePayAppDaysChange}
                ></lightning-combobox>

                <!-- 3) Gross Contract Value -->
                <div class="slds-p-top_small">
                    <label class="slds-form-element__label" for="grossContractValue">
                        Gross Contract Value
                    </label>
                    <div class="slds-form-element__control">
                        <lightning-formatted-number
                            value={grossContractValue}
                            format-style="currency"
                            currency-code="USD"
                            class="slds-text-body_regular"
                        ></lightning-formatted-number>
                    </div>
                </div>

                <!-- 4) Less: PAID to DATE -->
                <div class="slds-p-top_small">
                    <label class="slds-form-element__label" for="paidToDate">
                        Less: PAID to DATE
                    </label>
                    <div class="slds-form-element__control">
                        <lightning-formatted-number
                            value={paidToDate}
                            format-style="currency"
                            currency-code="USD"
                            class="slds-text-body_regular"
                        ></lightning-formatted-number>
                    </div>
                </div>

                <!-- 5) Plus: Change Orders -->
                <div class="slds-p-top_small">
                    <label class="slds-form-element__label" for="changeOrders">
                        Plus: Change Orders
                    </label>
                    <div class="slds-form-element__control">
                        <lightning-formatted-number
                            value={changeOrders}
                            format-style="currency"
                            currency-code="USD"
                            class="slds-text-body_regular"
                        ></lightning-formatted-number>
                    </div>
                </div>

                <!-- 6) Remaining Gross Contract Value -->
                <div class="slds-p-top_small">
                    <label class="slds-form-element__label" for="remainingGrossContractValue">
                        Remaining Gross Contract Value
                    </label>
                    <div class="slds-form-element__control">
                        <lightning-formatted-number
                            value={remainingGrossContractValue}
                            format-style="currency"
                            currency-code="USD"
                            class="slds-text-body_regular"
                        ></lightning-formatted-number>
                    </div>
                </div>

                <!-- 7) Retainage Percentage -->
                <div class="slds-p-top_small">
                    <label class="slds-form-element__label" for="retainagePercentage">
                        Retainage Percentage
                    </label>
                    <div class="slds-form-element__control">
                        <lightning-formatted-number
                            value={retainagePercentage}
                            format-style="percent"
                            maximum-fraction-digits="0"
                            class="slds-text-body_regular"
                        ></lightning-formatted-number>
                    </div>
                </div>

                <!-- 8) Less: Retainage on Gross Contract -->
                <div class="slds-p-top_small">
                    <label class="slds-form-element__label" for="retainageOnGrossContract">
                        Less: Retainage on Gross Contract
                    </label>
                    <div class="slds-form-element__control">
                        <lightning-formatted-number
                            value={retainageOnGrossContract}
                            format-style="currency"
                            currency-code="USD"
                            class="slds-text-body_regular"
                        ></lightning-formatted-number>
                    </div>
                </div>

                <!-- 9) Net Remaining Contract Value -->
                <div class="slds-p-top_small">
                    <label class="slds-form-element__label" for="netRemainingContractValue">
                        Net Remaining Contract Value
                    </label>
                    <div class="slds-form-element__control">
                        <lightning-formatted-number
                            value={netRemainingContractValue}
                            format-style="currency"
                            currency-code="USD"
                            class="slds-text-body_regular"
                        ></lightning-formatted-number>
                    </div>
                </div>

                <!-- 10) Maximum Loan To Value (LTV) Allowed -->
                <div class="slds-p-top_small">
                    <label class="slds-form-element__label" for="maxLtvAllowed">
                        Maximum Loan To Value (LTV) Allowed
                    </label>
                    <div class="slds-form-element__control">
                        <lightning-formatted-number
                            value={maxLtvAllowed}
                            format-style="percent"
                            maximum-fraction-digits="0"
                            class="slds-text-body_regular"
                        ></lightning-formatted-number>
                    </div>
                </div>

                <!-- 11) Suggested Maximum Loan Amount -->
                <div class="slds-p-top_small">
                    <label class="slds-form-element__label" for="suggestedMaxLoanAmount">
                        Suggested Maximum Loan Amount
                    </label>
                    <div class="slds-form-element__control">
                        <lightning-formatted-number
                            value={suggestedMaxLoanAmount}
                            format-style="currency"
                            currency-code="USD"
                            class="slds-text-body_regular"
                        ></lightning-formatted-number>
                    </div>
                </div>

                <!-- 12) Loan Principal Amount (editable by user) -->
                <div class="slds-p-top_small">
                    <lightning-input
                        type="currency"
                        name="loanPrincipalAmount"
                        label="Loan Principal Amount"
                        value={loanPrincipalAmount}
                        step="0.01"
                        onchange={handleLoanPrincipalChange}
                    ></lightning-input>
                </div>
                

            </div>
        </div>
    </div>
</template>