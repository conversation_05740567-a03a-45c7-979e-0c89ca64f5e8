/* eslint-disable */
import { LightningElement, api, track, wire } from 'lwc';
import CASHFLOW_LINE_ITEM_OBJECT from '@salesforce/schema/Cashflow_Line_Item__c';
import { getObjectInfo, getPicklistValues } from 'lightning/uiObjectInfoApi';
import CATEGORY_FIELD from '@salesforce/schema/Cashflow_Line_Item__c.Line_Item_Category__c';

export default class CashflowProjectCashflow extends LightningElement {
    @api recordId;
    @api projectName;
    
    @api weekColumns = [];
    @api projectData = [];
    @api activeCashflow = [];
    
    // @api cashFlowData = [];
    @api isLoading = false;
    isLoading = false;

    // Pass options from parent to child modal correctly
    @api variableFixedOptions = []; // This is used internally by modalData if needed
    @api paymentFrequencyOptions = []; // This will be passed to the modal

    

    // These were local, ensure they are correctly sourced or passed if needed
    @track expenseCategoryOptions = [];
    @track paymentTermOptions = [
        { label: 'Net 30', value: 'Net 30' },
        { label: 'Net 60', value: 'Net 60' },
        { label: 'Due on Receipt', value: 'Due on Receipt' },
        { label: 'COD', value: 'COD' } // Added COD from Figma
    ];

    @track isModalVisible = false;
    // No longer need modalData here directly, it's managed by the child modal
    _currentEditCellContext = {}; // Will store { rowId, weekIdentifier, date, originalCellPayload }

    // Getter to ensure the paymentFrequencyOptions passed to the modal are correct
    get paymentFrequencyOptionsInternal() {
        return this.paymentFrequencyOptions;
    }

    @track isDrawerOpen = false;

    // 2) Compute a valid SLDS icon name
    get drawerIconName() {
        // pick whichever “chevron” makes sense when open vs closed
        return this.isDrawerOpen
            ? 'utility:chevronleft'   // points back toward the table
            : 'utility:chevronright'; // points away
    }

    // 3) Wire up the button
    toggleDrawer() {
        this.isDrawerOpen = !this.isDrawerOpen;
    }

    @track _cashFlowData = [];

    // whenever parent writes to `cash-flow-data`, we map in our flag
    @api
    set cashFlowData(rows) {
        this._cashFlowData = (rows || []).map(r => ({
            ...r,
            // mark every expense‐type row for deletion
            showDeleteButton: r.type === 'DATA_EXPENSE'
        }));
    }
    get cashFlowData() {
        return this._cashFlowData;
    }



      defaultRecordTypeId;

      @wire(getObjectInfo, { objectApiName: CASHFLOW_LINE_ITEM_OBJECT })
      handleObjectInfo({ data, error }) {
        if (data) {
          this.defaultRecordTypeId = data.defaultRecordTypeId;
        } else if (error) {
          console.error('Error loading object info', error);
        }
      }

      @wire(getPicklistValues, {
        recordTypeId: '$defaultRecordTypeId',
        fieldApiName: CATEGORY_FIELD
      })
      handleCategoryPicklist({ data, error }) {
        if (data) {
          // data.values is an array of { label, value, validFor, ... }
          this.expenseCategoryOptions = data.values;
        } else if (error) {
          console.error('Error loading picklist values', error);
        }
      }

    connectedCallback() {
        console.log('CashflowProjectCashflow loaded');
        console.log('cashFlowData -> ', JSON.stringify(this.cashFlowData));
        console.log('projects: ', JSON.stringify(this.projectData));
        console.log('activeCashflow: ', JSON.stringify(this.activeCashflow));
        console.log('cashFlowProjectCashflow got cashFlowData:',JSON.stringify(this.cashFlowData, null, 2));
    }

    @track isCategoryModalOpen = false;
    @track selectedCategory = null;
    @track categoryModalSection = null;

    handleAddItem(event) {
        console.log('this.isCategoryModalOpen ' + this.isCategoryModalOpen);
        const sectionId = event.currentTarget.dataset.sectionId;
        this.categoryModalSection = sectionId;
        this.isCategoryModalOpen = true;
        this.selectedCategory = null;

    }
    closeCategoryModal() {
        this.isCategoryModalOpen = false;
    }

    handleRadioChange(evt) {
        this.selectedCategory = evt.detail.value;
    }

    get isAddDisabled() {
        return !this.selectedCategory;
    }


    confirmCategorySelection() {
        this.dispatchEvent(new CustomEvent('addrow', {
            detail: {
                sectionId: this.categoryModalSection,
                category: this.selectedCategory
            },
            bubbles: true,
            composed: true
        }));
        this.closeCategoryModal();
    }

    handleDeleteRowClick(event) {
        const rowId = event.currentTarget.dataset.rowId;
        this.dispatchEvent(new CustomEvent('deleterow', {
            detail: { rowId },
            bubbles: true,
            composed: true
        }));
    }

    computedCellClass(cell) {
        let classes = cell.cellClass || 'slds-text-align_center';
        if (cell.isDifference) { // 'isDifference' means it's editable and blue highlighted
            classes += ' cell-clickable cell-difference wholelightblue';
        }
        return classes;
    }

    handleCellClick(event) {
        const rowIndex = parseInt(event.currentTarget.dataset.rowIndex, 10);
        const colIndex = parseInt(event.currentTarget.dataset.colIndex, 10);

        const clickedRowData = this.cashFlowData[rowIndex];
        const clickedCellData = clickedRowData?.weeks[colIndex];

        if (clickedRowData && clickedCellData && clickedCellData.isDifference) {
            // Store key identifiers and the original cell payload for context
            this._currentEditCellContext = {
                rowId: clickedRowData.id, // ID of the row
                weekIdentifier: clickedCellData.weekIdentifier, // Identifier of the week column
                date: clickedCellData.date, // Specific date of the cell
                originalCellPayload: JSON.parse(JSON.stringify(clickedCellData)) // Full original cell data
            };
            console.log('[CashflowProjectCashflow] _currentEditCellContext set:', JSON.stringify(this._currentEditCellContext));


            // Prepare data specifically for the modal's inputs
            const dataForModal = {
                category: clickedCellData.category || clickedCellData.rowLabel, // Display category
                expenseCategory: clickedCellData.expenseCategory || clickedCellData.category, // Editable expense category
                date: clickedCellData.date,
                variableFixed: clickedCellData.variableFixed || 'Fixed', // Default if undefined
                paymentFrequency: clickedCellData.paymentFrequency || 'Weekly', // Default if undefined
                weeklyAmount: clickedCellData.weeklyAmount !== undefined ? clickedCellData.weeklyAmount : clickedCellData.value,
                paymentTerm: clickedCellData.paymentTerm || 'Net 30', // Default if undefined
                // Pass any other relevant fields from clickedCellData if the modal needs them
            };
            console.log('[CashflowProjectCashflow] Data for modal:', JSON.stringify(dataForModal));


            const modal = this.template.querySelector('c-cashflow-edit-modal');
            if (modal) {
                modal.setModalData(dataForModal);
                this.isModalVisible = true;
            }
        } else {
            console.warn('[CashflowProjectCashflow] Cell not editable or data not found. RowIndex:', rowIndex, 'ColIndex:', colIndex);
        }
    }

    handleCloseModal() {
        this.isModalVisible = false;
        this._currentEditCellContext = {};
        const modal = this.template.querySelector('c-cashflow-edit-modal');
        if (modal) modal.stopWorking(); // Ensure spinner stops if closed manually
    }

    handleModalSave(event) {
        const updatedDataFromModal = event.detail;
        const { rowId, weekIdentifier, date, originalCellPayload } = this._currentEditCellContext;

        if (rowId && weekIdentifier && date && originalCellPayload) {
            const eventDetail = {
                updatedCellData: { // Data from the modal's current state
                    value: updatedDataFromModal.weeklyAmount, // Ensure this name matches modal's output
                    weeklyAmount: updatedDataFromModal.weeklyAmount,
                    category: updatedDataFromModal.category, // Original category (display only in modal)
                    expenseCategory: updatedDataFromModal.expenseCategory, // Potentially changed category
                    date: updatedDataFromModal.date, // Should be original date from hidden input
                    variableFixed: updatedDataFromModal.variableFixed, // Should be original from hidden input
                    paymentFrequency: updatedDataFromModal.paymentFrequency,
                    paymentTerm: updatedDataFromModal.paymentTerm
                    // If modal was to edit a specific SFID from multiple in a cell, it would return it here.
                    // salesforceId: updatedDataFromModal.editedSalesforceId // Example
                },
                originalCellData: { // Key identifiers for the container to find the cell
                    rowId: rowId,
                    weekIdentifier: weekIdentifier,
                    date: date,
                    // Optionally include the full original payload if container needs more detailed comparison
                    payload: originalCellPayload
                }
            };
            console.log('[CashflowProjectCashflow] Dispatching popoversave with eventDetail:', JSON.stringify(eventDetail));
            this.dispatchEvent(new CustomEvent('popoversave', { detail: eventDetail }));
        } else {
            console.error('[CashflowProjectCashflow] Critical context missing for saving modal data. Context:', JSON.stringify(this._currentEditCellContext));
            this.showToast('Error', 'Internal error: Missing cell context for save.', 'error');
        }
        this.handleCloseModal();
    }

    handleModalUnlink() {
        const { rowIndex, colIndex } = this._currentEditCellContext;
        if (rowIndex !== undefined && colIndex !== undefined) {
            this.dispatchEvent(new CustomEvent('popoverunlink', {
                detail: { rowIndex, colIndex }
            }));
        }
        this.handleCloseModal();
    }

    handleModalDelete() {
        const { rowIndex, colIndex } = this._currentEditCellContext;
        console.log('Delete item event received from modal for cell:', rowIndex, colIndex);
        this.handleModalUnlink();
        this.showToast('Info', 'Item delete requested (simulated as unlink).', 'info');
    }

    handleModalToastError(event) {
        const { title, message } = event.detail;
        this.showToast(title, message, 'error');
    }

    
    handleWeekColumnsChange(event) {
    // pull the filtered weeks from the sidebar
        this.dispatchEvent(new CustomEvent('weekcolumnschange', {
            detail: { weekColumns: event.detail.weekColumns },
            bubbles: true,
            composed: true
        }));
        // this.weekColumns = event.detail.weekColumns;
        // console.log('Parent got filtered weeks:', this.weekColumns);
    }



    showToast(title, message, variant) {
        this.dispatchEvent(new CustomEvent('toasterror', {
            bubbles: true,
            composed: true,
            detail: { title, message, variant }
        }));
    }


    get totalColumnCountInternal() {
        return (this.weekColumns?.length || 0) + 1;
    }

    // get filteredExpenseCategoryOptions() {
    //     const used = new Set(
    //         this._cashFlowData
    //             .filter(r => r.type === 'DATA_EXPENSE' && !r.isNew)
    //             .map(r => r.label)
    //     );
    //     const remaining = this.expenseCategoryOptions.filter(opt => !used.has(opt.value));
    //     return remaining.map(opt => ({ label: opt.label, value: opt.value }));
    // }

    get filteredExpenseCategoryOptions() {
    const vals = Array.isArray(this.expenseCategoryOptions)
      ? this.expenseCategoryOptions
      : [];
    const used = new Set(
        this._cashFlowData
            .filter(r => r.type === 'DATA_EXPENSE' && !r.isNew)
            .map(r => r.label)
    );
    return vals
      .filter(opt => !used.has(opt.value))
      .map(opt => ({ label: opt.label, value: opt.value }));
}

get hasFilteredCategories() {
  return this.filteredExpenseCategoryOptions.length > 0;
}


}