/* eslint-disable */
import { LightningElement, api,track } from 'lwc';

export default class CashflowCalculatorTab extends LightningElement {
    

    @api recordId;

  /** the same weekColumns array you render in the cashflow table */
  @api weekColumns = [];

  /** raw cashflow rows (if you ever want to combine them in here) */
  @api cashFlowData = [];
  @track cashFlowData = [];

  /** your calculatorData object with summary + loanDetails, etc */
  @api calculatorData = {};

  @api disbursements = [];
  @api payApplications = [];
  @api transactions = [];
  @api projectData = [];

  /** which tab is active */
  @track activeTab = 'loanDisbursements';

  connectedCallback() {
    console.log('recordId:     ', this.recordId);
    console.log('weekColumns:  ', JSON.stringify(this.weekColumns));
    console.log('cashFlowData: ', JSON.stringify(this.cashFlowData));
    console.log('calculatorData:', JSON.stringify(this.calculatorData));
    console.log('disbursementData:', JSON.stringify(this.disbursements));
    console.log('payApplications:', JSON.stringify(this.payApplications));
    console.log('transactions ', JSON.stringify(this.transactions));
    console.log('projectData: ', JSON.stringify(this.projectData));
  }

  handleTabActive(event) {
    this.activeTab = event.target.value;
  }

  handleFromLoanDisbursement(event) {
    // Replace the local array (un-freezing any proxy) with the child’s detail
    this.cashFlowData = event.detail;

    // Then re‐emit it up, so that anything above (e.g. cashflowContainer) can catch it:
    this.dispatchEvent(
      new CustomEvent('cashflowdataupdate', {
        detail: this.cashFlowData,
        bubbles: true,
        composed: true
      })
    );
  }

  handleFromPayApplication(event) {
    // event.detail is the newly patched array from PayApplication
    this.cashFlowData = event.detail;

    // Re‐emit upward so that cashflowContainer.js sees the newest state
    this.dispatchEvent(
      new CustomEvent('cashflowdataupdate', {
        detail: this.cashFlowData,
        bubbles: true,
        composed: true
      })
    );
  }




    

    get projectId() {
      return Array.isArray(this.disbursements) && this.disbursements.length
        ? this.disbursements[0].Project__c
        : null;
    }   
    
    
    get mfCashOut() {
        if (!this.projectId) return 0;
        return this.transactions
            .filter(tx => tx.Project__c === this.projectId)
            .reduce((sum, tx) => sum + (tx.Loan_Principal_to_date__c || 0), 0);
    }

    get originationFee() {
        if (!this.projectId) return 0;
        return this.transactions
            .filter(tx => tx.Project__c === this.projectId)
            .reduce((sum, tx) => {
                return sum +
                    (tx.Default_Fee_Application__c || 0) +
                    (tx.Doc_Stamp_Fees_Application__c || 0) +
                    (tx.Late_Fee_Application__c || 0) +
                    (tx.Legal_Fees_Application__c || 0);
            }, 0);
    }

    get interestIncome() {
        if (!this.projectId) return 0;
        return this.transactions
            .filter(tx => tx.Project__c === this.projectId)
            .reduce((sum, tx) => sum + (tx.Interest_Application__c || 0), 0);
    }

    get totalIncome() {
        return this.originationFee + this.interestIncome;
    }

    get totalPrincipalInterest() {
        return this.mfCashOut + this.totalIncome;
    }

    get revolverAvailability() {
        const proj = Array.isArray(this.projectData) 
          ? (this.projectData[0] || {}) 
          : (this.projectData || {});

    // use Loan_Principal__c instead of Loan_Principal_Amount__c
      const loanPrincipal = proj.Loan_Principal__c || 0;

      console.log('loanPrincipal '+loanPrincipal);

      // sum up all transactions’ Amount__c for this project
      const sumTxnAmount = this.transactions
          .filter(tx => tx.Project__c === this.projectId)
          .reduce((total, tx) => total + (tx.Amount__c || 0), 0);

          console.log('sum ',sumTxnAmount);
          console.log('mf cash ', this.mfCashOut);

      // formula: principal – mfCashOut + sum of txn amounts
      return loanPrincipal - this.mfCashOut + sumTxnAmount;
    }

    get weeksOutstanding() {
        return Array.isArray(this.weekColumns)
            ? this.weekColumns.length
            : 0;
    }


}