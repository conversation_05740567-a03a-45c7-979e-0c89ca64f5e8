// import { LightningElement, api, track } from 'lwc';
// import { NavigationMixin } from 'lightning/navigation';
// import { ShowToastEvent } from "lightning/platformShowToastEvent";
// import isSandbox from '@salesforce/apex/mf123opp.isSandbox';
// import LightningConfirm from 'lightning/confirm';
// import uploadFiles from "@salesforce/apex/AttachmentCompController.uploadFiles";
// import deleteFile from "@salesforce/apex/AttachmentCompController.deleteFile";
// import getFilesOfRecordSF from "@salesforce/apex/AttachmentCompController.getFilesOfRecordSF";
// import fetchFileShareLink from '@salesforce/apex/DropboxController.fetchFileShareLink';
// import createFileShareLink from '@salesforce/apex/DropboxController.createFileShareLink';
// import getDropboxShareLink from '@salesforce/apex/DropboxController.getDropboxShareLink';

// export default class FileUploadSFCmp extends NavigationMixin(LightningElement) {
//     @api recordId;
//     @track isloading = true;
//     files = [];
//     @track dropdownVisible = false;
//     basePath;
    
//     get dropdownClass() {
//         return this.dropdownVisible = 'slds-dropdown slds-dropdown_right';
//         // : 'slds-dropdown slds-dropdown_right';
//     }

//     get getTitle() {
//         return "Files (" + this.files.length + ")";
//     }

//     connectedCallback() {
//         console.log('connectedCallback 12 '+this.recordId);
//         this.retrieveFiles();

//         isSandbox()
//             .then((result) => {
//                 this.basePath = result ? '/mf/s/' : '/s/';
//             })
//             .catch((error) => {
//                 console.error('Error checking sandbox status:', JSON.stringify(error));
//             });
//     }

//     toggleDropdown(event) {
//         const fileId = event.target.dataset.id;
//         console.log('fileId '+fileId);
        
//         this.files = this.files.map(file => ({
//             ...file,
//             dropdownVisible: file.contentDocument.Id === fileId ? !file.dropdownVisible : false
//         }));
        
//         console.log('this files 36'+JSON.stringify(this.files));
//     }


//     get hasFiles() {
//         return this.files != undefined && this.files.length > 0;
//     }

//     retrieveFiles() {
//         //this.isloading = false; 

//         getFilesOfRecordSF({ recordId: this.recordId, showAllData: false })
//         .then((files) => {
//             console.log('files 4332' + JSON.stringify(files));
//             let baseUrl = "https://" + location.host;
//             // console.log('baseUrl ' + baseUrl);

//             // .filter(file => file.FileExtension !== 'snote')
//             this.files = files
//                 .map((file) => {
//                     const { contentDocument, contentDocumentLink } = file;
//                     let fileData = {
//                         ...file,
//                         Id: contentDocument.Id,
//                         Title: contentDocument.Title,
//                         Extension: contentDocument.FileExtension,
//                         isInternally: contentDocumentLink.Visibility === 'InternalUsers',
//                         ContentSize: this.formatBytes(contentDocument.ContentSize),
//                         ContentModifiedDate: this.formatDate(contentDocument.ContentModifiedDate),
//                         CreatedByName: contentDocument.CreatedBy.FirstName ? contentDocument.CreatedBy.FirstName + ' ' + contentDocument.CreatedBy.LastName : contentDocument.CreatedBy.LastName,
//                         dropdownVisible: false,
//                         DropboxUrl: contentDocument.LatestPublishedVersion?.Dropbox_Url__c || null,
//                         isFileSynced: contentDocument.LatestPublishedVersion?.Dropbox_Sync_Status__c,
//                         showViewExternal: contentDocument.FileExtension && contentDocument.FileExtension.toLowerCase() === 'xlsx'
//                                             && contentDocument.LatestPublishedVersion?.Dropbox_Url__c && (contentDocument.LatestPublishedVersion?.Dropbox_Sync_Status__c === 'Processing - Waiting Dropbox Confirmation' || contentDocument.LatestPublishedVersion?.Dropbox_Sync_Status__c === 'Synced')
//                         //showViewExternal: contentDocument.FileExtension && (contentDocument.FileExtension.toLowerCase() === 'xlsx')
//                     };

//                     if (contentDocument.ContentDistributions && contentDocument.ContentDistributions.length > 0) {
//                         fileData.DownloadUrl = contentDocument.ContentDistributions[0]?.ContentDownloadUrl || '';
//                         fileData.FileUrl = contentDocument.ContentDistributions[0]?.DistributionPublicUrl || '';
//                     } else { 
//                         fileData.DownloadUrl = baseUrl  + "/sfc/servlet.shepherd/document/download/" + contentDocument.Id;
//                         fileData.FileUrl = baseUrl + '/sfc/servlet.shepherd/version/renditionDownload?rendition=THUMB720BY480&versionId=' + contentDocument.LatestPublishedVersionId;
//                     }

//                     return fileData;
//                 });

//                 console.log('this files '+JSON.stringify(this.files));

//             this.isloading = false;
//             // this.hasAttachments = this.files.length > 0;
//         })
//         .catch((error) => {
//             console.error('Error retrieving files:', this.getWireError(error));
//             this.displayToast("Error", this.getWireError(error), "error");
//             // this.hasAttachments = false;
//             this.isloading = false;
//         });
//     }

//     formatBytes(bytes, decimals = 1) {
//         if (bytes === 0) return "0 Bytes";

//         const k = 1024;
//         const dm = decimals < 0 ? 0 : decimals;
//         const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];

//         const i = Math.floor(Math.log(bytes) / Math.log(k));

//         return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + " " + sizes[i];
//     }

//     formatDate(dateString) {
//         const options = { year: 'numeric', month: 'numeric', day: 'numeric' };
//         return new Date(dateString).toLocaleDateString(undefined, options);
//     }

//     handleUploadFinished(event) {
//         const contentDocumentIds = event.detail.files.map(file => file.documentId);
//         console.log('contentDocumentIds '+contentDocumentIds);
//         console.log('recordId '+this.recordId);
//         uploadFiles({ recordId: this.recordId, contentDocumentIds, isInternal: false })
//             .then(() => {
//                 this.retrieveFiles();
//                 this.displayToast("Success", "Files uploaded successfully", "success");
//                 //${uploadedFiles.length}
//                 // this.dispatchEvent(
//                 //     new ShowToastEvent({
//                 //         title: 'Success',
//                 //         message: ` file(s) uploaded successfully!`,
//                 //         variant: 'success',
//                 //     })
//                 // );
//             })
//             .catch((error) => {
//                 this.displayToast("Error", this.getWireError(error), "error");
//             });
//     }
    
//     handleInternalUpload(event) {
//         const contentDocumentIds = event.detail.files.map(file => file.documentId);
//         console.log('contentDocumentIds '+contentDocumentIds);
//         console.log('recordId '+this.recordId);
//         uploadFiles({ recordId: this.recordId, contentDocumentIds, isInternal: true })
//             .then(() => {
//                 this.retrieveFiles();
//                 this.displayToast("Success", "Files uploaded successfully", "success");
//             })
//             .catch((error) => {
//                 this.displayToast("Error", this.getWireError(error), "error");
//             });
//     }
    
//      async handleRowAction(event) {
//         let actionName = event.currentTarget.dataset.name;
//         let actiontoggle = event.currentTarget.dataset.actiontoggle;
//         let fileId = event.currentTarget.dataset.id;
//         let row = this.files.find(file => file.Id === fileId);
//         if(!actiontoggle){
//             this.files = this.files.map(file => ({
//                 ...file,
//                 dropdownVisible: file.contentDocument.Id === fileId ? !file.dropdownVisible : false
//             }));
//         }

//         let fileName = event.currentTarget.dataset.title;
//         console.log('fileName' + fileName);
//         let fileExtension = event.currentTarget.dataset.fileextension;
//         console.log('fileExtension' + fileExtension);
//         let filepath = event.currentTarget.dataset.filepath;
//         console.log('filepath' + filepath);
      
//         switch (actionName) {
//             case 'view':
//                 // this[NavigationMixin.Navigate]({
//                 //     type: 'standard__webPage',
//                 //     attributes: {
//                 //         url: '/sfc/servlet.shepherd/document/download/' + fileId
//                 //     }
//                 // });
//                 // this.viewDocument(event.currentTarget.dataset);
//                 this.viewDocument(row);
                
//                 break;
//             case 'download':
//                 this.downloadFile(row);
//                 break;
//             case 'delete':
//                 await this.deleteFile(fileId);
//                 break;
//             case 'View External':
//                 this.isloading = true;
//                 this.viewInExcelMode(fileName, fileExtension, filepath, fileId);
//                 break;
//             default:
//         }
//     }

//         viewInExcelMode(fileName, fileExtension, filepath, fileId) {
//         let completeFileName = fileName + '.' + fileExtension;
//         console.log('completeFileName' + completeFileName);
//         let completeFilePath = filepath + completeFileName;
//         console.log('completeFilePath' + completeFilePath);

//         getDropboxShareLink({ contentDocumentId: fileId })
//             .then((existingLink) => {
//                 if (existingLink) {
//                     this.isloading = false;
//                     console.log('Using existing Dropbox link:', existingLink);
//                     this.navigateToDropboxLink(existingLink);
//                 } else {
//                     console.log('No existing Dropbox link. Creating new share link...');
//                     createFileShareLink({ path: completeFilePath, contentDocumentId: fileId })
//                         .then((newLink) => {
//                             console.log('newLink '+newLink);
//                             if (newLink === 'shared_link_exists') {
//                                 console.log('Share link already exists. Fetching existing link...');
//                                 //this.fetchFileShareLink(contentDocumentId);
//                                 fetchFileShareLink({ path: completeFilePath })
//                                     .then((result) => { 
//                                         if (result.url) {
//                                             this.isloading = false;
//                                             console.log('Dropbox URL:414 ', result.url);

//                                             this[NavigationMixin.Navigate](
//                                                 {
//                                                     type: 'standard__webPage',
//                                                     attributes: {
//                                                         url: result.url,
//                                                     },
//                                                 },
//                                                 false
//                                             );
//                                         } else if (result.error) {
//                                             this.showToast('Error', result.error, 'error');
//                                         } else {
//                                             this.showToast('Error', 'Unexpected response from server.', 'error');
//                                         }
//                                     })
//                                     .catch((error) => {
//                                         console.error('Error calling Apex method:', error);
//                                         this.showToast('Error', 'Failed to open the file in edit mode.', 'error');
//                                     });
//                             } else if (newLink != 'shared_link_exists') {
//                                 this.isloading = false;
//                                 console.log('New Dropbox link created:', newLink);
//                                 this.navigateToDropboxLink(newLink); 
//                             }
//                              else {
//                                 this.showToast('Error', 'Failed to create Dropbox share link.', 'error');
//                             }
//                         })
//                         .catch(() => {
//                             this.showToast('Error', 'Failed to create Dropbox share link.', 'error');
//                         });
//                 }
//             })
//             .catch((error) => {
//                 console.error('Error fetching Dropbox share link:', error);
//                 this.showToast('Error', 'Failed to fetch Dropbox share link.', 'error');
//             });
//     }

//     navigateToDropboxLink(url) {
//         this[NavigationMixin.Navigate](
//             {
//                 type: 'standard__webPage',
//                 attributes: {
//                     url: url,
//                 },
//             },
//             false
//         );
//     }

//      async deleteFile(fileId) {
//         const result = await LightningConfirm.open({
//             message: 'Are you sure you want to delete this file?',
//             variant: 'header',
//             label: 'Delete Confirmation',
//         });
//         if (result) {
//             this.isloading = true;
//             deleteFile({ contentDocumentId: fileId })
//                 .then(() => {
//                     //this.displayToast('Success', result, 'success');
//                     this.displayToast("Success", "File deleted successfully", "success");
//                     this.isloading = false;
//                     this.retrieveFiles();
//                 })
//                 .catch(error => {
//                     //this.displayToast('Error', this.getWireError(error), 'error');
//                     this.displayToast("Error", this.getWireError(error), "error");
//                     this.isloading = false;
//                 });
//         }
//     }

//     downloadFile(row) {
//         console.log('in downloadFile row '+JSON.stringify(row));
//         window.location.href = row.DownloadUrl;
//         setTimeout(() => {
//             this.displayToast('Download Complete', 'Your file has been downloaded.', 'success');
//         }, 3000);
//     }

//     viewDocument(row){
//         console.log('in downloadFile row '+JSON.stringify(row));
//         console.log('in downloadFile row '+row.FileUrl);
//         console.log('cid ' + row.contentDocument.Id);
//         // this[NavigationMixin.Navigate]({
//         //     type: 'standard__webPage',
//         //     attributes: {
//         //         url: row.FileUrl
//         //     }
//         // }, false );

//         this[NavigationMixin.Navigate]({
//         type: "standard__namedPage",
//         attributes: {
//             pageName: "filePreview",
//         },
//         state: {
//             recordIds: row.contentDocument.Id,
//             selectedRecordId: row.contentDocument.Id,
//         },
//         });
//     }

//     displayToast(title, message, variant) {
//         this.dispatchEvent(
//             new ShowToastEvent({
//                 title: title,
//                 message: message,
//                 variant: variant
//             })
//         );
//     }

//     getWireError(error) {
//         let message = "Unknown error";
//         if (Array.isArray(error.body)) {
//             message = error.body.map((e) => e.message).join(", ");
//         } else if (typeof error.body.message === "string") {
//             message = error.body.message;
//         }
//         return message;
//     }
    
//     // handleViewAll() {
//     //     this[NavigationMixin.Navigate]({
//     //         type: 'standard__navItemPage', 
//     //         attributes: {
//     //             apiName: 'View_Files' 
//     //         },
//     //         state: {
//     //             c__recordId: this.recordId
//     //         }
//     //     });
//     // }

//     handleViewAll() {
//         // var cmpDef = {
//         //     componentDef: "c:fileRecordsViewAllCmp",
//         //     attributes: {
//         //         propertyValue: "500"
//         //     },
//         //     state: {
//         //         c__recordId: this.recordId,
//         //         c__isShowAllData: true
//         //     }
//         // };
//         // let encodedDef = btoa(JSON.stringify(cmpDef));
//         // this[NavigationMixin.Navigate]({
//         //     type: 'standard__webPage',  
//         //     attributes: {
//         //         url: "/one/one.app#" + encodedDef
//         //     },
//         //     // state: {
//         //     //     recordId: this.recordId  
//         //     // } 
//         // });
//         this[NavigationMixin.Navigate]({
//             // Pass in pageReference
//             type: 'standard__component',
//             attributes: {
//                 componentName: 'c__fileRecordsViewAllCmp',
//             },
//             state: {
//                 c__recordId: this.recordId,
//             },
//         });
//     }
// }
/* eslint-disable */
import { LightningElement, api, track } from 'lwc';
import { NavigationMixin }    from 'lightning/navigation';
import { ShowToastEvent }     from 'lightning/platformShowToastEvent';
import LightningConfirm       from 'lightning/confirm';
import isSandbox              from '@salesforce/apex/mf123opp.isSandbox';
import uploadFiles            from "@salesforce/apex/AttachmentCompController.uploadFiles";
import deleteFile             from "@salesforce/apex/AttachmentCompController.deleteFile";
import getFilesOfRecordSF     from "@salesforce/apex/AttachmentCompController.getFilesOfRecordSF";
import fetchFileShareLink     from '@salesforce/apex/DropboxController.fetchFileShareLink';
import createFileShareLink    from '@salesforce/apex/DropboxController.createFileShareLink';
import getDropboxShareLink    from '@salesforce/apex/DropboxController.getDropboxShareLink';

export default class FileUploadSFCmp extends NavigationMixin(LightningElement) {
  @api recordId;
  @track isloading      = true;
  @track parentFiles    = [];
  @track childFiles     = [];
  @track files          = [];  // unused now, kept for compatibility
  @track hasFiles       = false;
  @track hasChildFiles  = false;
  @track dropdownVisible= false;
  basePath;

  get getTitle() {
    return `Files (${this.parentFiles.length})`;
  }

  get dropdownClass() {
    return 'slds-dropdown slds-dropdown_right';
  }

  connectedCallback() {
    this.retrieveFiles();
    isSandbox()
      .then(res => this.basePath = res ? '/mf/s/' : '/s/')
      .catch(err => console.error(err));
  }

  retrieveFiles() {
    this.isloading = true;
    getFilesOfRecordSF({ recordId: this.recordId, showAllData: false })
    .then(wrappers => {
      const baseUrl = `https://${location.host}`;
      // map wrappers -> flat file objects, include the linkedEntityId
      const all = wrappers.map(w => {
        const cd = w.contentDocument;
        const cdl= w.contentDocumentLink;
        const obj = {
          Id:                  cd.Id,
          Title:               cd.Title,
          Extension:           cd.FileExtension,
          isInternally:        cdl.Visibility === 'InternalUsers',
          ContentSize:         this.formatBytes(cd.ContentSize),
          ContentModifiedDate: this.formatDate(cd.ContentModifiedDate),
          CreatedByName:       `${cd.CreatedBy.FirstName||''} ${cd.CreatedBy.LastName}`,
          dropdownVisible:     false,
          DropboxUrl:          cd.LatestPublishedVersion?.Dropbox_Url__c,
          showViewExternal:    cd.FileExtension?.toLowerCase()==='xlsx'
                                 && cd.LatestPublishedVersion?.Dropbox_Url__c
        };
        // distribution vs. direct download
        if (cd.ContentDistributions?.length) {
          obj.DownloadUrl = cd.ContentDistributions[0].ContentDownloadUrl;
          obj.FileUrl     = cd.ContentDistributions[0].DistributionPublicUrl;
        } else {
          obj.DownloadUrl = `${baseUrl}/sfc/servlet.shepherd/document/download/${cd.Id}`;
          obj.FileUrl     = `${baseUrl}/sfc/servlet.shepherd/version/renditionDownload?rendition=THUMB720BY480&versionId=${cd.LatestPublishedVersionId}`;
        }
        obj.linkedEntityId = cdl.LinkedEntityId;
        return obj;
      });

      // split into parent vs. child
      this.parentFiles   = all.filter(f => f.linkedEntityId === this.recordId);
      this.childFiles    = all.filter(f => f.linkedEntityId !== this.recordId);
      this.hasFiles      = this.parentFiles.length > 0;
      this.hasChildFiles = this.childFiles.length > 0;
    })
    .catch(err => {
      this.displayToast('Error', err.body?.message||err.message, 'error');
      this.hasFiles = this.hasChildFiles = false;
    })
    .finally(() => {
      this.isloading = false;
    });
  }

  toggleDropdown(event) {
    const id = event.currentTarget.dataset.id;
    // parent?
    if (this.parentFiles.some(f=>f.Id===id)) {
      this.parentFiles = this.parentFiles.map(f=>({
        ...f,
        dropdownVisible: f.Id===id ? !f.dropdownVisible : false
      }));
    } else {
      this.childFiles = this.childFiles.map(f=>({
        ...f,
        dropdownVisible: f.Id===id ? !f.dropdownVisible : false
      }));
    }
  }

  async handleRowAction(event) {
    const action = event.currentTarget.dataset.name;
    const id     = event.currentTarget.dataset.id;
    // find in parent or child
    let row;
    if (this.parentFiles.some(f=>f.Id===id)) {
      row = this.parentFiles.find(f=>f.Id===id);
    } else {
      row = this.childFiles.find(f=>f.Id===id);
      // isChild = true;
    }
    // close dropdown
    this.toggleDropdown(event);

    switch(action) {
      case 'view':
        // preview in file page
        this[NavigationMixin.Navigate]({
          type: 'standard__namedPage',
          attributes: { pageName: 'filePreview' },
          state: {
            recordIds: id,
            selectedRecordId: id
          }
        });
        break;
      case 'download':
        window.location.href = row.DownloadUrl;
        break;
      case 'delete':
        const ok = await LightningConfirm.open({
          message: 'Are you sure you want to delete this file?',
          variant: 'header', label: 'Confirm Delete'
        });
        if (ok) {
          await deleteFile({ contentDocumentId: id });
          this.displayToast('Success','File deleted','success');
          this.retrieveFiles();
        }
        break;
      case 'View External':
        this.viewInExcelMode(row.Title, row.Extension, row.DropboxUrl, id);
        break;
    }
  }

  handleUploadFinished(evt) {
    const ids = evt.detail.files.map(f=>f.documentId);
    uploadFiles({ recordId: this.recordId, contentDocumentIds: ids, isInternal: false })
      .then(()=>{ this.displayToast('Success','Files uploaded','success'); this.retrieveFiles(); })
      .catch(err=> this.displayToast('Error', this.getWireError(err), 'error'));
  }

  handleInternalUpload(evt) {
    const ids = evt.detail.files.map(f=>f.documentId);
    uploadFiles({ recordId: this.recordId, contentDocumentIds: ids, isInternal: true })
      .then(()=>{ this.displayToast('Success','Files uploaded','success'); this.retrieveFiles(); })
      .catch(err=> this.displayToast('Error', this.getWireError(err), 'error'));
  }

    viewInExcelMode(fileName, fileExtension, filepath, fileId) {
        let completeFileName = fileName + '.' + fileExtension;
        console.log('completeFileName' + completeFileName);
        let completeFilePath = filepath + completeFileName;
        console.log('completeFilePath' + completeFilePath);

        getDropboxShareLink({ contentDocumentId: fileId })
            .then((existingLink) => {
                if (existingLink) {
                    this.isloading = false;
                    console.log('Using existing Dropbox link:', existingLink);
                    this.navigateToDropboxLink(existingLink);
                } else {
                    console.log('No existing Dropbox link. Creating new share link...');
                    createFileShareLink({ path: completeFilePath, contentDocumentId: fileId })
                        .then((newLink) => {
                            console.log('newLink '+newLink);
                            if (newLink === 'shared_link_exists') {
                                console.log('Share link already exists. Fetching existing link...');
                                //this.fetchFileShareLink(contentDocumentId);
                                fetchFileShareLink({ path: completeFilePath })
                                    .then((result) => { 
                                        if (result.url) {
                                            this.isloading = false;
                                            console.log('Dropbox URL:414 ', result.url);

                                            this[NavigationMixin.Navigate](
                                                {
                                                    type: 'standard__webPage',
                                                    attributes: {
                                                        url: result.url,
                                                    },
                                                },
                                                false
                                            );
                                        } else if (result.error) {
                                            this.showToast('Error', result.error, 'error');
                                        } else {
                                            this.showToast('Error', 'Unexpected response from server.', 'error');
                                        }
                                    })
                                    .catch((error) => {
                                        console.error('Error calling Apex method:', error);
                                        this.showToast('Error', 'Failed to open the file in edit mode.', 'error');
                                    });
                            } else if (newLink != 'shared_link_exists') {
                                this.isloading = false;
                                console.log('New Dropbox link created:', newLink);
                                this.navigateToDropboxLink(newLink); 
                            }
                             else {
                                this.showToast('Error', 'Failed to create Dropbox share link.', 'error');
                            }
                        })
                        .catch(() => {
                            this.showToast('Error', 'Failed to create Dropbox share link.', 'error');
                        });
                }
            })
            .catch((error) => {
                console.error('Error fetching Dropbox share link:', error);
                this.showToast('Error', 'Failed to fetch Dropbox share link.', 'error');
            });
    }
  

  formatBytes(bytes, decimals = 1) {
    if (!bytes) return '0 Bytes';
    const k = 1024, dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes','KB','MB','GB','TB'];
    const i = Math.floor(Math.log(bytes)/Math.log(k));
    return parseFloat((bytes/Math.pow(k,i)).toFixed(dm)) + ' ' + sizes[i];
  }

  formatDate(d) {
    return new Date(d).toLocaleDateString();
  }

  handleViewAll() {
    this[NavigationMixin.Navigate]({
      type: 'standard__component',
      attributes: { componentName: 'c__fileRecordsViewAllCmp' },
      state: { c__recordId: this.recordId }
    });
  }

  displayToast(title, msg, variant) {
    this.dispatchEvent(new ShowToastEvent({ title, message: msg, variant }));
  }

  getWireError(err) {
    if (Array.isArray(err.body)) {
      return err.body.map(e=>e.message).join(', ');
    }
    return err.body?.message||err.message;
  }
}