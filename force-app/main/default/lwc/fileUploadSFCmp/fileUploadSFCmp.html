<template>
    <div class="slds-card slds-theme_shade">
        <!-- CARD HEADER -->
        <div class="slds-card__header slds-grid slds-theme_shade">
            <header class="slds-media slds-media_center slds-has-flexi-truncate slds-theme_shade">
                <div class="slds-media__figure">
                    <lightning-icon icon-name="utility:file" size="small" alternative-text="Files Icon">
                    </lightning-icon>
                </div>
                <div class="slds-media__body slds-theme_shade">
                    <h2 class="slds-card__header-title">
                        <a href="javascript:void(0);" class="slds-card__header-link slds-truncate">
                            <span>{getTitle}</span>
                        </a>
                    </h2>
                </div>
                <div class="slds-no-flex slds-grid slds-gutters">
                    <div class="slds-col">
                        <lightning-file-upload record-id={recordId} label="Upload Internally"
                            onuploadfinished={handleInternalUpload} multiple>
                        </lightning-file-upload>
                    </div>
                    <div class="slds-col">
                        <lightning-file-upload record-id={recordId} label="Upload for All"
                            onuploadfinished={handleUploadFinished} multiple>
                        </lightning-file-upload>
                    </div>
                </div>
            </header>
        </div>

        <!-- CARD BODY -->
        <div class="slds-card__body">
            <!-- Spinner -->
            <template if:true={isloading}>
                <lightning-spinner alternative-text="Loading" size="small"></lightning-spinner>
            </template>

            <!-- CONTENT -->
            <template if:false={isloading}>
                <!-- PARENT FILES -->
                <template if:true={hasFiles}>
                    <table class="slds-table slds-border_top slds-table_cell-buffer parent-table">
                        <colgroup>
                            <col style="width:30%" />
                            <col style="width:20%" />
                            <col style="width:15%" />
                            <col style="width:20%" />
                            <col style="width:10%" />
                            <col style="width:5%" />
                        </colgroup>
                        <thead>
                            <tr class="slds-line-height_reset">
                                <th>
                                    <div class="slds-truncate heading-detail" title="Title">Title</div>
                                </th>
                                <th>
                                    <div class="slds-truncate heading-detail" title="Last Modified">Last Modified</div>
                                </th>
                                <th>
                                    <div class="slds-truncate heading-detail" title="Size">Size</div>
                                </th>
                                <th>
                                    <div class="slds-truncate heading-detail" title="Created By">Created By</div>
                                </th>
                                <th>
                                    <div class="slds-truncate heading-detail" title="Internal Only">Internal Only</div>
                                </th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            <template for:each={parentFiles} for:item="file">
                                <tr key={file.Id} class="slds-hint-parent">
                                    <td>
                                        <a href="javascript:void(0);" class="slds-text-link heading-value"
                                            data-name="view" data-id={file.Id} onclick={handleRowAction}>
                                            {file.Title}
                                        </a>
                                    </td>
                                    <td>
                                        <div class="slds-truncate" title={file.ContentModifiedDate}>
                                            {file.ContentModifiedDate}</div>
                                    </td>
                                    <td>
                                        <div class="slds-truncate" title={file.ContentSize}>{file.ContentSize}</div>
                                    </td>
                                    <td>
                                        <div class="slds-truncate" title={file.CreatedByName}>{file.CreatedByName}</div>
                                    </td>
                                    <td>
                                        <lightning-input type="checkbox" checked={file.isInternally} disabled>
                                        </lightning-input>
                                    </td>
                                    <td class="slds-text-align_right">
                                        <lightning-button-icon icon-name="utility:down" variant="border-filled"
                                            alternative-text="Settings" data-id={file.Id} onclick={toggleDropdown}>
                                        </lightning-button-icon>
                                        <template if:true={file.dropdownVisible}>
                                            <div class={dropdownClass}>
                                                <ul class="slds-dropdown__list" role="menu">
                                                    <li class="slds-dropdown__item">
                                                        <a href="javascript:void(0);" class="slds-text-link"
                                                            data-name="view" data-id={file.Id}
                                                            onclick={handleRowAction}>
                                                            View
                                                        </a>
                                                    </li>
                                                    <li class="slds-dropdown__item">
                                                        <a href="javascript:void(0);" class="slds-text-link"
                                                            data-name="download" data-id={file.Id}
                                                            onclick={handleRowAction}>
                                                            Download
                                                        </a>
                                                    </li>
                                                    <li class="slds-dropdown__item">
                                                        <a href="javascript:void(0);" class="slds-text-link"
                                                            data-name="delete" data-id={file.Id}
                                                            onclick={handleRowAction}>
                                                            Delete
                                                        </a>
                                                    </li>
                                                    <template if:true={file.showViewExternal}>
                                                        <li class="slds-dropdown__item">
                                                            <a href="javascript:void(0);" class="slds-text-link"
                                                                data-name="View External" data-id={file.Id}
                                                                onclick={handleRowAction}>
                                                                View In Excel
                                                            </a>
                                                        </li>
                                                    </template>
                                                </ul>
                                            </div>
                                        </template>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </template>

                <!-- CHILD FILES -->
                <template if:true={hasChildFiles}>
                    <h3 class="slds-text-title_caps slds-m-top_medium">Related Files ({childFiles.length})</h3>
                    <table class="slds-table slds-border_top slds-table_cell-buffer child-table">
                        <colgroup>
                            <col style="width:30%" />
                            <col style="width:20%" />
                            <col style="width:15%" />
                            <col style="width:20%" />
                            <col style="width:10%" />
                            <col style="width:5%" />
                        </colgroup>
                        <thead>
                            <tr class="slds-line-height_reset">
                                <th>
                                    <div class="slds-truncate heading-detail" title="Title">Title</div>
                                </th>
                                <th>
                                    <div class="slds-truncate heading-detail" title="Last Modified">Last Modified</div>
                                </th>
                                <th>
                                    <div class="slds-truncate heading-detail" title="Size">Size</div>
                                </th>
                                <th>
                                    <div class="slds-truncate heading-detail" title="Created By">Created By</div>
                                </th>
                                <th>
                                    <div class="slds-truncate heading-detail" title="Internal Only">Internal Only</div>
                                </th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            <template for:each={childFiles} for:item="file">
                                <tr key={file.Id} class="slds-hint-parent">
                                    <td>
                                        <a href="javascript:void(0);" class="slds-text-link heading-value"
                                            data-name="view" data-id={file.Id} onclick={handleRowAction}>
                                            {file.Title}
                                        </a>
                                    </td>
                                    <td>
                                        <div class="slds-truncate" title={file.ContentModifiedDate}>
                                            {file.ContentModifiedDate}</div>
                                    </td>
                                    <td>
                                        <div class="slds-truncate" title={file.ContentSize}>{file.ContentSize}</div>
                                    </td>
                                    <td>
                                        <div class="slds-truncate" title={file.CreatedByName}>{file.CreatedByName}</div>
                                    </td>
                                    <td>
                                        <lightning-input type="checkbox" checked={file.isInternally} disabled>
                                        </lightning-input>
                                    </td>
                                    <td class="slds-text-align_right">
                                        <lightning-button-icon icon-name="utility:down" variant="border-filled"
                                            alternative-text="Settings" data-id={file.Id} onclick={toggleDropdown}>
                                        </lightning-button-icon>
                                        <template if:true={file.dropdownVisible}>
                                            <div class={dropdownClass}>
                                                <ul class="slds-dropdown__list" role="menu">
                                                    <li class="slds-dropdown__item">
                                                        <a href="javascript:void(0);" class="slds-text-link"
                                                            data-name="view" data-id={file.Id}
                                                            onclick={handleRowAction}>
                                                            View
                                                        </a>
                                                    </li>
                                                    <li class="slds-dropdown__item">
                                                        <a href="javascript:void(0);" class="slds-text-link"
                                                            data-name="download" data-id={file.Id}
                                                            onclick={handleRowAction}>
                                                            Download
                                                        </a>
                                                    </li>
                                                    <li class="slds-dropdown__item">
                                                        <a href="javascript:void(0);" class="slds-text-link"
                                                            data-name="delete" data-id={file.Id}
                                                            onclick={handleRowAction}>
                                                            Delete
                                                        </a>
                                                    </li>
                                                    <template if:true={file.showViewExternal}>
                                                        <li class="slds-dropdown__item">
                                                            <a href="javascript:void(0);" class="slds-text-link"
                                                                data-name="View External" data-id={file.Id}
                                                                onclick={handleRowAction}>
                                                                View In Excel
                                                            </a>
                                                        </li>
                                                    </template>
                                                </ul>
                                            </div>
                                        </template>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </template>

                <template if:false={hasFiles}>
                    <p>No records to show...</p>
                </template>
            </template>
        </div>

        <!-- CARD FOOTER -->
        <div class="slds-card__footer">
            <a onclick={handleViewAll}>View All</a>
        </div>
    </div>
</template>