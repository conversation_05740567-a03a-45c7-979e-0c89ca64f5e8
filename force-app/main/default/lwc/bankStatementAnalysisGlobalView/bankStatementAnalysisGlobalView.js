/* eslint-disable */
import { LightningElement, track, api, wire } from 'lwc';
import getBankStatementAnalysis from '@salesforce/apex/BankStatementAnalysisGVController.getStatementData';
import getReport from '@salesforce/apex/ReportController.getReport';
import { NavigationMixin } from 'lightning/navigation';
const MONTHS = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'July', 'Aug', 'Sept', 'Oct', 'Nov', 'Dec'];


export default class BankStatementAnalysisGlobalView extends NavigationMixin(LightningElement) {

    @api recordId;
    @api lastNMonths = 6;
    @api emptyCellPlaceholder = '-';

    serverCallError = false;
    noDataFound = false;
    dataLoading = true;

    @track error;
    @track months;
    @track statementAnalysisData;
    @track totalFeeOfEachMonth = {};
    @track creditAnalysisCategories = [];
    @track debitAnalysisCategories = [];
    @track balanceBefore6Month;
    @track isExistBalanceBefore6Month = false;
    @track selectedCategory = 'MF_Category_Manual_Override__c';
    @track transactionForReport;
    get categoryOptions() {
        return [
            { label: 'AI Category', value: 'AI_Category__c' },
            { label: 'MF Category Manual Override', value: 'MF_Category_Manual_Override__c' },
            { label: 'MF Category', value: 'MF_Category__c' }
        ];
    }

    @wire(getBankStatementAnalysis, { recordId: '$recordId', lastNMonths: '$lastNMonths', category: '$selectedCategory' })
    wiredBankStatementAnalysis(result) {
        
        this.dataLoading = true;
        const { error, data } = result;


        console.log('result => ', JSON.stringify(result));


        if (data) {
                console.log('enter data if');
            if (Object.entries(data).length !== 0) {
                console.log('enter object if');
                this.months = JSON.parse(JSON.stringify(data.months)).reverse();
                this.creditAnalysisCategories = JSON.parse(JSON.stringify(data.creditAnalysisCategories))
                    .map(category => ({ 'category': category.category, 'viewOrder': category.viewOrder, 'values': this.initializeAnArray(this.months.length, this.emptyCellPlaceholder) }));
                this.debitAnalysisCategories = JSON.parse(JSON.stringify(data.debitAnalysisCategories))
                    .map(category => ({ 'category': category.category, 'viewOrder': category.viewOrder, 'values': this.initializeAnArray(this.months.length, this.emptyCellPlaceholder) }));
                this.transactionForReport = JSON.parse(JSON.stringify(data.transactions));
                const transactions = JSON.parse(JSON.stringify(data.transactions));
                if (data.hasOwnProperty('initialTransaction')) {
                    const initialTransaction = JSON.parse(JSON.stringify(data.initialTransaction));
                    console.log('reached at process transaction');
                    this.processInitialTransaction(transactions, initialTransaction);
                }
                this.startStatementAnalysis(transactions);
            } else {
                this.dataLoading = false;
                this.noDataFound = true;
            }

        } else if (error) {

            this.error = error.body.message;
            this.dataLoading = false;
            this.serverCallError = true;


        }
    }

    processInitialTransaction(transactions, initialTransaction) {
        transactions.sort((a, b) => b.orderNumber - a.orderNumber);
        const firstTransaction = transactions[0];
        console.log('firstTransaction -', JSON.stringify(firstTransaction));
        const firstTransactionMonth = this.getMonthFromTransactionDate(firstTransaction.transactionDate) - 1;
        console.log(' if outside');
        console.log('MONTHS[firstTransactionMonth]'+MONTHS[firstTransactionMonth]);
        console.log('this.months[0]'+this.months[0]);
        if (MONTHS[firstTransactionMonth] == this.months[0]) {
            console.log('enter if inside');
            if(initialTransaction.credit != null) {
                console.log('enter if');
                console.log('initialTransaction.credit '+initialTransaction.credit );
                console.log('initialTransaction.debit '+initialTransaction.debit );
                console.log('initialTransaction.balance '+initialTransaction.balance );
                this.balanceBefore6Month = (initialTransaction.balance - initialTransaction.credit).toFixed(2);
            } else{
                   this.balanceBefore6Month = (initialTransaction.balance + initialTransaction.debit).toFixed(2);
                   
            }
            this.isExistBalanceBefore6Month = true;
        } else { 
            this.balanceBefore6Month = '-';
            console.log('enter if esle'); 
        }

    }

    getMonthFromTransactionDate(transactionDate) {
        const regex = /^(\d{4})-(\d{2})-(\d{2})$/;
        const match = transactionDate.match(regex);

        if (match) {
            const month = parseInt(match[2], 10); 
            return month;
        } else {
            console.error('Invalid date format');
            return null; 
        }
    }


    /**
     * Initializes an array with a specified length and delimiter.
     * @param {number} length - The length of the array.
     * @param {any} delimiter - The value to fill the array with.
     * @returns {Array} - The initialized array.
     */
    initializeAnArray(length, delimiter) {
        return Array.from({ length }, () => delimiter);
    }

    /**
     * Formats a number into USD currency format.
     * @param {number} number - The number to format.
     * @returns {string} - The formatted number in USD.
     */

    formattedNumberInUSD(number) {
        if (number === '-') {
            return '-'; 
        }
        const formatter = new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            maximumFractionDigits: 2,
            minimumFractionDigits: 2
        });
        return formatter.format(number);
    }


    /**
     * Extracts the month name from a date string.
     * @param {string} dateString - The date string in 'YYYY-MM-DD' format.
     * @returns {string} - The month name.
     */
    getMonthName(dateString) {
        const date = new Date(dateString);
        const month = this.getMonthFromTransactionDate(date) - 1;
        return MONTHS[month];
    }

    /**
     * Starts the statement analysis.
     * @param {Array} transactions - Array of transaction objects.
     */
    startStatementAnalysis(transactions) {

        const analysisData = {};

        const beginningBalanceOfEachMonth = this.initializeAnArray(this.months.length, this.emptyCellPlaceholder);
        const totalCreditOfEachMonth = this.initializeAnArray(this.months.length, this.emptyCellPlaceholder);
        const totalDebitOfEachMonth = this.initializeAnArray(this.months.length, this.emptyCellPlaceholder);
        const totalFeeOfEachMonth = this.initializeAnArray(this.months.length, this.emptyCellPlaceholder);
        const endingBalanceOfEachMonth = this.initializeAnArray(this.months.length, this.emptyCellPlaceholder);
        const numberOfNFSODReturnOfEachMonth = this.initializeAnArray(this.months.length, this.emptyCellPlaceholder);
        const totalTransferOfEachMonth = this.initializeAnArray(this.months.length, this.emptyCellPlaceholder);
        const transferCreditPercentageOfEachMonth = this.initializeAnArray(this.months.length, this.emptyCellPlaceholder);
        const atmWithdrawalOfEachMonth = this.initializeAnArray(this.months.length, this.emptyCellPlaceholder);

        let creditAnalysis = [];
        let debitAnalysis = [];
        const statementsGroupedByMonthWithAllData = {};


        const statementsGroupedByMonth = Object.groupBy(transactions, (value) => {
            const date = value.transactionDate;
            const month = this.getMonthFromTransactionDate(date) - 1;
            return MONTHS[month];

        });

        this.months.forEach((month) => {
            statementsGroupedByMonthWithAllData[month] = statementsGroupedByMonth[month] || [];
        });

        Object.entries(statementsGroupedByMonthWithAllData).forEach(([month, statements]) => {

            const monthIndex = this.months.indexOf(month);

            if (monthIndex === -1) {
                console.warn(`Month "${month}" not found in this.months array.`);
                return;
            }

            let beginningBalance;
            debugger;
            if (statements[0] != null && monthIndex == 0) {


                beginningBalanceOfEachMonth[monthIndex] = this.calculateBeginningBalanceOfEachMonth(statements, monthIndex > 0 ? endingBalanceOfEachMonth[monthIndex - 1] : null);
                // if (monthIndex === 0) {
                //     beginningBalance = this.parseUSD(this.balanceBefore6Month, false);
                //     beginningBalanceOfEachMonth[monthIndex] = this.balanceBefore6Month === '-' ? '-' : '$' + this.parseUSD(this.balanceBefore6Month, false);

                // } else {
                //     beginningBalance = this.parseUSD(beginningBalanceOfEachMonth[monthIndex], false);
                // }
                beginningBalance = beginningBalanceOfEachMonth[monthIndex] ? this.parseUSD(beginningBalanceOfEachMonth[monthIndex], false) :  '-';
            }
            else {
                if (monthIndex >= 1) {
                    console.log('enter 1 month '+monthIndex);
                    beginningBalanceOfEachMonth[monthIndex] = this.calculateBeginningBalanceOfEachMonth(statements, monthIndex > 0 ? endingBalanceOfEachMonth[monthIndex - 1] : null);
                    beginningBalance = this.parseUSD(beginningBalanceOfEachMonth[monthIndex], false);
                } else if (monthIndex == 0) {

                    beginningBalanceOfEachMonth[monthIndex] = this.balanceBefore6Month ? this.formattedNumberInUSD(this.balanceBefore6Month) :'-';
                    beginningBalance = this.balanceBefore6Month ? this.balanceBefore6Month :'-';

                }
            }


            totalCreditOfEachMonth[monthIndex] = this.calculateTotalCreditOfEachMonth(statements);
            const totalCreditAmount = this.parseUSD(totalCreditOfEachMonth[monthIndex], false);
            totalDebitOfEachMonth[monthIndex] = this.calculateTotalDebitOfEachMonth(statements);
            const totalDebitAmount = this.parseUSD(totalDebitOfEachMonth[monthIndex], false);
            totalFeeOfEachMonth[monthIndex] = this.calculateTotalFeeOfEachMonth(statements);
            const totalFeeAmount = this.parseUSD(totalFeeOfEachMonth[monthIndex], false);
            endingBalanceOfEachMonth[monthIndex] = this.calculateEndingBalanceOfEachMonth(beginningBalance, totalCreditAmount, totalDebitAmount, totalFeeAmount);
            numberOfNFSODReturnOfEachMonth[monthIndex] = this.calculateNumberOfNFSODReturnOfEachMonth();
            totalTransferOfEachMonth[monthIndex] = this.calculateTotalTransferOfEachMonth(statements);
            const transferAmount = this.parseUSD(totalTransferOfEachMonth[monthIndex], false);

            transferCreditPercentageOfEachMonth[monthIndex] = this.calculateTransferCreditPercentageOfEachMonth(transferAmount, totalCreditAmount);
            atmWithdrawalOfEachMonth[monthIndex] = this.calculateATMWithdrawalOfEachMonth(statements);
            creditAnalysis = this.calculateAmountForCategoryOfEachMonth(creditAnalysis, statements, monthIndex, 'credit');
            debitAnalysis = this.calculateAmountForCategoryOfEachMonth(debitAnalysis, statements, monthIndex, 'debit');
        });

        analysisData['beginningBalance'] = [...beginningBalanceOfEachMonth];
        analysisData['totalCredit'] = [...totalCreditOfEachMonth];
        analysisData['totalDebit'] = [...totalDebitOfEachMonth];
        analysisData['totalFee'] = [...totalFeeOfEachMonth];
        analysisData['endingBalance'] = [...endingBalanceOfEachMonth];
        analysisData['totalNFSODReturn'] = [...numberOfNFSODReturnOfEachMonth];
        analysisData['totalTransfer'] = [...totalTransferOfEachMonth];
        analysisData['totalTransferCreditPercentage'] = [...transferCreditPercentageOfEachMonth];
        analysisData['totalATMWithdrawal'] = [...atmWithdrawalOfEachMonth];
        analysisData['creditAnalysis'] = [...this.mergeCategories(this.creditAnalysisCategories, creditAnalysis).sort((a, b) => a.viewOrder - b.viewOrder)];
        analysisData['debitAnalysis'] = [...this.mergeCategories(this.debitAnalysisCategories, debitAnalysis).sort((a, b) => a.viewOrder - b.viewOrder)];

        if (Object.entries(analysisData).length > 0) {
            this.statementAnalysisData = { ...analysisData };
            this.dataLoading = false;
        } else {
            this.dataLoading = false;
            this.noDataFound = true;
        }

    }

    mergeCategories(fullCategories, partialCategories) {
        const merged = fullCategories.map(category => {
            const matchingPartial = partialCategories.find(p => p.category === category.category);
            return {
                category: category.category,
                viewOrder: category.viewOrder,
                values: category.values.map((value, index) =>
                    matchingPartial?.values[index] !== undefined ? matchingPartial.values[index] : value
                )
            };
        });
        partialCategories.forEach(p => {
            const exists = fullCategories.some(f => f.category === p.category);
            if (!exists) {
                merged.push(p);
            }
        });

        return merged;
    }

    calculateAmountForCategoryOfEachMonth(analysis, statements, monthIndex, type) {
        const knownCategories = (type === 'credit' ? this.creditAnalysisCategories : this.debitAnalysisCategories).map(cat => cat.category);

        statements.forEach((statement) => {
            const amount = statement[type];
            const analysisType = statement.analysisType;
            let mfCategory = statement.mfCategory?.trim();

            if (typeof amount === 'number' && analysisType?.toLowerCase() === type.toLowerCase()) {
                if (!mfCategory || !knownCategories.includes(mfCategory)) {
                    mfCategory = type === 'credit' ? 'Miscellaneous' : 'Overhead';
                }

                let categoryObj = analysis.find((cat) => cat.category === mfCategory);
                if (!categoryObj) {
                    categoryObj = {
                        category: mfCategory,
                        values: this.initializeAnArray(this.months.length, this.emptyCellPlaceholder)
                    };
                    analysis.push(categoryObj);
                }

                let existingValue = this.parseUSD(categoryObj.values[monthIndex], true);
                existingValue += amount;
                categoryObj.values[monthIndex] = this.formattedNumberInUSD(existingValue);
            }
        });

        return analysis;
    }




    /**
     * Parses a USD formatted string back to a number.
     * @param {string} usdString - The USD formatted string (e.g., "$1,234.56").
     * @returns {number} - The numerical value.
     */


    parseUSD(usdString, returnZeroInsteadOfDash) {
        if (usdString === '-' || usdString === null || usdString === undefined || usdString === '') {
            return returnZeroInsteadOfDash ? 0 : '-';
        }
        if (typeof usdString === 'number') {
            return usdString;
        }

        const parsed = parseFloat(usdString.replace(/[^0-9.-]+/g, ''));
        return isNaN(parsed) ? (returnZeroInsteadOfDash ? 0 : '-') : parsed;
    }

    /**
     * Calculates the beginning balance for the month.
     * @param {Array} statements - Array of transaction objects for the month.
     * @returns {string} - Formatted USD string of the beginning balance.
     */


    calculateBeginningBalanceOfEachMonth(statements, endingBalanceOfPreviousMonth) {
        
        console.log('statements '+JSON.stringify(statements));
        if (!endingBalanceOfPreviousMonth || endingBalanceOfPreviousMonth == '-') {
            if (!statements || statements.length === 0 || statements[0] === undefined) {
                console.log('enter if');
                return '-';
            }
             statements.sort((a, b) => b.orderNumber - a.orderNumber);
               console.log('statements 2'+JSON.stringify(statements));
            let { balance, debit, credit } = statements[statements.length - 1];
            console.log('statement '+JSON.stringify(statements[statements.length - 1]));
            let calculatedBalance;
            if(debit){ calculatedBalance = balance+debit;
            console.log('if calc '+calculatedBalance);}
            else{ calculatedBalance = balance-credit;
            console.log('else calc '+calculatedBalance);}
            
            return this.formattedNumberInUSD(calculatedBalance);
        }
        
        return endingBalanceOfPreviousMonth;
    }



    /**
     * Calculates the total credit for the month.
     * @param {Array} statements - Array of transaction objects for the month.
     * @returns {string} - Formatted USD string of the total credit.
     */
    calculateTotalCreditOfEachMonth(statements) {
        if (statements.length === 0) return '-';
        let totalCredit = statements.reduce((total, statement) => total + (statement.credit ? statement.credit : 0), 0);
        return this.formattedNumberInUSD(totalCredit);
    }

    /**
     * Calculates the total debit for the month.
     * @param {Array} statements - Array of transaction objects for the month.
     * @returns {string} - Formatted USD string of the total debit.
     */

    calculateTotalDebitOfEachMonth(statements) {
        if (!statements || statements.length === 0) return '-';
        let totalDebit = statements.reduce((total, statement) => total + (statement.debit ? statement.debit : 0), 0);
        return this.formattedNumberInUSD(totalDebit);
    }

    /**
     * Calculates the total fee for the month based on category or subCategory matching 'fee'.
     * @param {Array} statements - Array of transaction objects for the month.
     * @returns {string} - Formatted USD string of the total fee.
     */

    calculateTotalFeeOfEachMonth(statements) {
        if (!statements || statements.length === 0) return '-';
        const feeRegex = /\bfee|fees\b/i;
        let totalFee = statements.reduce((total, statement) => {
            const isFee = feeRegex.test(statement.category) || feeRegex.test(statement.subCategory);
            return total + (isFee && statement.debit ? statement.debit : 0);
        }, 0);
        return totalFee > 0 ? this.formattedNumberInUSD(totalFee) : this.emptyCellPlaceholder;
    }

    /**
     * Calculates the ending balance for the month.
     * @param {string} beginningBalance - Formatted USD string of the beginning balance.
     * @param {number} totalCreditAmount - Total credit amount as a number.
     * @param {number} totalDebitAmount - Total debit amount as a number.
     * @param {number} totalFeeAmount - Total fee amount as a number.
     * @returns {string} - Formatted USD string of the ending balance.
     */


    calculateEndingBalanceOfEachMonth(beginningBalance, totalCreditAmount, totalDebitAmount, totalFeeAmount) {
        const hasAllEmpty = [beginningBalance, totalCreditAmount, totalDebitAmount].every(v => v === '-');

        if (hasAllEmpty) {
            return '-'; 
        }

        const bBal = beginningBalance === '-' ? 0 : beginningBalance;
        const credit = totalCreditAmount === '-' ? 0 : totalCreditAmount;
        const debit = totalDebitAmount === '-' ? 0 : totalDebitAmount;

        const endingBalance = bBal + credit - debit;
        return this.formattedNumberInUSD(endingBalance);

    }


    /**
     * Calculates the number of NFSOD returns for the month.
     * Placeholder implementation.
     * @param {Array} statements - Array of transaction objects for the month.
     * @returns {string} - Formatted USD string representing the number of NFSOD returns.
     */
    calculateNumberOfNFSODReturnOfEachMonth() {
        let totalNFSODReturns = '-';
        return totalNFSODReturns;
    }

    /**
     * Calculates the total transfer for the month based on category or subCategory matching 'transfer'.
     * @param {Array} statements - Array of transaction objects for the month.
     * @returns {string} - Formatted USD string of the total transfer.
     */
    calculateTotalTransferOfEachMonth(statements) {
        const transferRegex = /\btransfer\b/i;
        let totalTransfers = statements.reduce((total, statement) => {
            const isTransfer = transferRegex.test(statement.category) || transferRegex.test(statement.subCategory) || transferRegex.test(statement.description);
            if (isTransfer) {
                if (statement.debit) {
                    return total + (-statement.debit);
                } else if (statement.credit) {
                    return total + statement.credit;
                }
            }
            return total;
        }, 0);
        return totalTransfers > 0 ? this.formattedNumberInUSD(totalTransfers) : '-';
    }

    /**
     * Calculates the transfer credit percentage for the month.
     * @param {Array} statements - Array of transaction objects for the month.
     * @returns {string} - Percentage string.
     */
    calculateTransferCreditPercentageOfEachMonth(transferAmount, totalCreditAmount) {
        if (transferAmount === '-' || totalCreditAmount === '-') return '-';
        const totalTransferCreditPercentage = `${(((transferAmount !== 0) && (totalCreditAmount !== 0)) ? ((transferAmount / totalCreditAmount) * 100).toFixed(2) : (0.00).toFixed(2))}%`;

        return totalTransferCreditPercentage == '0.00%' ? '-' : totalTransferCreditPercentage;
    }

    /**
     * Calculates the total ATM withdrawals for the month based on matching 'atm withdrawal' or 'atm withdrawals'.
     * @param {Array} statements - Array of transaction objects for the month.
     * @returns {string} - Formatted USD string of the total ATM withdrawals.
     */
    calculateATMWithdrawalOfEachMonth(statements) {
        const atmWithdrawalRegex = /\bwithdrawal|withdrawals?\b/i;
        let totalATMWithdrawals = statements.reduce((total, statement) => {
            const isATMWithdrawal = atmWithdrawalRegex.test(statement.category) || atmWithdrawalRegex.test(statement.subCategory) || atmWithdrawalRegex.test(statement.description);
            return total + (isATMWithdrawal ? statement.debit ? statement.debit : statement.credit ? statement.credit : 0 : 0);
        }, 0);
        return totalATMWithdrawals > 0 ? this.formattedNumberInUSD(totalATMWithdrawals) : '-';
    }

    /**
     * Aggregates credit amounts per category and subCategory for each month.
     * @param {Array} creditAnalysis - The current creditAnalysis array.
     * @param {Array} statements - Array of transaction objects for the current month.
     * @param {number} monthIndex - The index of the current month in the months array.
     * @returns {Array} - Updated creditAnalysis array.
     */
    calculateCreditAmountForCategoryAndSubCategoryOfEachMonth(creditAnalysis, statements, monthIndex) {
        statements.forEach(statement => {
            if (statement.credit && typeof statement.credit === 'number' && statement.credit > 0) {
                const category = statement.category.trim();
                const subCategory = statement.subCategory ? statement.subCategory.trim() : '-';

                let categoryObj = creditAnalysis.find(cat => cat.category === category);
                if (!categoryObj) {
                    categoryObj = { category, subCategories: [] };
                    creditAnalysis.push(categoryObj);
                }

                let subCategoryObj = categoryObj.subCategories.find(sub => sub.subCategory === subCategory);
                if (!subCategoryObj) {
                    subCategoryObj = { subCategory, values: this.initializeAnArray(this.months.length, this.emptyCellPlaceholder) };
                    categoryObj.subCategories.push(subCategoryObj);
                }

                let existingValue = this.parseUSD(subCategoryObj.values[monthIndex], true);
                existingValue += statement.credit;
                subCategoryObj.values[monthIndex] = this.formattedNumberInUSD(existingValue);
            }
        });

        return creditAnalysis;
    }


    /**
     * Aggregates credit amounts per category and subCategory for each month.
     * @param {Array} creditAnalysis - The current creditAnalysis array.
     * @param {Array} statements - Array of transaction objects for the current month.
     * @param {number} monthIndex - The index of the current month in the months array.
     * @returns {Array} - Updated creditAnalysis array.
     */
    calculateDebitAmountForCategoryAndSubCategoryOfEachMonth(debitAnalysis, statements, monthIndex) {
        statements.forEach(statement => {
            if (statement.debit && typeof statement.debit === 'number' && statement.debit > 0) {
                const category = statement.category.trim();
                const subCategory = statement.subCategory ? statement.subCategory.trim() : 'Undefined SubCategory';

                let categoryObj = debitAnalysis.find(cat => cat.category === category);
                if (!categoryObj) {
                    categoryObj = { category, subCategories: [] };
                    debitAnalysis.push(categoryObj);
                }

                let subCategoryObj = categoryObj.subCategories.find(sub => sub.subCategory === subCategory);
                if (!subCategoryObj) {
                    subCategoryObj = { subCategory, values: this.initializeAnArray(this.months.length, this.emptyCellPlaceholder) };
                    categoryObj.subCategories.push(subCategoryObj);
                }

                let existingValue = this.parseUSD(subCategoryObj.values[monthIndex], true);
                existingValue += statement.debit;
                subCategoryObj.values[monthIndex] = this.formattedNumberInUSD(existingValue);
            }
        });

        return debitAnalysis;
    }

    /**
     * Escapes special characters in a string for use in a regex pattern.
     * @param {string} string - The input string to escape.
     * @returns {string} - The escaped string.
     */
    escapeRegex(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); 
    }

    handleCategoryChange(event) {

        this.selectedCategory = event.detail.value;
        this.dataLoading = true;
    }

    handleCellClick(event) {
        const attributeName = event.target.getAttribute('data-attribute');
        const selectedMonthIndex = event.target.getAttribute('data-index');
        let category = '';
        if (attributeName === 'Debit Amount' || attributeName === 'Credit Amount') {
            category = event.target.getAttribute('data-cat');
        }

        const selectedMonth = this.months[selectedMonthIndex];
        const index = MONTHS.indexOf(selectedMonth) + 1;
        
        console.log('enter');
        const transactionsForMonth = this.transactionForReport.filter(transaction => {
            const transactionMonth = this.getMonthFromTransactionDate(transaction.transactionDate);
            return transactionMonth === index;  
        });
        const date = new Date(transactionsForMonth[0].transactionDate);
        const year = date.getFullYear();
        const month = this.getMonthFromTransactionDate(transactionsForMonth[0].transactionDate)-1;
        const firstDateOfCurrentMonth = new Date(year, month, 1);

        const firstDateOfNextMonth = new Date(year, month + 1, 1);
        let reportId;
        console.log('attribute'+attributeName);
        if (attributeName === 'Total') {
            getReport({ reportName: 'Transactions by month' })
                .then((result) => {
                    reportId = result;
                    console.log('enter result');
                    this.navigateToReport(reportId, firstDateOfCurrentMonth, firstDateOfNextMonth);

                })
                .catch((error) => {
                    this.error = error;
                    console.error('Error retrieving report:', error);
                });
        } else if (attributeName === 'Debit Amount') {

            getReport({ reportName: 'trans by category with trans date debit' })
                .then((result) => {
                    reportId = result;
                    if (category === 'Overhead') {
                        this.navigateToReportsingle(reportId, firstDateOfCurrentMonth, firstDateOfNextMonth, '', category);
                    } else {
                        this.navigateToReportsingle(reportId, firstDateOfCurrentMonth, firstDateOfNextMonth, category, category);
                    }
                })
                .catch((error) => {
                    this.error = error;
                    console.error('Error retrieving report:', error);
                });
        } else if (attributeName === 'Credit Amount') {

            getReport({ reportName: 'trans by category with trans date credit' })
                .then((result) => {
                    reportId = result;
                    if (category === 'Miscellaneous') {
                        this.navigateToReportsingle(reportId, firstDateOfCurrentMonth, firstDateOfNextMonth, '', category);
                    } else {
                        this.navigateToReportsingle(reportId, firstDateOfCurrentMonth, firstDateOfNextMonth, category, category);
                    }
                })
                .catch((error) => {
                    this.error = error;
                    console.error('Error retrieving report:', error);
                });
        }

    }


    navigateToReport(reportId, firstDateOfCurrentMonth, firstDateOfNextMonth) {

        const formattedFirstDateOfCurrentMonth = firstDateOfCurrentMonth.toISOString().split('T')[0];
        const formattedFirstDateOfNextMonth = firstDateOfNextMonth.toISOString().split('T')[0];

        const baseURL = window.location.origin;
        const reportUrl = `${baseURL}/lightning/r/Report/${reportId}/view?fv0=${formattedFirstDateOfCurrentMonth}&fv1=${formattedFirstDateOfNextMonth}&fv2=${this.recordId}`;

        this[NavigationMixin.Navigate]({
            type: 'standard__webPage',
            attributes: {
                url: reportUrl
            }
        });
    }

    navigateToReportsingle(reportId, firstDateOfCurrentMonth, firstDateOfNextMonth, category, category1) {

        const formattedFirstDateOfCurrentMonth = firstDateOfCurrentMonth.toISOString().split('T')[0];
        const formattedFirstDateOfNextMonth = firstDateOfNextMonth.toISOString().split('T')[0];
        const baseURL = window.location.origin;
        const reportUrl = `${baseURL}/lightning/r/Report/${reportId}/view?fv0=${formattedFirstDateOfCurrentMonth}&fv1=${formattedFirstDateOfNextMonth}&fv2=${this.recordId}&fv3=${category}&fv4=${category1}&fv5=${category1}&fv6=${category1}`;

        this[NavigationMixin.Navigate]({
            type: 'standard__webPage',
            attributes: {
                url: reportUrl
            }
        });
    }

}