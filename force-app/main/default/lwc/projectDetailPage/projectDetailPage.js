/* eslint-disable */
import { LightningElement, track, wire,  api } from 'lwc';
import { CurrentPageReference } from 'lightning/navigation';
import getProjectsForOpportunity from '@salesforce/apex/mf123opp.getProjectsForOpportunity';
import { NavigationMixin } from "lightning/navigation";
import getOpprOfProject from '@salesforce/apex/mf123opp.getOpprOfProject';
import isSandbox from '@salesforce/apex/mf123opp.isSandbox';
import getDisbursementRequests from '@salesforce/apex/mf123opp.getDisbursementRequests';

export default class ProjectDetailPage extends  NavigationMixin(LightningElement) {
    @api isStandardRecordDetailPage;
    @api pageName;
    @track headingLabel;
    //@track headingValue;

    @track projectId;
    @track projName;
    @track projects = [];
    @track oppId;
    @track projectDetails = {};
    @track disbursementRequests = [];
    @track disReqLabel;
    isSandbox;

    // columns = [
    //     { label: 'Disbursement Request', fieldName: 'name', type: 'text', hideDefaultActions: true },
    //     { label: 'Amount Approved', fieldName: 'amountApproved', type: 'currency', hideDefaultActions: true },
    //     { label: 'Disbursement Type', fieldName: 'disbursementType', type: 'text', hideDefaultActions: true},
    //     { label: 'Last Modified Date', fieldName: 'lastModifiedDate', type: 'date', hideDefaultActions: true }
    // ];

    connectedCallback() {
        isSandbox()
            .then((result) => {
                this.isSandbox = result ? '/mf/s/' : '/s/';
            })
            .catch((error) => {
                console.error('Error checking sandbox status:', JSON.stringify(error));
            });
            
        console.log('in pdf '+this.isStandardRecordDetailPage);
        console.log('in pdf '+this.pageName);
        this.headingLabel = this.pageName;
    }

    get df(){
        return this.headingLabel === 'Project' ? true : false;
    }

    @wire(CurrentPageReference)
    getPageReference(currentPageReference) {
        if (currentPageReference) {
            console.log('currentPageReference in pd '+JSON.stringify(currentPageReference));
            const { attributes, state } = currentPageReference;
            const projectIdFromUrl = attributes.recordId;
            console.log('projectIdFromUrl edwjrf '+projectIdFromUrl);

            //const { state } = currentPageReference;
            // const headingValue = state.recordName;
            // console.log('headingValue '+headingValue);
            // if(headingValue){
            //     this.headingValue = headingValue;
            // }

            if (projectIdFromUrl) {
                this.projectId = projectIdFromUrl;
                console.log('765467 '+this.projectId);
                
                this.fetchOppId(this.projectId);
                //this.fetchDisbursementRequests(this.projectId);
            }
        }
    }

    fetchOppId(projId) {
        console.log('fetchOppId '+this.pageName);
        getOpprOfProject({ projectId : projId, pgName : this.pageName })
        .then(result => {
            console.log('result#$%^& '+JSON.stringify(result));
            //console.log('dfr '+result[0].Loan_Opportunity__r.Id);
            // this.projName = result[0].Name;
            //this.oppId = result[0].Loan_Opportunity__r.Id;
            //this.fetchProjects(this.oppId);
            this.projectDetails = {
                    projName: result[0].Name
                    // accountName: result[0].Account_Name__r.Name,
                    // owner: result[0].Owner.FirstName + ' ' + result[0].Owner.LastName,
                    // loanStatus: result[0].Loan_Status__c,
                    // loanOpportunity: result[0].Loan_Opportunity__r.Name,
                    // assignedMfUw: result[0].Assigned_MF_Servicer__r.FirstName + ' ' + result[0].Assigned_MF_Servicer__r.LastName,
                    // mfProjectNumber: result[0].Project_Number__c,
                    // projectStartDate: result[0].Project_Start_Date__c,
                    // loanMaturityDate: result[0].Loan_Maturity_Date__c
                };
        })
        .catch(error => {
            console.error('Error fetching projects in header :', JSON.stringify(error));
        });
    }

    handleQuickAction(event) {
        const projectId = this.projectId;
        // event.target.dataset.id;
        this[NavigationMixin.Navigate]({
            type: 'standard__webPage',
            attributes: {
                url: `${this.isSandbox}disbursement-request?recordId=${projectId}`
            }
        });
    }

    handleViewTransaction() {
         const projectId = this.projectId; 
        console.log('view transactions called');
        this[NavigationMixin.Navigate]({
            type: 'standard__webPage',
            attributes: {
                url: `${this.isSandbox}transaction-list?recordId=${projectId}`
            }
        });
    }

    // fetchDisbursementRequests(projId) {
    //     getDisbursementRequests({ projectId: projId })
    //         .then(result => {
    //             console.log('disbursement request result '+JSON.stringify(result));
    //             this.disbursementRequests = result.map(req => ({
    //                 id: req.Id,
    //                 name: req.Name,
    //                 amountApproved: req.Amount_Approved__c,
    //                 disbursementType: req.Disbursement_Type__c,
    //                 lastModifiedDate: req.LastModifiedDate
    //             }));
    //             console.log('324rfew '+this.disbursementRequests.length);
    //             this.disReqLabel = this.disbursementRequests.length ? 'Disbursement Request (' + this.disbursementRequests.length +')' :  'Disbursement Request';
    //         })
    //         .catch(error => {
    //             console.error('Error fetching disbursement requests:', JSON.stringify(error));
    //         });
    // }


    // fetchProjects(opprId) {
    //     getProjectsForOpportunity({ opportunityId: opprId })
    //         .then(result => {
    //             console.log('this.projects '+this.projects);
    //             this.projects = result.map(item => ({
    //                 Id: item.project.Id,
    //                 Name: item.project.Name,
    //                 Amount: item.project.MF_Loan_Amount__c,
    //                 StartDate: item.project.Project_Start_Date__c,
    //                 feedItemCount: item.feedItemCount
    //             }));
    //             console.log('this.projects '+JSON.stringify(this.projects));
    //             console.log('this.projects '+this.projects[0].Name);
    //         })
    //         .catch(error => {
    //             console.error('Error fetching projects in header :', JSON.stringify(error));
    //         });
    // }
    
}