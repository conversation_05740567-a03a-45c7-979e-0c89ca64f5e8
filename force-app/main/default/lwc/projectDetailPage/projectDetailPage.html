<!-- <template>
    <lightning-layout multiple-rows>
        <lightning-layout-item size="6" small-device-size="12" medium-device-size="6" large-device-size="6">
            <p class="loan-info-title heading-detail slds-m-top_large">{headingLabel}</p>
            <h1 class="title">{projectDetails.projName}</h1>
        </lightning-layout-item>
        <lightning-layout-item size="3" small-device-size="12" medium-device-size="6" large-device-size="3">
        </lightning-layout-item>
        <template if:true={df}>
            <lightning-layout-item size="2.5" small-device-size="12" medium-device-size="6" large-device-size="2.5">
                <div class="slds-p-top_large slds-text-align_right slds-m-right_small">
                    <button class="slds-button slds-button_neutral custom-green-border" onclick={handleQuickAction} value="Submit Disbursement"> SUBMIT DISBURSEMENT</button>
                </div>
            </lightning-layout-item>
        </template>
    </lightning-layout>
</template> -->
<template>
    <lightning-layout multiple-rows>
        <!-- Left half (6/12) -->
        <lightning-layout-item size="6" small-device-size="12" medium-device-size="6" large-device-size="6">
            <p class="loan-info-title heading-detail slds-m-top_large">
                {headingLabel}
            </p>
            <h1 class="title">{projectDetails.projName}</h1>
        </lightning-layout-item>

        <!-- Right half (6/12) only when df is true -->
        <template if:true={df}>
            <lightning-layout-item size="6" small-device-size="12" medium-device-size="6" large-device-size="6">
                <div class="slds-p-top_large slds-text-align_right">
                    <!-- First button -->
                    <button class="slds-button slds-button_neutral custom-green-border" onclick={handleViewTransaction} value="View Transactions"> VIEW TRANSACTIONS</button>
                    
                    <!-- Second (duplicate) button -->
                    <button class="slds-button slds-button_neutral custom-green-border" onclick={handleQuickAction} value="Submit Disbursement"> SUBMIT DISBURSEMENT</button>
                </div>
            </lightning-layout-item>
        </template>
    </lightning-layout>
</template>