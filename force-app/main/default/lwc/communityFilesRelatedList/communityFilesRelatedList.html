<template>
    <div class="bgroundColor elementHoldingHTMLContent" lwc:dom="manual"></div>

    <!-- Spinner -->
    <template if:true={isShowSpinner}>
        <lightning-spinner alternative-text="Loading" size="small"></lightning-spinner>
    </template>

    <!-- Files Section -->
    <div data-id="filesSection">
        <template if:true={loaded}>
            <lightning-layout multiple-rows>
                <lightning-layout-item size="12">
                    <h1 class="title heading-part">{getTitle}</h1>
                    <div class="loan-card">

                        <!-- PARENT FILES TABLE -->
                        <template if:true={hasFiles}>

                            <table class="slds-table slds-table_cell-buffer">
                                <colgroup>
                                    <col style="width: 40%;" />
                                    <col style="width: 20%;" />
                                    <col style="width: 15%;" />
                                    <col style="width: 20%;" />
                                    <col style="width: 5%;" />
                                </colgroup>
                                <thead>
                                    <tr class="slds-line-height_reset">
                                        <th scope="col">
                                            <div class="slds-truncate heading-detail loan-info-title" title="Project">
                                                TITLE</div>
                                        </th>
                                        <th scope="col">
                                            <div class="slds-truncate heading-detail loan-info-title"
                                                title="Start Date">UPDATED</div>
                                        </th>
                                        <th scope="col">
                                            <div class="slds-truncate heading-detail loan-info-title" title="Amount">
                                                SIZE</div>
                                        </th>
                                        <th scope="col">
                                            <div class="slds-truncate heading-detail loan-info-title" title="Messages">
                                                CREATED BY</div>
                                        </th>
                                        <th scope="col"></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <template for:each={files} for:item="file">
                                        <tr key={file.id} class="slds-hint-parent">
                                            <td>
                                                <div class="responsive-text heading-value" title={file.Title}>
                                                    {file.Title}
                                                </div>
                                            </td>
                                            <td>
                                                <div class="slds-truncate heading-value"
                                                    title={file.ContentModifiedDate}>
                                                    {file.ContentModifiedDate}
                                                </div>
                                            </td>
                                            <td>
                                                <div class="slds-truncate heading-value" title={file.ContentSize}>
                                                    {file.ContentSize}
                                                </div>
                                            </td>
                                            <td>
                                                <div class="slds-truncate heading-value" title={file.CreatedByName}>
                                                    {file.CreatedByName}
                                                </div>
                                            </td>
                                            <td class="slds-text-align_right">
                                                <div
                                                    class="slds-dropdown-trigger slds-dropdown-trigger_click slds-is-open">
                                                    <button
                                                        class="slds-button slds-button_icon"
                                                        aria-haspopup="true"
                                                        aria-expanded="true"
                                                        title="Show More"
                                                        onclick={toggleDropdown}
                                                        data-id={file.Id}
                                                    >
                                                        <lightning-icon
                                                            icon-name="utility:chevrondown"
                                                            alternative-text="Show More"
                                                            size="x-small"
                                                            variant="success"
                                                        ></lightning-icon>
                                                    </button>
                                                    <template if:true={file.dropdownVisible}>
                                                        <div class={dropdownClass}>
                                                            <ul class="slds-dropdown__list" role="menu"
                                                                aria-label="Show More">
                                                                <li class="slds-dropdown__item" role="presentation">
                                                                    <a href="javascript:void(0);"
                                                                        class="slds-text-link heading-value"
                                                                        data-name="view" data-fileurl={file.PathLower}
                                                                        onclick={handleRowAction} data-id={file.Id}>
                                                                        View
                                                                    </a>
                                                                </li>
                                                                <li class="slds-dropdown__item" role="presentation">
                                                                    <a href="javascript:void(0);"
                                                                        class="slds-text-link heading-value"
                                                                        data-name="download" onclick={handleRowAction}
                                                                        data-id={file.Id}>
                                                                        Download
                                                                    </a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </template>
                                                </div>
                                            </td>
                                        </tr>
                                    </template>
                                </tbody>
                            </table>
                        </template>

                        <!-- CHILD FILES TABLE -->
                        <template if:true={hasChildAttachments}>
                            <h2 class="slds-text-heading_small slds-m-top_medium">
                                Related Files
                            </h2>
                            <table class="slds-table slds-table_cell-buffer">
                                <colgroup>
                                    <col style="width: 40%;" />
                                    <col style="width: 20%;" />
                                    <col style="width: 15%;" />
                                    <col style="width: 20%;" />
                                    <col style="width: 5%;" />
                                </colgroup>
                                <thead>
                                    <tr class="slds-line-height_reset">
                                        <th scope="col">
                                            <div class="slds-truncate heading-detail loan-info-title" title="Project">
                                                TITLE</div>
                                        </th>
                                        <th scope="col">
                                            <div class="slds-truncate heading-detail loan-info-title"
                                                title="Start Date">UPDATED</div>
                                        </th>
                                        <th scope="col">
                                            <div class="slds-truncate heading-detail loan-info-title" title="Amount">
                                                SIZE</div>
                                        </th>
                                        <th scope="col">
                                            <div class="slds-truncate heading-detail loan-info-title" title="Messages">
                                                CREATED BY</div>
                                        </th>
                                        <th scope="col"></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <template for:each={childFiles} for:item="file">
                                        <tr key={file.id} class="slds-hint-parent">
                                            <td>
                                                <div class="responsive-text heading-value" title={file.Title}>
                                                    {file.Title}
                                                </div>
                                            </td>
                                            <td>
                                                <div class="slds-truncate heading-value"
                                                    title={file.ContentModifiedDate}>
                                                    {file.ContentModifiedDate}
                                                </div>
                                            </td>
                                            <td>
                                                <div class="slds-truncate heading-value" title={file.ContentSize}>
                                                    {file.ContentSize}
                                                </div>
                                            </td>
                                            <td>
                                                <div class="slds-truncate heading-value" title={file.CreatedByName}>
                                                    {file.CreatedByName}
                                                </div>
                                            </td>
                                            <td class="slds-text-align_right">
                                                <div
                                                    class="slds-dropdown-trigger slds-dropdown-trigger_click slds-is-open">
                                                    <button
                                                        class="slds-button slds-button_icon"
                                                        aria-haspopup="true"
                                                        aria-expanded="true"
                                                        title="Show More"
                                                        onclick={toggleDropdown}
                                                        data-id={file.Id}
                                                    >
                                                        <lightning-icon
                                                            icon-name="utility:chevrondown"
                                                            alternative-text="Show More"
                                                            size="x-small"
                                                            variant="success"
                                                        ></lightning-icon>
                                                    </button>
                                                    <template if:true={file.dropdownVisible}>
                                                        <div class={dropdownClass}>
                                                            <ul class="slds-dropdown__list" role="menu"
                                                                aria-label="Show More">
                                                                <li class="slds-dropdown__item" role="presentation">
                                                                    <a href="javascript:void(0);"
                                                                        class="slds-text-link heading-value"
                                                                        data-name="view" data-fileurl={file.PathLower}
                                                                        onclick={handleRowAction} data-id={file.Id}>
                                                                        View
                                                                    </a>
                                                                </li>
                                                                <li class="slds-dropdown__item" role="presentation">
                                                                    <a href="javascript:void(0);"
                                                                        class="slds-text-link heading-value"
                                                                        data-name="download" onclick={handleRowAction}
                                                                        data-id={file.Id}>
                                                                        Download
                                                                    </a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </template>
                                                </div>
                                            </td>
                                        </tr>
                                    </template>
                                </tbody>
                            </table>
                        </template>

                        <!-- Upload Section -->
                        <div class="upload-section">
                            <lightning-file-upload onuploadfinished={handleUploadFinished} slot="actions" multiple>
                            </lightning-file-upload>
                        </div>

                    </div>
                </lightning-layout-item>
            </lightning-layout>
        </template>
    </div>

    <!-- Skeleton while loading -->
    <template if:false={loaded}>
        <template if:false={isOppPage}>
            <svg width="1120" height="400" viewBox="0 0 1120 300" xmlns="http://www.w3.org/2000/svg">
                <rect width="1120" height="300" fill="white" />
                <rect x="20" y="10" rx="4" ry="4" width="80" height="15" fill="#ddd" />
                <rect x="50" y="50" width="120" height="10" rx="5" fill="#D6D6D6" />
                <rect x="300" y="50" width="120" height="10" rx="5" fill="#D6D6D6" />
                <rect x="500" y="50" width="120" height="10" rx="5" fill="#D6D6D6" />
                <rect x="700" y="50" width="120" height="10" rx="5" fill="#D6D6D6" />
                <rect x="50" y="100" width="120" height="12" rx="5" fill="#E0E0E0" />
                <rect x="300" y="100" width="120" height="12" rx="5" fill="#E0E0E0" />
                <rect x="500" y="100" width="120" height="12" rx="5" fill="#E0E0E0" />
                <rect x="700" y="100" width="120" height="12" rx="5" fill="#E0E0E0" />
                <rect x="1030" y="95" width="20" height="20" rx="5" fill="#D6D6D6" />
                <rect x="50" y="130" width="120" height="12" rx="5" fill="#E0E0E0" />
                <rect x="300" y="130" width="120" height="12" rx="5" fill="#E0E0E0" />
                <rect x="500" y="130" width="120" height="12" rx="5" fill="#E0E0E0" />
                <rect x="700" y="130" width="120" height="12" rx="5" fill="#E0E0E0" />
                <rect x="1030" y="125" width="20" height="20" rx="5" fill="#D6D6D6" />
                <rect x="20" y="200" width="1060" height="60" rx="10" fill="#F3F3F3" stroke="#D6D6D6"
                    stroke-dasharray="5,5" />
                <rect x="50" y="225" width="120" height="20" rx="5" fill="#E0E0E0" />
                <rect x="200" y="225" width="100" height="15" rx="5" fill="#D6D6D6" />
            </svg>
        </template>
        <template if:true={isOppPage}>
            <svg width="600" height="400" viewBox="0 0 600 300" xmlns="http://www.w3.org/2000/svg">
                <rect x="20" y="20" rx="4" ry="4" width="80" height="15" fill="#ddd" />
                <rect width="560" height="300" fill="white" />
                <rect x="20" y="20" rx="4" ry="4" width="80" height="15" fill="#ddd" />
                <rect x="50" y="50" width="60" height="10" rx="5" fill="#D6D6D6" />
                <rect x="300" y="50" width="60" height="10" rx="5" fill="#D6D6D6" />
                <rect x="200" y="50" width="60" height="10" rx="5" fill="#D6D6D6" />
                <rect x="400" y="50" width="80" height="10" rx="5" fill="#D6D6D6" />
                <rect x="50" y="100" width="120" height="12" rx="5" fill="#E0E0E0" />
                <rect x="200" y="100" width="80" height="12" rx="5" fill="#E0E0E0" />
                <rect x="300" y="100" width="60" height="12" rx="5" fill="#E0E0E0" />
                <rect x="400" y="100" width="80" height="12" rx="5" fill="#E0E0E0" />
                <rect x="500" y="95" width="20" height="20" rx="5" fill="#D6D6D6" />
                <rect x="50" y="130" width="120" height="12" rx="5" fill="#E0E0E0" />
                <rect x="200" y="130" width="80" height="12" rx="5" fill="#E0E0E0" />
                <rect x="300" y="130" width="60" height="12" rx="5" fill="#E0E0E0" />
                <rect x="400" y="130" width="80" height="12" rx="5" fill="#E0E0E0" />
                <rect x="500" y="125" width="20" height="20" rx="5" fill="#D6D6D6" />
                <rect x="20" y="200" width="520" height="60" rx="10" fill="#F3F3F3" stroke="#D6D6D6"
                    stroke-dasharray="5,5" />
                <rect x="50" y="225" width="120" height="20" rx="5" fill="#E0E0E0" />
                <rect x="200" y="225" width="100" height="15" rx="5" fill="#D6D6D6" />
            </svg>
        </template>
    </template>
</template>