/* eslint-disable */
import { LightningElement, api, wire, track } from "lwc";
import { ShowToastEvent } from "lightning/platformShowToastEvent";
import { CurrentPageReference } from 'lightning/navigation';
import { NavigationMixin } from "lightning/navigation";
import LightningConfirm from 'lightning/confirm';
import isSandbox from '@salesforce/apex/mf123opp.isSandbox';
import getFilesOfRecord from "@salesforce/apex/AttachmentCompController.getFilesOfRecord";
import uploadFiles from "@salesforce/apex/AttachmentCompController.uploadFiles";
import deleteFile from "@salesforce/apex/AttachmentCompController.deleteFile";
import getOpprOfProject from '@salesforce/apex/mf123opp.getOpprOfProject';
import getCurrentUserAccountName from '@salesforce/apex/mf123opp.getCurrentUserAccountName';
import fetchFileShareLink from '@salesforce/apex/DropboxController.fetchFileShareLink';
import createFileShareLink from '@salesforce/apex/DropboxController.createFileShareLink';
import getDropboxShareLink from '@salesforce/apex/DropboxController.getDropboxShareLink';
import { subscribe, MessageContext } from 'lightning/messageService';
import SCROLL_CHANNEL from '@salesforce/messageChannel/ScrollMessageChannel__c';

const actions = [
    { label: 'View', name: 'view' },
    { label: 'Download', name: 'download' },
    { label: 'Delete', name: 'delete' }
]
const columns = [
    {
        label: "TITLE",
        fieldName: "FileUrl",
        type: "url",
        typeAttributes: {
            label: {
                fieldName: "Title"
            },
            name: "Download",
            target: "_blank"
        },
        sortable: true,
        hideDefaultActions: true
    },
    {
        label: "FILE TYPE",
        fieldName: "FileType",
        hideDefaultActions: true
    },
    {
        label: "SIZE",
        fieldName: "ContentSize",
        hideDefaultActions: true
    },
    {
        label: 'CREATED BY', fieldName: 'CreatedByName',
        cellAttributes: {
            iconName: 'standard:user', iconPosition: 'left'
        }
    },
    {
        label: "LAST MODIFIED",
        fieldName: "ContentModifiedDate",
        type: "date",
        sortable: true,
        hideDefaultActions: true
    },
    {
        type: 'action',
        typeAttributes: {
            rowActions: actions
        }
    }
];

export default class CommunityFilesRelatedList extends NavigationMixin(LightningElement) {
    @api recordId;
    @api allowUploadingFiles;
    @api isStandredRecordDetailPage = false;
    @api pageName;
    @track parentFiles = [];
    @track childFiles = [];
    files = [];
    columns = columns;
    defaultSortDirection = "desc";
    sortDirection = "desc";
    sortedBy = "ContentModifiedDate";
    oppId;
    @track loaded = false;
    @track hasChildAttachments = false;
    @track hasAttachments = false;
    @track cardWidth = '12';

    @track dropdownVisible = false;
    @track projName;
    @track currentUserAccountName;
    basePath;
    isOppPage;
    isShowSpinner = false;


    get dropdownClass() {
        return this.dropdownVisible = 'slds-dropdown slds-dropdown_right';
        // : 'slds-dropdown slds-dropdown_right';
    }

    get hasFiles() {
        return this.files != undefined && this.files.length > 0;
    }


    @wire(CurrentPageReference)
    getPageReference(currentPageReference) {
        if (currentPageReference) {
            const { state } = currentPageReference;

            const oppIdFromUrl = state.id;
            if (oppIdFromUrl) {
                this.oppId = oppIdFromUrl;
                this.isOppPage = true;
                //this.fetchOpportunityType();
            }
        }
    }

    connectedCallback() {

        if (this.recordId != undefined) {
            this.oppId = this.recordId;
        }
        if (this.isStandredRecordDetailPage == true) {
            this.cardWidth = '7';
            this.fetchOppId(this.recordId)
                .then(() => {
                    this.retrieveFiles();
                })
                .catch((error) => {
                    console.error('Error fetching opportunity ID:', error);
                    this.loaded = true;
                });
        }
        else if (this.isStandredRecordDetailPage == false) {
            this.fetchOppId(this.oppId)
                .then(() => {
                    this.retrieveFiles();
                })
                .catch((error) => {
                    console.error('Error fetching opportunity ID:', error);
                    this.loaded = true;
                });
        }

        getCurrentUserAccountName()
            .then((result) => {
                this.currentUserAccountName = result;
            })
            .catch((error) => {
                console.error('Error fetching current user account name:', error);
                this.loaded = true;
            });
        //this.retrieveFiles();
        isSandbox()
            .then((result) => {
                this.basePath = result ? '/mf/s/' : '/s/';
            })
            .catch((error) => {
                console.error('Error checking sandbox status:', JSON.stringify(error));
            });

    }

    fetchOppId(projId) {
        return new Promise((resolve, reject) => {
            getOpprOfProject({ projectId: projId, pgName: this.pageName })
                .then(result => {
                    if (result != undefined && result != '' && result.length > 0) {
                        this.projName = JSON.parse(JSON.stringify(result[0].Name))
                    }
                    resolve();
                })
                .catch(error => {
                    this.loaded = true;
                    reject(error);
                });
        });
    }

    // toggleDropdown(event) {
    //     const fileId = event.currentTarget.dataset.id;
    //     this.files = this.files.map(file => ({
    //         ...file,
    //         dropdownVisible: file.Id === fileId ? !file.dropdownVisible : file.dropdownVisible
    //     }));
    // }

    toggleDropdown(event) {
        const fileId = event.currentTarget.dataset.id;

        // Parent?
        if (this.files.some(f => f.Id === fileId)) {
            this.files = this.files.map(f => ({
                ...f,
                dropdownVisible: f.Id === fileId ? !f.dropdownVisible : f.dropdownVisible
            }));
        }
        // Child?
        else if (this.childFiles.some(f => f.Id === fileId)) {
            this.childFiles = this.childFiles.map(f => ({
                ...f,
                dropdownVisible: f.Id === fileId ? !f.dropdownVisible : f.dropdownVisible
            }));
        }
    }

    truncateFilename(filename, length) {
        if (filename.length > length) {
            return filename.substring(0, length) + '...';
        }
        return filename;
    }

    retrieveFiles() {
        this.loaded = false;

        getFilesOfRecord({ recordId: this.oppId })
            .then((result) => {
                // your Apex now returns an object with two lists
                const parent = result.parentFiles || [];
                const child = result.childFiles || [];
                const baseUrl = "https://" + location.host.replace("site", "salesforce");

                // common mapper to apply your styling/transforms
                const mapFn = (file) => {
                    let fileData = {
                        ...file,
                        Title: this.truncateFilename(file.Title, 17),
                        ContentSize: this.formatBytes(file.ContentSize),
                        ContentModifiedDate: this.formatDate(file.ContentModifiedDate),
                        CreatedByName: file.CreatedBy.FirstName
                            ? `${file.CreatedBy.FirstName} ${file.CreatedBy.LastName}`
                            : file.CreatedBy.LastName,
                        dropdownVisible: false
                    };

                    if (file.ContentDistributions?.length) {
                        fileData.DownloadUrl = file.ContentDistributions[0].ContentDownloadUrl || '';
                        fileData.FileUrl = file.ContentDistributions[0].DistributionPublicUrl || '';
                    } else {
                        fileData.DownloadUrl = `${baseUrl}/sfc/servlet.shepherd/document/download/${file.Id}`;
                        fileData.FileUrl = `${baseUrl}/sfc/servlet.shepherd/version/renditionDownload?rendition=THUMB720BY480&versionId=${file.LatestPublishedVersionId}`;
                    }

                    fileData.showViewExternal = ['xlsx', 'csv'].includes(
                        file.FileExtension?.toLowerCase()
                    );
                    fileData.DropboxUrl = file.LatestPublishedVersion?.Dropbox_Url__c || null;
                    return fileData;
                };

                // process parent files
                this.files = parent
                    .filter(f => f.FileExtension !== 'snote')
                    .map(mapFn);

                // process child files
                this.childFiles = child
                    .filter(f => f.FileExtension !== 'snote')
                    .map(mapFn);

                // flags for rendering your two tables
                this.hasAttachments = this.files.length > 0;
                this.hasChildAttachments = this.childFiles.length > 0;
            })
            .catch((error) => {
                console.error('Error retrieving files:', this.getWireError(error));
                this.displayToast("Error", this.getWireError(error), "error");
                this.hasAttachments = false;
                this.hasChildAttachments = false;
            })
            .finally(() => {
                this.loaded = true;
            });
    }

    // retrieveFiles() {
    //     this.loaded = false;

    //     getFilesOfRecord({ recordId: this.oppId })
    //     .then((files) => {
    //         console.log('files ' + JSON.stringify(files));
    //         let baseUrl = "https://" + location.host.replace("site","salesforce");
    //         console.log('baseUrl ' + baseUrl);

    //         this.files = files
    //             .filter(file => file.FileExtension !== 'snote')
    //             .map((file) => {
    //                 let fileData = {
    //                     ...file,
    //                     Title: this.truncateFilename(file.Title, 17),
    //                     ContentSize: this.formatBytes(file.ContentSize),
    //                     ContentModifiedDate: this.formatDate(file.ContentModifiedDate),
    //                     CreatedByName: file.CreatedBy.FirstName ? file.CreatedBy.FirstName + ' ' + file.CreatedBy.LastName : file.CreatedBy.LastName,
    //                     dropdownVisible: false
    //                 };

    //                 if (file.ContentDistributions && file.ContentDistributions.length > 0) {
    //                     fileData.DownloadUrl = file.ContentDistributions[0]?.ContentDownloadUrl || '';
    //                     fileData.FileUrl = file.ContentDistributions[0]?.DistributionPublicUrl || '';
    //                 } else { 
    //                     fileData.DownloadUrl = baseUrl + "/sfc/servlet.shepherd/document/download/" + file.Id;
    //                     fileData.FileUrl = baseUrl + '/sfc/servlet.shepherd/version/renditionDownload?rendition=THUMB720BY480&versionId=' + file.LatestPublishedVersionId;
    //                 }
    //                 fileData.showViewExternal = file.FileExtension && (file.FileExtension.toLowerCase() === 'xlsx' || file.FileExtension.toLowerCase() === 'csv');
    //                 fileData.DropboxUrl = file.LatestPublishedVersion?.Dropbox_Url__c || null;

    //                 // Log the Dropbox URL for debugging
    //                 console.log('DropboxUrl:', fileData.DropboxUrl);

    //                 return fileData;
    //             });

    //         this.loaded = true;
    //         this.hasAttachments = this.files.length > 0;
    //     })
    //     .catch((error) => {
    //         console.error('Error retrieving files:', this.getWireError(error));
    //         this.displayToast("Error", this.getWireError(error), "error");
    //         this.hasAttachments = false;
    //         this.loaded = true;
    //     });
    // }


    formatDate(dateString) {
        const options = { year: 'numeric', month: 'numeric', day: 'numeric' };
        return new Date(dateString).toLocaleDateString(undefined, options);
    }

    get getTitle() {
        return "Files (" + this.files.length + ")";
    }

    onHandleSort(event) {
        const { fieldName: sortedBy, sortDirection } = event.detail;
        const dir = sortDirection === 'asc' ? 1 : -1;

        // helper to compare
        const sorter = (a, b) => {
            let x = a[sortedBy], y = b[sortedBy];
            if (typeof x === 'string') { x = x.toLowerCase(); y = y.toLowerCase(); }
            return dir * ((x > y) - (y > x));
        };

        // sort parent if the click came from its headers
        if (event.currentTarget.closest('.parent-table')) {
            this.files = [...this.files].sort(sorter);
            this.sortedBy = sortedBy;
            this.sortDirection = sortDirection;
        }
        // or sort children if from the child table
        else {
            this.childFiles = [...this.childFiles].sort(sorter);
        }
    }


    sortBy(field, reverse, primer) {
        const key = primer
            ? function (x) {
                return primer(x[field]);
            }
            : function (x) {
                return x[field];
            };

        return function (a, b) {
            a = key(a);
            b = key(b);

            if (typeof a === "string") {
                a = a.toLowerCase();
                b = b.toLowerCase();
            }

            return reverse * ((a > b) - (b > a));
        };
    }

    // async handleRowAction(event) {
    //     console.log('handleRowAction 363 ' + JSON.stringify(event.currentTarget.dataset));
    //     let actionName = event.currentTarget.dataset.name;
    //     let fileId = event.currentTarget.dataset.id;
    //     console.log('fileId 363 ' + fileId);
    //     let row = this.files.find(file => file.Id === fileId);

    //     //view in edit mode
    //     let fileName = event.currentTarget.dataset.title;
    //     let fileExtension = event.currentTarget.dataset.fileextension;
    //     let filepath = event.currentTarget.dataset.filepath;

    //     this.files = this.files.map(file => ({
    //         ...file,
    //         dropdownVisible: file.Id === fileId ? !file.dropdownVisible : file.dropdownVisible
    //     }));

    //     switch (actionName) {
    //         case 'view':
    //             this.viewDocument(row);
    //             break;
    //         case 'download':
    //             this.downloadFile(row);
    //             break;
    //         case 'delete':
    //             await this.deleteFile(fileId);
    //             break;
    //         case 'View External':
    //             this.loaded = false;
    //             this.viewInExcelMode(fileName, fileExtension, filepath, fileId);
    //             break;
    //         default:
    //     }
    // }

    async handleRowAction(event) {
  const actionName = event.currentTarget.dataset.name;
  const fileId     = event.currentTarget.dataset.id;

  // find row and which array it belongs to
  let row, arrayKey;
  if (this.files.some(f => f.Id === fileId)) {
    arrayKey = 'files';
    row      = this.files.find(f => f.Id === fileId);
  } else {
    arrayKey = 'childFiles';
    row      = this.childFiles.find(f => f.Id === fileId);
  }

  // toggle only the clicked row in the right array
  this[arrayKey] = this[arrayKey].map(f => ({
    ...f,
    dropdownVisible: f.Id === fileId ? !f.dropdownVisible : f.dropdownVisible
  }));

  // now run the requested action
  switch (actionName) {
    case 'view':
      this.viewDocument(row);
      break;
    case 'download':
      this.downloadFile(row);
      break;
    case 'delete':
      // after deletion we just refresh everything
      const confirmed = await LightningConfirm.open({
        message: 'Are you sure you want to delete this file?',
        variant: 'header',
        label: 'Delete Confirmation'
      });
      if (confirmed) {
        try {
          await deleteFile({ contentDocumentId: fileId });
          this.displayToast('Success','File deleted','success');
          this.retrieveFiles();
        } catch(e) {
          this.displayToast('Error', this.getWireError(e), 'error');
        }
      }
      break;
    default:
      // any other custom actions…
  }
}


    viewInExcelMode(fileName, fileExtension, filepath, fileId) {
        let completeFileName = fileName + '.' + fileExtension;
        let completeFilePath = filepath + completeFileName;

        getDropboxShareLink({ contentDocumentId: fileId })
            .then((existingLink) => {
                if (existingLink) {
                    this.loaded = true;
                    console.log('Using existing Dropbox link:', existingLink);
                    this.navigateToDropboxLink(existingLink);
                } else {
                    console.log('No existing Dropbox link. Creating new share link...');
                    createFileShareLink({ path: completeFilePath, contentDocumentId: fileId })
                        .then((newLink) => {
                            console.log('newLink ' + newLink);
                            if (newLink === 'shared_link_exists') {
                                console.log('Share link already exists. Fetching existing link...');
                                //this.fetchFileShareLink(contentDocumentId);
                                fetchFileShareLink({ path: completeFilePath })
                                    .then((result) => {
                                        if (result.url) {
                                            this.loaded = true;
                                            console.log('Dropbox URL:414 ', result.url);

                                            this[NavigationMixin.Navigate](
                                                {
                                                    type: 'standard__webPage',
                                                    attributes: {
                                                        url: result.url,
                                                    },
                                                },
                                                false
                                            );
                                        } else if (result.error) {
                                            this.showToast('Error', result.error, 'error');
                                        } else {
                                            this.showToast('Error', 'Unexpected response from server.', 'error');
                                        }
                                    })
                                    .catch((error) => {
                                        console.error('Error calling Apex method:', error);
                                        this.showToast('Error', 'Failed to open the file in edit mode.', 'error');
                                    });
                            } else if (newLink != 'shared_link_exists') {
                                this.loaded = true;
                                console.log('New Dropbox link created:', newLink);
                                this.navigateToDropboxLink(newLink);
                            }
                            else {
                                this.showToast('Error', 'Failed to create Dropbox share link.', 'error');
                            }
                        })
                        .catch((error) => {
                            console.error('Error creating Dropbox share link:', error);
                            this.showToast('Error', 'Failed to create Dropbox share link.', 'error');
                        });
                }
            })
            .catch((error) => {
                console.error('Error fetching Dropbox share link:', error);
                this.showToast('Error', 'Failed to fetch Dropbox share link.', 'error');
            });
    }

    navigateToDropboxLink(url) {
        this[NavigationMixin.Navigate](
            {
                type: 'standard__webPage',
                attributes: {
                    url: url,
                },
            },
            false
        );
    }

    async deleteFile(fileId) {
        const result = await LightningConfirm.open({
            message: 'Are you sure you want to delete this file?',
            variant: 'header',
            label: 'Delete Confirmation',
        });
        if (result) {
            this.loaded = false;
            deleteFile({ contentDocumentId: fileId })
                .then(() => {
                    //this.displayToast('Success', result, 'success');
                    this.displayToast("Success", "File deleted successfully", "success");
                    this.loaded = true;
                    this.retrieveFiles();
                })
                .catch(error => {
                    //this.displayToast('Error', this.getWireError(error), 'error');
                    this.displayToast("Error", this.getWireError(error), "error");
                    this.loaded = true;
                });
        }
    }

    viewDocument(row) {
        console.log('in downloadFile row ' + JSON.stringify(row));
        console.log('in downloadFile row ' + row.FileUrl);
        this[NavigationMixin.Navigate]({
            type: 'standard__webPage',
            attributes: {
                url: row.FileUrl
            }
        }, false);
    }

    downloadFile(row) {
        console.log('in downloadFile row ' + JSON.stringify(row));
        window.location.href = row.DownloadUrl;
        console.log('in downloadFile row window.location.href ' + window.location.href);
    }


    get filesArePresent() {
        return this.files?.length > 0;
    }

    getWireError(error) {
        let message = "Unknown error";
        if (Array.isArray(error.body)) {
            message = error.body.map((e) => e.message).join(", ");
        } else if (typeof error.body.message === "string") {
            message = error.body.message;
        }
        return message;
    }

    displayToast(title, message, variant) {
        this.dispatchEvent(
            new ShowToastEvent({
                title: title,
                message: message,
                variant: variant
            })
        );
    }

    formatBytes(bytes, decimals = 1) {
        if (bytes === 0) return "0 Bytes";

        const k = 1024;
        const dm = decimals < 0 ? 0 : decimals;
        const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];

        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + " " + sizes[i];
    }


    handleUploadFinished(event) {
        this.isShowSpinner = true;
        const contentDocumentIds = event.detail.files.map(file => file.documentId);

        uploadFiles({ recordId: this.oppId, contentDocumentIds, isInternal: false })
            .then(() => {
                this.retrieveFiles();
                setTimeout(() => {
                    this.isShowSpinner = false;
                }, 1000);
                this.displayToast("Success", "Files uploaded successfully", "success");
            })
            .catch((error) => {
                this.isShowSpinner = false;
                this.displayToast("Error", this.getWireError(error), "error");
            });
    }

    @wire(MessageContext) messageContext;
    subscription;

    renderedCallback() {
        if (this.subscription || !this.messageContext) {
            return;
        }

        this.subscription = subscribe(this.messageContext, SCROLL_CHANNEL, (message) => {
            if (message.targetSection === 'filesSection') {
                this.scrollToSection();
            }
        });
    }

    scrollToSection() {
        const section = this.template.querySelector('[data-id="filesSection"]');
        if (section) {
            section.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    }
}