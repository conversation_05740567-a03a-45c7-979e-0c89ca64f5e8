import { LightningElement, api } from "lwc";

export default class LoanDisbursement extends LightningElement {
  @api transactions = [];
  @api disbursements = [];
  @api cashFlowData = [];
  @api weekColumns = [];

  connectedCallback() {
    // Log inputs for debugging
    console.log("API transactions:", JSON.stringify(this.transactions));
    console.log("API cashFlowData:", JSON.stringify(this.cashFlowData));
    console.log('weekColumns:  ', JSON.stringify(this.weekColumns));

    // Perform the update (will deep‐clone internally)
    this.updateMFLoanDisbursementData();
  }

  get projectId() {
    return Array.isArray(this.disbursements) && this.disbursements.length
      ? this.disbursements[0].Project__c
      : null;
  }

  /**
   * Given a JS Date, return the Date object for the Friday of that week.
   * If date is already a Friday (getDay() === 5), return it (with hours zeroed).
   * Otherwise, “push forward” to the next Friday.
   */
  getNextFriday(date) {
    const d = new Date(date);
    const dow = d.getDay(); // Sunday=0, Monday=1, …, Friday=5, Saturday=6
    const offset = (5 - dow + 7) % 7;
    d.setDate(d.getDate() + offset);
    d.setHours(0, 0, 0, 0);
    return d;
  }

  /**
   * 1) Filters + groups transactions by their Friday date
   * 2) Deep‐clones the MFLoanDisb row from cashFlowData
   * 3) Updates each cloned week’s .value and adds a .dateLineItem array
   * 4) Replaces the old cashFlowData with this new cloned array
   */
  updateMFLoanDisbursementData() {
    // Step A: Build a map: { weekTimestamp → { loanDisbSum, items: [ { id, loanDisb } ] } }
    const txns = (Array.isArray(this.transactions) ? this.transactions : []).filter(
      (tx) => tx.Project__c === this.projectId
    );

    // Map each transaction to { weekKey, weekDate, txId, loanDisb }
    const perTxn = txns.map((tx) => {
      const weekFriday = this.getNextFriday(new Date(tx.CreatedDate));
      return {
        weekKey: weekFriday.getTime(),
        weekDate: weekFriday,
        txId: tx.Id,
        loanDisb: tx.Loan_Principal_to_date__c || 0
      };
    });

    // Reduce into groups by weekKey
    const groups = perTxn.reduce((acc, curr) => {
      const key = curr.weekKey;
      if (!acc[key]) {
        acc[key] = {
          weekDate: curr.weekDate,
          loanDisbSum: 0,
          items: []
        };
      }
      acc[key].loanDisbSum += curr.loanDisb;
      acc[key].items.push({
        id: curr.txId,
        loanDisb: curr.loanDisb
      });
      return acc;
    }, {});

    // Step B: Find the index of the MFLoanDisb row in the original array
    const mfIndex = this.cashFlowData.findIndex((row) => row.id === "MFLoanDisb");
    if (mfIndex < 0) {
      // If MFLoanDisb not found, nothing to do
      return;
    }

    // Step C: Deep‐clone cashFlowData but only mutate the MFLoanDisb row
    const newCashFlowData = this.cashFlowData.map((row, idx) => {
      if (idx !== mfIndex) {
        // Return all other rows unchanged
        return row;
      }

      // Deep‐clone the MFLoanDisb row and its weeks array
      const clonedWeeks = Array.isArray(row.weeks)
        ? row.weeks.map((w) => ({ ...w }))
        : [];

      const clonedRow = {
        ...row,
        weeks: clonedWeeks
      };

      // Now update each cloned week entry if it matches one of our grouped Fridays
      Object.values(groups).forEach((grp) => {
        // Format grp.weekDate as "YYYY-MM-DD" to match week.date
        const year = grp.weekDate.getFullYear();
        const month = String(grp.weekDate.getMonth() + 1).padStart(2, "0");
        const day = String(grp.weekDate.getDate()).padStart(2, "0");
        const isoDate = `${year}-${month}-${day}`;

        // Find that week in the cloned weeks array
        const match = clonedWeeks.find((wk) => wk.date === isoDate);
        if (match) {
          // Since this is a clone, it's safe to assign directly
          match.value = grp.loanDisbSum;
          match.dateLineItem = grp.items;
        }
      });

      return clonedRow;
    });

    // Step D: Reassign the public property so LWC sees a new reference
    this.cashFlowData = newCashFlowData;

    this.dispatchEvent(
      new CustomEvent('cashflowdataupdate', {
        detail: this.cashFlowData,
        bubbles: true,
        composed: true
      })
    );

    // Step E (optional): log out the new structure to verify
    console.log(
      "Updated cashFlowData:",
      JSON.stringify(this.cashFlowData, null, 2)
    );
  }

 


  get displayRows() {
    // 1) Filter transactions for this project
    const txns =
      Array.isArray(this.transactions) && this.projectId
        ? this.transactions.filter((tx) => tx.Project__c === this.projectId)
        : [];

    // 2) Build groupsByISO: { "YYYY-MM-DD" → { loanDisbSum, origFeesSum, paymentSum, outstandingSum } }
    const groupsByISO = txns.reduce((acc, tx) => {
      // a) Find that week’s Friday for this transaction
      const weekFriday = this.getNextFriday(new Date(tx.CreatedDate));
      const yyyy = weekFriday.getFullYear();
      const mm = String(weekFriday.getMonth() + 1).padStart(2, "0");
      const dd = String(weekFriday.getDate()).padStart(2, "0");
      const iso = `${yyyy}-${mm}-${dd}`; // e.g. "2025-06-06"

      // b) Calculate the individual fields from the transaction
      const loanDisb = tx.Loan_Principal_to_date__c || 0;
      const origFees =
        (tx.Default_Fee_Application__c || 0) +
        (tx.Doc_Stamp_Fees_Application__c || 0) +
        (tx.Late_Fee_Application__c || 0) +
        (tx.Legal_Fees_Application__c || 0);
      const payment = tx.Amount__c || 0;
      // “Outstanding” here is: loanDisb + origFees – payment
      //const outstanding = loanDisb + origFees - payment;

      // c) Accumulate into acc[iso]
      if (!acc[iso]) {
        acc[iso] = {
          loanDisbSum: 0,
          origFeesSum: 0,
          paymentSum: 0,
          //outstandingSum: 0,
        };
      }
      acc[iso].loanDisbSum += loanDisb;
      acc[iso].origFeesSum += origFees;
      acc[iso].paymentSum += payment;
      //acc[iso].outstandingSum += outstanding;

      return acc;
    }, {});

    // 3) Now iterate over this.weekColumns to build exactly weekColumns.length rows
    const rows = [];
    
    let prevWeekOutstanding = 0;
    let previousWeekNumber = 0;

    for (const wkCol of this.weekColumns) {
      // wkCol.date is assumed to be an ISO string like "YYYY-MM-DD"
      const isoDate = wkCol.date;
      const sums = groupsByISO[isoDate] || {
        loanDisbSum: 0,
        origFeesSum: 0,
        paymentSum: 0,
        //outstandingSum: 0,
      };

      
      const begValue = prevWeekOutstanding;

      // Current outstanding is what we computed (or 0 if none)
      const currentOutstanding = prevWeekOutstanding + sums.loanDisbSum + sums.origFeesSum - sums.paymentSum;

      let weekNumber;
      if(currentOutstanding ===0){
        weekNumber = 0;
        previousWeekNumber = 0;
      }
      else{
        weekNumber = previousWeekNumber + 1;
        previousWeekNumber = weekNumber;
      }

      const row = {
        id: `${isoDate}`, // or whatever unique ID you want
        date: new Date(isoDate).toLocaleDateString(), // e.g. "6/6/2025"
        week: weekNumber,
        beg: `$ ${begValue.toFixed(2)}`,
        loanDisb: `$ ${sums.loanDisbSum.toFixed(2)}`,
        origFees: `$ ${sums.origFeesSum.toFixed(2)}`,
        payment: `$ ${sums.paymentSum.toFixed(2)}`,
        outstanding: `$ ${currentOutstanding.toFixed(2)}`,
      };

      prevWeekOutstanding = currentOutstanding;
      
      rows.push(row);
    }

    return rows;
  }
}