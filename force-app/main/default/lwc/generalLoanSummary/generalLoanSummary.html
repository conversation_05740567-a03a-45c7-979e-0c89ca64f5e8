<template>
  <lightning-card title="General Loan Summary" icon-name="standard:opportunity" class="loan-summary-card">
    <div class="slds-p-around_medium loan-summary-container">
      <!-- Row 1: Key Metrics -->
      <lightning-layout   class="loan-summary-row-1">
        <!-- Column 1 -->
        <lightning-layout-item flexibility="auto" class="field-group">
          <p class="field-label">Customer</p>
          <lightning-formatted-text class="field-value" value={customerName}></lightning-formatted-text>

          <p class="field-label slds-m-top_medium">Project</p>
          <lightning-formatted-text class="field-value" value={projectName}></lightning-formatted-text>

          <p class="field-label slds-m-top_medium">Project Gross Margin</p>
          <lightning-formatted-number class="field-value" value={projectGrossMargin} format-style="currency" currency-code="USD"></lightning-formatted-number>
        </lightning-layout-item>

        <!-- Column 2 -->
        <lightning-layout-item flexibility="auto" class="field-group" >
          <p class="field-label">Approved MF Loan</p>
          <lightning-formatted-number class="field-value" value={approvedMFLoan} format-style="currency" currency-code="USD"></lightning-formatted-number>

          <p class="field-label slds-m-top_medium">Gross Project Contract Value</p>
          <lightning-formatted-number class="field-value" value={grossContractValue} format-style="currency"  currency-code="USD"></lightning-formatted-number>

          <p class="field-label slds-m-top_medium">Gross LTV</p>
          <lightning-formatted-number class="field-value" value={grossLTV} format-style="percent" maximum-fraction-digits="0"></lightning-formatted-number>
        </lightning-layout-item>

        <!-- Column 3 -->
        <lightning-layout-item flexibility="auto" class="field-group">
          <p class="field-label">Loan Date</p>
          <lightning-formatted-date-time class="field-value" value={loanDate} year="numeric" month="2-digit" day="2-digit"></lightning-formatted-date-time>

          <p class="field-label slds-m-top_medium">Maturity Date</p>
          <lightning-formatted-date-time class="field-value" value={loanMaturityDate} year="numeric" month="2-digit" day="2-digit"></lightning-formatted-date-time>
        </lightning-layout-item>
      </lightning-layout>

      <hr class="slds-m-vertical_medium" />

      <!-- Row 2: Underwriting Checklist -->
      <section class="underwriting-section">
        <h2 class="slds-text-heading_small slds-m-bottom_medium">Underwriting Checklist</h2>
        <lightning-layout multiple-rows="true"  class="loan-summary-row -2">
          <!-- Funds Control Executed -->
          <lightning-layout-item size="3" class="field-group">
            <p class="field-label">Funds Control Executed</p>
            <lightning-formatted-text class="field-value" value="FD"></lightning-formatted-text>
          </lightning-layout-item>

          <!-- Total Projected Financing Cost -->
          <lightning-layout-item size="3" class="field-group">
            <p class="field-label">Total Projected Financing Cost</p>
            <lightning-formatted-number class="field-value" value={totalPrincipalInterest} format-style="currency" currency-code="USD"></lightning-formatted-number>
          </lightning-layout-item>

          <!-- Project Weeks Outstanding -->
          <lightning-layout-item size="3" class="field-group">
            <p class="field-label">Project Weeks Outstanding</p>
            <lightning-formatted-number class="field-value" value={weeksOutstanding} maximum-fraction-digits="0"></lightning-formatted-number>
          </lightning-layout-item>

          <!-- Type of Loan -->
          <lightning-layout-item size="3" class="field-group">
            <p class="field-label">Type of Loan</p>
            <lightning-formatted-text class="field-value" value="Mobilization"></lightning-formatted-text>
          </lightning-layout-item>
        </lightning-layout>

        

      <!-- Additional Metrics -->
      
        <h2 class="slds-text-heading_small slds-m-bottom_medium">Additional Metrics</h2>
        <lightning-layout multiple-rows="true"  class="loan-summary-row-2">
          <!-- Max LTV -->
          <lightning-layout-item size="3" class="field-group">
            <p class="field-label">Max LTV (maximum LTV across all weeks)</p>
            <lightning-formatted-number class="field-value" value="0"  maximum-fraction-digits="0"></lightning-formatted-number>
          </lightning-layout-item>

          <!-- Max Exposure -->
          <lightning-layout-item size="3" class="field-group">
            <p class="field-label">Max Exposure (maximum outstanding loan amount across all weeks)</p>
            <lightning-formatted-number class="field-value" value="0"  currency-code="USD"></lightning-formatted-number>
          </lightning-layout-item>
        </lightning-layout>
      
      </section>

      <!-- Divider -->
      <hr class="slds-m-vertical_medium" />

      <!-- Scope of Work vs Project Cost Table -->
      <section class="scope-work-section">
        
        <table class="slds-table slds-table_cell-buffer slds-table_bordered slds-no-row-hover">
          <thead>
            <tr class="slds-text-title_caps">
              <th scope="col"><div class="slds-truncate">Scope of Work</div></th>
              <th scope="col"><div class="slds-truncate">Project Cost</div></th>
              <th scope="col"><div class="slds-truncate">MF Loan Per Item</div></th>
            </tr>
          </thead>
          <tbody>
            <template for:each={scopeWorkData} for:item="row">
              <tr key={row.label}>
                <th scope="row"><div class="slds-truncate">{row.label}</div></th>
                <td>
                  <lightning-formatted-number
                    value={row.projectCost}
                    format-style="currency"
                    currency-code="USD">
                  </lightning-formatted-number>
                </td>
                <td>
                  <lightning-formatted-number
                    value={row.mfLoanPerItem}
                    format-style="currency"
                    currency-code="USD">
                  </lightning-formatted-number>
                </td>
              </tr>
            </template>
            <tr class="totals-row slds-theme_shade">
              <th scope="row"><div class="slds-truncate">Totals</div></th>
              <td data-label="Contract Unbalanced">
                <lightning-formatted-number
                  value={totalProjectCost}
                  format-style="currency"
                  currency-code="USD">
                </lightning-formatted-number>
              </td>
              <td data-label="Loan Reconciles">
                <lightning-formatted-number
                  value={totalMfLoan}
                  format-style="currency"
                  currency-code="USD">
                </lightning-formatted-number>
              </td>
            </tr>
          </tbody>
        </table>
      </section>


      <hr class="slds-m-vertical_medium" />

      <section class="doc-stamp-section slds-m-bottom_medium">
        <h2 class="slds-text-heading_small slds-m-bottom_small">Doc Stamps</h2>
        <lightning-layout multiple-rows="false" horizontal-align="start" style="gap: 2rem;">

          <!-- Required? -->
          <lightning-layout-item size="2" class="field-group">
            <lightning-combobox
              name="docStampsRequired"
              label="Required?"
              value={docStampsRequired}
              options={yesNoOptions}
              onchange={handleDocStampsRequiredChange}>
            </lightning-combobox>
          </lightning-layout-item>

          <!-- Doc Stamp Amount -->
          <lightning-layout-item size="2" class="field-group">
            <p class="field-label">Doc Stamp Amount</p>
            <lightning-formatted-number 
              class="field-value"
              value={docStampAmount} 
              format-style="currency" 
              currency-code="USD">
            </lightning-formatted-number>
          </lightning-layout-item>

          <!-- Union Labor? -->
          <lightning-layout-item size="2" class="field-group">
            <lightning-combobox
              name="unionLabor"
              label="Union Labor?"
              value={unionLabor}
              options={yesNoOptions}
              onchange={handleUnionLaborChange}>
            </lightning-combobox>
          </lightning-layout-item>

        </lightning-layout>
      </section>

      

      <section class="notes-section slds-m-top_medium">
        <h2 class="slds-text-heading_medium slds-m-bottom_medium">Notes for Servicer</h2>
        <lightning-textarea
          name="servicerNotes"
          label="Notes for Servicer"
          variant="label-hidden"
          placeholder="Enter contingencies needed prior to funding"
          value={servicerNotes}
          onchange={handleServicerNotesChange}
        ></lightning-textarea>
      </section>


          <!-- Three-column tables row -->
    

    <div class="slds-p-horizontal_medium slds-p-vertical_medium">
       <div class="slds-p-horizontal_medium slds-p-vertical_medium">
                <!-- SLDS grid to place three tables side by side -->
                <div class="slds-grid slds-grid_pull-padded">

                    <!-- 1) Projected Disbursements -->
                    <div class="slds-col slds-p-horizontal_small slds-size_1-of-3">
                        <div class="slds-text-heading_small slds-text-align_center slds-m-bottom_small">
                            Projected Disbursements
                        </div>
                        <table class="slds-table slds-table_cell-buffer slds-table_bordered slds-no-row-hover">
                            <thead>
                                <tr class="slds-text-title_caps slds-text-align_center">
                                    <th scope="col">Date</th>
                                    <th scope="col">Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                <template for:each={mfDisbursmentWeeks} for:item="cell">
                                    <tr key={cell.id} class="slds-text-align_center">
                                        <!-- Display the date -->
                                        <td>{cell.date}</td>
                                        <!-- Display the amount/value -->
                                        <td>{cell.value}</td>
                                    </tr>
                                </template>
                            </tbody>
                            <tfoot>
                                <tr class="slds-text-title_bold slds-text-align_center">
                                    <td>Grand Total</td>
                                    <td>{mfLoanDisbursementTotal}</td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <!-- 2) Projected Pay Apps – Net Retainage -->
                    <div class="slds-col slds-p-horizontal_small slds-size_1-of-3">
                        <div class="slds-text-heading_small slds-text-align_center slds-m-bottom_small">
                            Projected Pay Apps - Receipt
                        </div>
                        <table class="slds-table slds-table_cell-buffer slds-table_bordered slds-no-row-hover">
                            <thead>
                                <tr class="slds-text-title_caps slds-text-align_center">
                                    <th scope="col">Date</th>
                                    <th scope="col">Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                <template for:each={PayAppWeeks} for:item="cell">
                                    <tr key={cell.id} class="slds-text-align_center">
                                        <!-- Display the date -->
                                        <td>{cell.date}</td>
                                        <!-- Display the amount/value -->
                                        <td>{cell.value}</td>
                                    </tr>
                                </template>
                            </tbody>
                            <tfoot>
                                <tr class="slds-text-title_bold slds-text-align_center">
                                    <td>Grand Total</td>
                                    <td>{payAppTotal}</td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <!-- 3) Repayment Schedule -->
                    <div class="slds-col slds-p-horizontal_small slds-size_1-of-3">
                        <div class="slds-text-heading_small slds-text-align_center slds-m-bottom_small">
                            Repayment Schedule
                        </div>
                        <table class="slds-table slds-table_cell-buffer slds-table_bordered slds-no-row-hover">
                            <thead>
                                <tr class="slds-text-title_caps slds-text-align_center">
                                    <th scope="col">Date</th>
                                    <th scope="col">Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                <template for:each={Weeks} for:item="cell">
                                    <tr key={cell.id} class="slds-text-align_center">
                                        <!-- Display the date -->
                                        <td>{cell.date}</td>
                                        <!-- Display the amount/value -->
                                        <td>{cell.value}</td>
                                    </tr>
                                </template>
                            </tbody>
                            <tfoot>
                                <tr class="slds-text-title_bold slds-text-align_center">
                                    <td>Grand Total</td>
                                    <td>$ 4,347,461.75</td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                </div>
        </div>
    </div>


      
    </div>
  </lightning-card>
</template>