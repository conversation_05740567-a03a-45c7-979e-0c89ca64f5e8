/* Ensure the container itself allows for proper internal layout */
.cashflow-container {
    border: 1px solid #e0e0e0; /* Lighter border for the main card */
    background-color: #ffffff;
}

.custom-app-header {
    background-color: #f3f4f6; /* Standard SLDS Page Header background */
    border-bottom: 1px solid #dddbda;
    min-height: 70px; /* Increased min-height for better vertical spacing */
    padding: var(--slds-spacing-small) var(--slds-spacing-medium); /* Consistent padding */
    box-sizing: border-box;
}

.header-content-wrapper {
    display: flex;
    padding-left: var(--lwc-spacingMedium, 1rem);
    /* align-items: center; Try center alignment for all items in the bar */
    align-items: flex-end; /* Or stick with flex-end if titles should push tabs down */
    width: 100%;
}

/* Left Panel: Titles */
.header-left-panel {
    flex-grow: 0; /* Don't grow */
    flex-shrink: 0; /* Don't shrink unduly */
    /* width: 25%; /* Assign a more fixed width or use flex-basis */
    margin-right: var(--slds-spacing-large); /* Space between title and tabs */
    align-self: center; /* Try to vertically center the title block */
}

.custom-app-header .header-main-title {
    font-size: 0.75rem; /* 12px */
    color: #54698d; /* Bluish-grey from Figma (approx) */
    font-weight: 600; /* Slightly bolder for caps */
    text-transform: uppercase;
    margin-bottom: var(--slds-spacing-xx-small); /* Small space below "CASH FLOW" */
    line-height: 1;
}

.custom-app-header .header-project-name {
    font-size: 1.125rem; /* 18px, slightly smaller than before for balance */
    font-weight: 700; /* Bold */
    color: #080707;
    line-height: 1.2;
    margin-top: 5px;
    white-space: nowrap; /* Prevent wrapping if possible */
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Middle Panel: App-Level Tabs */
.header-middle-panel {
    flex-grow: 1; /* Allow tabs to take available space */
    display: flex;
    justify-content: flex-start;
    align-items: flex-end; /* Align tabs to the bottom of this panel */
    min-width: 0; /* Important for flex children that might overflow */
    margin-bottom: -18px;
    margin-left: -80px;
}

.app-level-tabs .slds-tabs_default__nav {
    padding-bottom: 0;
    border-bottom: none;
    line-height: normal; /* Reset line height */
}

.app-level-tabs .slds-tabs_default__item {
    margin-right: var(--slds-spacing-x-small); /* Reduced space between tabs */
}

.app-level-tabs .slds-tabs_default__item .slds-tabs_default__link {
    padding: var(--slds-spacing-x-small) var(--slds-spacing-small); /* Smaller padding for compact tabs */
    color: #3e3e3c;
    font-size: 14px;
    padding-bottom: 15px;
    font-family: Proxima Nova;
     font-weight: 600px;
    border: none; /* Remove all borders */
    border-bottom: 4px solid transparent; /* Space for active indicator */
    text-decoration: none;
    transition: color 0.1s linear, border-color 0.1s linear;
    line-height: var(--lwc-lineHeightButton, 1.875rem); /* Align text vertically */
    height: auto; /* Let padding define height */
}

.app-level-tabs .slds-tabs_default__item.slds-is-active .slds-tabs_default__link {
    color: #000000; /* Salesforce blue for active tab text */
    background-color: transparent;
    border-bottom-color: #44A569; /* Active tab bottom border */
    box-shadow: none;
}
.app-level-tabs .slds-tabs_default__item:not(.slds-is-active):hover .slds-tabs_default__link {
    background-color: transparent;
    /* color: #2CAC68;
    border-bottom-color: #2CAC68;  */
}

.slds-tabs_default__item.slds-is-active:after {
     background-color: transparent;
    color: #44A569;
    border-bottom-color: #44A569; /* Lighter border on hover for inactive */
     line-height: 1.5; 

     padding-bottom: 10px;
}

/* Right Panel: Controls */
.header-right-panel {
    flex-grow: 0; /* Don't grow */
    flex-shrink: 0; /* Don't shrink */
    /* width: 35%; /* Or assign a more fixed width / use flex-basis */
    display: flex;
    align-items: center; /* Vertically center controls in their panel */
    justify-content: flex-end;
}
.header-right-panel .version-display {
    color: #747474;
    font-size: 10px;
    white-space: nowrap;
    margin-right: var(--slds-spacing-small); /* Space after version */
    font-family: Proxima Nova;
}

.selected-version {
    height: 33px;
    padding-bottom: 5px;
}

.version-combobox {
     width: 65px; 
    font-size: 0.75rem; 
    line-height: 1.2;
    color: #747474;
  
}

.slds-dropdown.version-dropdown {
    width: 60px !important;
}


.header-right-panel .lightning-button,
.header-right-panel .lightning-button-menu,
.header-right-panel .lightning-combobox {
    margin-left: var(--slds-spacing-x-small); /* Consistent spacing between controls */
}

/* Specific button styling for the "Placeholder" button menu to match Figma */
.placeholder-button-menu .slds-button {
    font-weight: 600; /* Match other text elements */
    color: #3e3e3c; /* Dark grey text */
    background-color: #ffffff; /* White background */
    border: 1px solid #c9c9c9; /* Standard border */
    padding-left: var(--slds-spacing-small);
    padding-right: var(--slds-spacing-small);
}
.placeholder-button-menu .slds-button:hover {
    background-color: #f3f4f6; /* Slight hover */
}


/* Main Content Tabset */
.main-content-tabset.slds-tabs_standard {
    background-color: #ffffff;
    border-top: 1px solid #dddbda; /* Clear separation from header */
    padding-top: var(--sds-c-tabs-scoped-spacing-block-start, 0.5rem); /* Add some space above tab content */

}
.main-content-tabset .slds-tabs_standard__item.slds-is-active .slds-tabs_standard__link {
    font-weight: 700; /* Bold active main tab */
    color: #080707;
}
.main-content-tabset .slds-tabs_standard__item .slds-tabs_standard__link {
    color: #3e3e3c;
    font-size: 0.875rem; /* Standard text size */
}
.slds-card__body_inner.slds-p-top_none {
    padding-top: 0 !important;
}

/* Footer (no changes from before, just for completeness) */
.footer-section {
    border-top: 1px solid #dddbda;
    background-color: #f3f4f6;
    font-weight: normal;
    color: #3e3e3c;
    text-align: center;
    padding: var(--slds-spacing-small);
   
}

.selected-tab {
    color: black !important;
    border-bottom-color: #44A569 !important;
}
.custom-green-button {
    background-color: #44A569; /* Bootstrap-like green */
    border: 1px solid #44A569;
    color: #fff;
    padding: 0.5rem 1rem;
    height: 32px;
    width: 200px;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s ease, border-color 0.2s ease;
}

.spinnerHolder {
    position: relative;
    display: inline-block;
    width: 80px;
    height: 80px;
    left:47%;
}

/* CSS file (e.g., myComponent.css) */