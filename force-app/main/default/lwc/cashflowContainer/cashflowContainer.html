<template>
    <div class="cashflow-container slds-card">

        <div class="slds-page-header slds-page-header_joined slds-page-header_bleed custom-app-header">
            <div class="slds-grid slds-gutters slds-wrap  slds-p-vertical_small header-content-wrapper">
                <div
                    class="slds-col slds-has-flexi-truncate slds-size_1-of-1 slds-medium-size_4-of-12 slds-large-size_2-of-12 header-left-panel">
                    <p class="slds-text-title_caps slds-line-height_reset header-main-title">Cash Flow</p>
                    <h1 class="slds-page-header__title slds-m-right_small slds-align-middle slds-truncate header-project-name"
                        title={projectName}>
                        {projectName}
                    </h1>
                </div>

                <div class="slds-col slds-size_1-of-1 slds-medium-size_4-of-12 slds-large-size_7-of-12 header-middle-panel slds-align-bottom"
                    style="overflow-x: auto; white-space: nowrap; -ms-overflow-style: none; scrollbar-width: none;">
                    <div class="app-level-tabs" style="display: inline-block;">
                        <ul class="slds-tabs_default__nav" role="tablist" style="display: inline-flex;">
                            <li class={appHeaderTabCoverPageWriteUp} title="Cover Page Write Up" role="presentation"
                                onclick={handleAppHeaderTabClick} data-tabvalue="coverPageWriteUp">
                                <a class="slds-tabs_default__link" href="javascript:void(0);" role="tab">Cover Page
                                    Write Up</a>
                            </li>
                            <li class={appHeaderTabCashFlow} title="Project CashFlow" role="presentation"
                                onclick={handleAppHeaderTabClick} data-tabvalue="appCashFlow">
                                <a class="slds-tabs_default__link" href="javascript:void(0);" role="tab">Cash Flow</a>
                            </li>
                            <li class={appHeaderTabCalculator} title="Calculator" role="presentation"
                                onclick={handleAppHeaderTabClick} data-tabvalue="Calculator">
                                <a class="slds-tabs_default__link" href="javascript:void(0);" role="tab">Calculator</a>
                            </li>
                            <li class={appHeaderTabDealSummary} title="Deal Summary" role="presentation"
                                onclick={handleAppHeaderTabClick} data-tabvalue="dealSummary">
                                <a class="slds-tabs_default__link" role="tab" data-value="Deal Summary">Deal Summary</a>
                            </li>
                            <li class={appHeaderTabContractProjectInfo} title="Contract and Project Info"
                                role="presentation" onclick={handleAppHeaderTabClick}
                                data-tabvalue="contractAndProjectInfo">
                                <a class="slds-tabs_default__link" role="tab"
                                    data-value="Contract and Project Info">Contract and Project Info</a>
                            </li>
                            <li class={appHeaderTabCommitteeFlow} title="Committee Flow" role="presentation"
                                onclick={handleAppHeaderTabClick} data-tabvalue="committeeFlow">
                                <a class="slds-tabs_default__link" role="tab" data-value="Committee Flow">Committee
                                    Flow</a>
                            </li>
                            <li class={appHeaderTabFactorDetails} title="Factor Details" role="presentation"
                                onclick={handleAppHeaderTabClick} data-tabvalue="factorDetails">
                                <a class="slds-tabs_default__link" role="tab" data-value="Factor Details">Factor
                                    Details</a>
                            </li>
                            <li class={appHeaderTabArTracker} title="AR Tracker" role="presentation"
                                onclick={handleAppHeaderTabClick} data-tabvalue="arTracker">
                                <a class="slds-tabs_default__link" role="tab" data-value="AR Tracker">AR Tracker</a>
                            </li>
                            <li class={appHeaderTabArChart} title="AR Chart" role="presentation"
                                onclick={handleAppHeaderTabClick} data-tabvalue="arChart">
                                <a class="slds-tabs_default__link" role="tab" data-value="AR Chart">AR Chart</a>
                            </li>
                            <li class={appHeaderTabHandoff} title="Handoff" role="presentation"
                                onclick={handleAppHeaderTabClick} data-tabvalue="handoff">
                                <a class="slds-tabs_default__link" role="tab" data-value="Handoff">Handoff</a>
                            </li>
                            <li class={appHeaderTabTermSheet} title="Term Sheet" role="presentation"
                                onclick={handleAppHeaderTabClick} data-tabvalue="termSheet">
                                <a class="slds-tabs_default__link" role="tab" data-value="Term Sheet">Term Sheet</a>
                            </li>
                        </ul>
                    </div>
                </div>

                <div
                    class="slds-col slds-size_1-of-1 slds-medium-size_4-of-12 slds-large-size_3-of-12 header-right-panel slds-text-align_right">
                    <span class="slds-text-body_small slds-m-right_small slds-align-middle version-display">Version:  </span>
                    <lightning-combobox name="version" value={selectedVersion} options={versionOptions}
                        onchange={handleVersionChange} class="version-combobox slds-m-left_small slds-m-top_xx-small"
                        variant="label-hidden">
                    </lightning-combobox>


                    <!-- <lightning-button
                        variant="brand"
                        label="Save" title="Save Data"
                        onclick={handleGlobalSave}
                        disabled={isLoading}
                        class="slds-m-left_xx-small slds-m-left_small slds-m-right_small"> 
                         </lightning-button>-->
                    <button
                            class="custom-green-button slds-button slds-m-left_xx-small slds-m-left_small slds-m-right_small"
                            onclick={handleGlobalSave}
                            disabled={isLoading}>
                            Save
                        </button>

                    <!-- <lightning-button
                        label="Placeholder" variant="border" icon-size="x-small"
                        menu-alignment="right"
                        class="slds-m-left_xx-small  placeholder-button-menu" style=" border-radius: 0%;">
                    </lightning-button> -->
                    <!-- <lightning-button-menu
                        label="Placeholder" variant="border" icon-size="x-small"
                        menu-alignment="right"
                        class=" slds-xx-right_small placeholder-button-menu" style=" border-radius: 0%;">
                        <lightning-menu-item value="Action1" label="Action 1"></lightning-menu-item>
                        <lightning-menu-item value="Action2" label="Action 2"></lightning-menu-item>
                        <lightning-menu-item value="Action3" label="Action 3"></lightning-menu-item>
                    </lightning-button-menu>-->
                    <lightning-button-group class="slds-m-left_xx-small  placeholder-button-menu">
                        <lightning-button label="Placeholder"></lightning-button>
                        <lightning-button label="Placeholder"></lightning-button>
                        <lightning-button-menu alternative-text="Show menu" variant="border-filled">
                            <lightning-menu-item label="Menu Item One" value="item1"></lightning-menu-item>
                            <lightning-menu-item label="Menu Item Two" value="item2"></lightning-menu-item>
                            <lightning-menu-item label="Menu Item Three" value="item3"></lightning-menu-item>
                        </lightning-button-menu>
                    </lightning-button-group>
                </div>

            </div>
        </div>
        <div class="slds-card__body slds-card__body_inner slds-p-top_none">
            <template if:true={isLoading}>
                <div class="spinnerHolder">
                    <lightning-spinner alternative-text="Loading" size="large"></lightning-spinner>
                </div>
            </template>
            <template if:true={error}>
            </template>

            <template if:false={isLoading}>
                <!-- <lightning-tabset active-tab-value={activeTab} onselect={handleTabSelect} variant="standard" class="main-content-tabset">
                    <lightning-tab label="Cover Page Write Up" value="coverWriteUp"> -->
                <template if:true={isCoverWriteUpActive}>
                    <c-cashflow-cover-write-up-tab record-id={recordId} cover-data={coverPageData}>
                    </c-cashflow-cover-write-up-tab>
                </template>
                <!-- </lightning-tab>
                    <lightning-tab label="Project Cashflows" value="projectCashflows"> -->
                <template if:true={isProjectCashflowsActive}>
                    <c-cashflow-project-cashflow record-id={recordId} project-name-prop={projectName}
                        week-columns={weekColumns} cash-flow-data={cashFlowData} project-data={projectData} active-cashflow={_activeCashflow} is-loading={isLoadingForChild}
                        variable-fixed-options={variableFixedOptions}
                        payment-frequency-options={paymentFrequencyOptions} parent-selected-version={selectedVersion}
                        parent-selected-weeks={selectedWeeks} parent-selected-view={selectedView}
                        parent-weeks-options={weeksOptions} parent-view-options={viewOptions}
                        onpopoversave={handlePopoverSave} onpopoverunlink={handlePopoverUnlink}
                        ontoasterror={handleChildToastError}
                        onaddrow = {handleAddNewRow} ondeleterow={handleDeleteRow} onweekcolumnschange={handleWeekColumnsChange}>
                    </c-cashflow-project-cashflow>
                </template>

                <template if:true={isCalculatorActive}>
                    <c-cashflow-calculator-tab record-id={recordId} calculator-data={calculatorData}
                        week-columns={weekColumns} cash-flow-data={cashFlowData} disbursements={disbursementData} pay-applications={payApplicationData} transactions={transactionData}
                        project-data={projectData}
                        oncashflowdataupdate={handleFromCalculatorTab}
                        oncalculatedit={handleCalculatorEdit}>
                    </c-cashflow-calculator-tab>
                </template>
                <template if:true={isDealSummaryActive}>
                    <div class="slds-p-around_medium slds-tabs_default__content slds-show" role="tabpanel">
                        <div class="slds-section slds-is-open">
                            <h3 class="slds-section__title slds-theme_shade">
                                <span class="slds-truncate slds-p-horizontal_small" title="Deal Summary">Deal Summary</span>
                            </h3>
                            <div aria-hidden="false" class="slds-section__content">
                                <div class="slds-grid slds-gutters slds-p-horizontal_small slds-p-bottom_small">
                                    <div class="slds-col slds-size_1-of-1 slds-m-bottom_small">
                                        <p class="slds-text-heading_small">Content for Deal Summary</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>

                <template if:true={isContractProjectInfoActive}>
                    <div class="slds-p-around_medium slds-tabs_default__content slds-show" role="tabpanel">
                        <div class="slds-section slds-is-open">
                            <h3 class="slds-section__title slds-theme_shade">
                                <span class="slds-truncate slds-p-horizontal_small" title="Contract and Project Info">Contract and Project Info</span>
                            </h3>
                            <div aria-hidden="false" class="slds-section__content">
                                <div class="slds-grid slds-gutters slds-p-horizontal_small slds-p-bottom_small">
                                    <div class="slds-col slds-size_1-of-1 slds-m-bottom_small">
                                        <p class="slds-text-heading_small">Content for Contract and Project Info</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>

                <template if:true={isCommitteeFlowActive}>
                    <div class="slds-p-around_medium slds-tabs_default__content slds-show" role="tabpanel">
                        <div class="slds-section slds-is-open">
                            <h3 class="slds-section__title slds-theme_shade">
                                <span class="slds-truncate slds-p-horizontal_small" title="Committee Flow">Committee Flow</span>
                            </h3>
                            <div aria-hidden="false" class="slds-section__content">
                                <div class="slds-grid slds-gutters slds-p-horizontal_small slds-p-bottom_small">
                                    <div class="slds-col slds-size_1-of-1 slds-m-bottom_small">
                                        <p class="slds-text-heading_small">Content for Committee Flow</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>

                <template if:true={isFactorDetailsActive}>
                    <div class="slds-p-around_medium slds-tabs_default__content slds-show" role="tabpanel">
                        <div class="slds-section slds-is-open">
                            <h3 class="slds-section__title slds-theme_shade">
                                <span class="slds-truncate slds-p-horizontal_small" title="Factor Details">Factor Details</span>
                            </h3>
                            <div aria-hidden="false" class="slds-section__content">
                                <div class="sds-grid slds-gutters slds-p-horizontal_small slds-p-bottom_small">
                                    <div class="slds-col slds-size_1-of-1 slds-m-bottom_small">
                                        <p class="slds-text-heading_small">Content for Factor Details</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>

                <template if:true={isArTrackerActive}>
                    <div class="slds-p-around_medium slds-tabs_default__content slds-show" role="tabpanel">
                        <div class="slds-section slds-is-open">
                            <h3 class="slds-section__title slds-theme_shade">
                                <span class="slds-truncate slds-p-horizontal_small" title="AR Tracker">AR Tracker</span>
                            </h3>
                            <div aria-hidden="false" class="slds-section__content">
                                <div class="slds-grid slds-gutters slds-p-horizontal_small slds-p-bottom_small">
                                    <div class="slds-col slds-size_1-of-1 slds-m-bottom_small">
                                        <p class="slds-text-heading_small">Content for AR Tracker</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>

                <template if:true={isArChartActive}>
                    <div class="slds-p-around_medium slds-tabs_default__content slds-show" role="tabpanel">
                        <div class="slds-section slds-is-open">
                            <h3 class="slds-section__title slds-theme_shade">
                                <span class="slds-truncate slds-p-horizontal_small" title="AR Chart">AR Chart</span>
                            </h3>
                            <div aria-hidden="false" class="slds-section__content">
                                <div class="slds-grid slds-gutters slds-p-horizontal_small slds-p-bottom_small">
                                    <div class="slds-col slds-size_1-of-1 slds-m-bottom_small">
                                        <p class="slds-text-heading_small">Content for AR Chart</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>

                <template if:true={isHandoffActive}>
                    <!-- <div class="slds-p-around_medium slds-tabs_default__content slds-show" role="tabpanel">
                        <div class="slds-section slds-is-open">
                            <h3 class="slds-section__title slds-theme_shade">
                                <span class="slds-truncate slds-p-horizontal_small" title="Handoff">Handoff</span>
                            </h3>
                            <div aria-hidden="false" class="slds-section__content">
                                <div class="slds-grid slds-gutters slds-p-horizontal_small slds-p-bottom_small">
                                    <div class="slds-col slds-size_1-of-1 slds-m-bottom_small">
                                        <p class="slds-text-heading_small">Content for Handoff</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div> -->
                    <c-general-loan-summary  project-name-prop={projectName}
                        week-columns={weekColumns} cash-flow-data={cashFlowData} project-data={projectData} account={_activeCashflow} transactions={transactionData} active-cashflow={_activeCashflow} account-data={_account}>
                    </c-general-loan-summary>
                </template>

                <template if:true={isTermSheetActive}>
                    <div class="slds-p-around_medium slds-tabs_default__content slds-show" role="tabpanel">
                        <div class="slds-section slds-is-open">
                            <h3 class="slds-section__title slds-theme_shade">
                                <span class="slds-truncate slds-p-horizontal_small" title="Term Sheet">Term Sheet</span>
                            </h3>
                            <div aria-hidden="false" class="slds-section__content">
                                <div class="slds-grid slds-gutters slds-p-horizontal_small slds-p-bottom_small">
                                    <div class="slds-col slds-size_1-of-1 slds-m-bottom_small">
                                        <p class="slds-text-heading_small">Content for Term Sheet</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
                <!-- </lightning-tab>
                    <lightning-tab label="DEAL SUMMARY" value="dealSummary">
                        <template if:true={isDealSummaryActive}> <p class="slds-p-around_medium">Deal Summary Content Placeholder</p>
                        </template>
                    </lightning-tab>
                    <lightning-tab label="Contract and Project Info" value="contractProjectInfo">
                        <template if:true={isContractProjectInfoActive}> <p class="slds-p-around_medium">Contract and Project Info Placeholder</p> </template>
                    </lightning-tab>
                    <lightning-tab label="Committee Flow" value="committeeFlow">
                        <template if:true={isCommitteeFlowActive}> <p class="slds-p-around_medium">Committee Flow Placeholder</p> </template>
                    </lightning-tab>
                    <lightning-tab label="Factor Details" value="factorDetails">
                        <template if:true={isFactorDetailsActive}> <p class="slds-p-around_medium">Factor Details Placeholder</p> </template>
                    </lightning-tab>
                    <lightning-tab label="AR Tracker" value="arTracker">
                        <template if:true={isArTrackerActive}> <p class="slds-p-around_medium">AR Tracker Placeholder</p> </template>
                    </lightning-tab>
                    <lightning-tab label="AR Chart" value="arChart">
                        <template if:true={isArChartActive}> <p class="slds-p-around_medium">AR Chart Placeholder</p> </template>
                    </lightning-tab>
                    <lightning-tab label="Handoff" value="handoff">
                        <template if:true={isHandoffActive}> <p class="slds-p-around_medium">Handoff Placeholder</p> </template>
                    </lightning-tab>
                    <lightning-tab label="Term Sheet" value="termSheet">
                        <template if:true={isTermSheetActive}> <p class="slds-p-around_medium">Term Sheet Placeholder</p> </template>
                    </lightning-tab> -->
                <!-- </lightning-tabset> -->
            </template>
        </div>

        <div class="slds-card__footer slds-p-horizontal_medium slds-p-vertical_small footer-section">
            Quarterly Performance (Placeholder from Container)
        </div>
    </div>
</template>