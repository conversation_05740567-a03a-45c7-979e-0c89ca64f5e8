/* eslint-disable */
import { LightningElement, api, track, wire } from 'lwc';
import getCashflowData from '@salesforce/apex/CashflowDataService.getCashflowData';
import MFL_LOGO from '@salesforce/resourceUrl/mfLogoLwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { CurrentPageReference } from 'lightning/navigation';

// ——————————————————————————————————————————————————————————————————————————————
// If you want to pull “all expense categories” from a picklist, re-import these:
import CASHFLOW_LINE_ITEM_OBJECT from '@salesforce/schema/Cashflow_Line_Item__c';
import { getObjectInfo, getPicklistValues } from 'lightning/uiObjectInfoApi';
import CATEGORY_FIELD from '@salesforce/schema/Cashflow_Line_Item__c.Line_Item_Category__c';
// ——————————————————————————————————————————————————————————————————————————————

const CALCULATED_ROW_TYPES = ['DATA_CALCULATED_TOTAL', 'DATA_CALCULATED_SUMMARY'];
const CHILD_LINE_ITEMS_REL_NAME = 'Cashflow_Line_Item_Details__r';
const POPOVER_ROW_TYPES = [
    'DATA_EXPENSE',
    'DATA_FINANCING_EDITABLE',
    'DATA_REVENUE_EDITABLE',
    'DATA_READONLY'
];

const createSectionHeader = (label, showAdd = false) => {
    const id = `section-${label.replace(/\s+/g, '').toLowerCase()}`;
    return {
        id,
        label,
        isSectionHeader: true,
        sectionId: id,
        showAddButton: showAdd,
        type: 'SECTION_HEADER',
        level: 0,
        weeks: [],
        calculatedTotal: null,
        rowClass: 'slds-text-title_caps section-header'
    };
};

export default class LoanSummaryClientView extends LightningElement {
    // ────────────────────────────────────────────────────────────────────────────
    // 1) Reactive / tracked properties
    // ────────────────────────────────────────────────────────────────────────────
    @api recordId;
    @track isLoading = true;
    @track error;

    // Header data
    logoUrl = MFL_LOGO;
    @track customerName = '';
    @track approvedFunding = '';
    @track projectName = '';
    // projectGross is now a getter, see section 2

    // Raw data containers
    @track weekColumns = [];
    @track cashFlowData = [];
    @track disbursementData = [];
    @track payApplicationData = [];
    @track transactions = [];

    // If you need “all expense categories” from the picklist:
    @track expenseCategoryOptions = [];
    defaultRecordTypeId;
    @track loanDate;
    @track matureDate;
    // Internal (non-@track)
    _projectData = null;
    _activeCashflow = null;
    _forecastLines = [];
    _currentProjectId = null;
    _currentCashflowId = null;
    _urlCashflowId = null;

    @wire(CurrentPageReference)
    pageRef;

    get projectGross() {
        const payAppRow = this.cashFlowData.find(r => r.id === 'PayAppSubmitted');
        if (!payAppRow) {
            return '$ 0.00';
        }
        const total = payAppRow.weeks.reduce((sum, cell) => sum + (parseFloat(cell.value) || 0), 0);
        return `$ ${total.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    }

    // 2.2) The “PROJECT COSTS” rows (header + every cost row)
    get projectCostRows() {
        const allRows = this.cashFlowData || [];
        const startIndex = allRows.findIndex(
            r => r.isSectionHeader && r.sectionId === 'section-projectcosts'
        );
        if (startIndex < 0) return [];

        const headerRow = { ...allRows[startIndex] };
        const costRows = [];
        for (let i = startIndex + 1; i < allRows.length; i++) {
            if (allRows[i].isSectionHeader) break;
            costRows.push({ ...allRows[i] });
        }
        return [headerRow, ...costRows];
    }

    // 2.3) halfTotalCols → used for colspan in the table’s section header
    get totalColumnCountInternalPlusTotal() {
        const totalCols = (this.weekColumns?.length || 0) + 1;
        return totalCols;
    }
    get halfTotalCols() {
        const total = this.totalColumnCountInternalPlusTotal || 0;
        return Math.floor(total / 2);
    }

    // 2.4) “MF Disbursement” weeks (for the bottom table)
    get mfDisbursmentWeeks() {
        const row = this.cashFlowData.find(r => r.id === 'MFLoanDisb');
        return row ? row.weeks : [];
    }

    // 2.5) “PayApp” weeks (for the middle bottom table)
    get PayAppWeeks() {
        const row = this.cashFlowData.find(r => r.id === 'ProjectedNetPayApp');
        return row ? row.weeks : [];
    }

    // 2.6) Totals for those two bottom tables
    get mfLoanDisbursementTotal() {
        return this.mfDisbursmentWeeks
            .reduce((sum, cell) => sum + (parseFloat(cell.value) || 0), 0)
            .toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 });
    }
    get payAppTotal() {
        return this.PayAppWeeks
            .reduce((sum, cell) => sum + (parseFloat(cell.value) || 0), 0)
            .toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 });
    }

    get totalProjectCost() {
        const row = this.cashFlowData.find(r => r.id === 'TotalProjectCost');
        return row ? row.calculatedTotal : 0;
    }

    get estimatedGross(){
        const numericPayAppTotal = parseFloat(this.payAppTotal.replace(/,/g, ''));
        const rounded = Math.round(numericPayAppTotal / 100) * 100;
        const a = Number(rounded);
        const b = Number(this.totalProjectCost);
        if (!isFinite(a) || !isFinite(b) || a === 0) {
            return '0';
        }

        const raw = (a - b) / a;
        return parseFloat(raw.toFixed(2)); 

    }

    get originationFee() {
        if (!this._currentProjectId) return 0;
        return this.transactions
            .filter(tx => tx.Project__c === this.projectId)
            .reduce((sum, tx) => {
                return sum +
                    (tx.Default_Fee_Application__c || 0) +
                    (tx.Doc_Stamp_Fees_Application__c || 0) +
                    (tx.Late_Fee_Application__c || 0) +
                    (tx.Legal_Fees_Application__c || 0);
            }, 0);
    }

    get interestIncome() {
        if (!this.projectId) return 0;
        return this.transactions
            .filter(tx => tx.Project__c === this.projectId)
            .reduce((sum, tx) => sum + (tx.Interest_Application__c || 0), 0);
    }

    get projectedTotalFinancingExpense(){
        return this.originationFee + this.interestIncome;
    }

    get totalLessRetainage() {
        const row = this.cashFlowData.find(r => r.id === 'LessRetainage');
        if (!row) {
            return 0;
        }
        const rawTotal = row.calculatedTotal;  
        console.log('rawtotal '+rawTotal);         
        const roundedHundreds = Math.round(rawTotal / 100) * 100;
        console.log('roundedHundreds '+roundedHundreds);
        return roundedHundreds;                           
    }
        get borrowerPreFinancingGrossProfit(){
    const numericPayAppTotal = parseFloat(this.payAppTotal.replace(/,/g, ''));
        const rounded = Math.round(numericPayAppTotal / 100) * 100;
        const a = Number(rounded);
        const b = Number(this.totalProjectCost);
        console.log('a'+a);
        console.log('b'+b);
        if (!isFinite(a) || !isFinite(b) || a === 0) {
            return '0';
        }

        const raw = (a - b);
        console.log('raw '+raw);
        const rawdata = raw - this.totalLessRetainage;
        return rawdata; 
        }

        get projectedPostFinancingGrossProfit(){
            return this.borrowerPreFinancingGrossProfit - this.projectedTotalFinancingExpense;
        }

        get projectedAdjustedGrossMargin(){
            const numerator   = Number(this.projectedPostFinancingGrossProfit);
            const denominator = Number(this.projectGross);

            // If either value is invalid or denominator is zero, bail out
            if (!isFinite(numerator) || !isFinite(denominator) || denominator === 0) {
                return 0;
            }

            // Otherwise return the raw ratio
            return numerator / denominator;

        }
    // ────────────────────────────────────────────────────────────────────────────
    // 3) Wire up picklist values for expenseCategoryOptions
    // ────────────────────────────────────────────────────────────────────────────

    @wire(getObjectInfo, { objectApiName: CASHFLOW_LINE_ITEM_OBJECT })
    handleObjectInfo({ data, error }) {
        if (data) {
            this.defaultRecordTypeId = data.defaultRecordTypeId;
        }
    }

    @wire(getPicklistValues, {
        recordTypeId: '$defaultRecordTypeId',
        fieldApiName: CATEGORY_FIELD
    })
    handleCategoryPicklist({ data, error }) {
        if (data) {
            // data.values is an array of { label, value, … }
            this.expenseCategoryOptions = data.values;
            this.renderedCallback();
        }
    }

    // ────────────────────────────────────────────────────────────────────────────
    // 4) Lifecycle & data loading
    // ────────────────────────────────────────────────────────────────────────────

    renderedCallback() {
        if (this.pageRef && !this._currentProjectId) {
            const state = this.pageRef.state;
            this._urlCashflowId = state?.c__recordId;
            this._currentProjectId = state?.c__projectId || this.recordId;

            if (this._currentProjectId) {
                this.loadCashflowDetails(this._currentProjectId, this._urlCashflowId);
            } else {
                this.error = 'Project Record ID is not available.';
                this.isLoading = false;
                this._showToast('Warning', this.error, 'warning');
            }
        }
    }

    async loadCashflowDetails(projectIdToLoad, cashflowIdFromUrl) {
        this.isLoading = true;
        this.error = undefined;

        try {
            const pageData = await getCashflowData({
                projectId: projectIdToLoad,
                cashflowId: cashflowIdFromUrl
            });

            if (!pageData) {
                throw new Error('No data returned from Apex.');
            }

            // — Store raw references
            this._projectData = pageData.project;
            this._activeCashflow = pageData.activeCashflow;
            this._forecastLines = pageData.forecastLines || [];
            console.log('cashflowLineItem ' + JSON.stringify(this._forecastLines));
            this._currentCashflowId = this._activeCashflow?.Id || null;
            this.transactions = pageData.transactions || [];

            // — Populate header fields
            this.loanDate = this._activeCashflow?.Forecast_Start_Date__c || 'N/A';
            this.matureDate = this._activeCashflow?.Forecast_End_Date__c || 'N/A';
            this.customerName = pageData.account.Name;
            this.approvedFunding = this._projectData?.Loan_Principal__c || '0';
            this.projectName = this._projectData?.Name || 'N/A';
            
            // — Build weekColumns (using Forecast_Start_Date__c or earliest Week_Start_Date__c)
            this.weekColumns = this.generateWeekColumnsFromForecast(
                this._forecastLines,
                parseInt(this._activeCashflow?.Projected_Weeks_Outstanding__c || '52', 10)
            );

            // — Convert raw data → cashFlowData (grid of rows + cells)
            this.cashFlowData = this.transformDataToCashflowRows(
                this._projectData,
                this._activeCashflow,
                this._forecastLines,
                this.weekColumns
            );

            // — Related lists (for bottom tables)
            this.disbursementData = pageData.disbursements || [];
            this.payApplicationData = pageData.payApplications || [];

            // — Recalculate all totals so every row’s “calculatedTotal” is correct
            this.recalculateAllTotals();
        } catch (err) {
            console.error('[LoanSummaryClientView] Error loading cashflow data:', err);
            this.error =
                'Failed to load cashflow data: ' +
                (err.body?.message || err.message || 'Unknown');
            this._showToast('Error', this.error, 'error');
        } finally {
            this.isLoading = false;
        }
    }

    // ────────────────────────────────────────────────────────────────────────────
    // 5) Data‐transformation & utility methods
    // ────────────────────────────────────────────────────────────────────────────

    generateWeekColumnsFromForecast(forecastLines, numberOfWeeksToDisplay = 52) {
        let overallStartDate;
        const uniqueDates = new Set();

        forecastLines.forEach(line => {
            if (line.Week_Start_Date__c) {
                uniqueDates.add(line.Week_Start_Date__c);
            }
        });

        if (uniqueDates.size > 0) {
            const sortedDates = Array.from(uniqueDates).sort(
                (a, b) => new Date(a + 'T00:00:00Z') - new Date(b + 'T00:00:00Z')
            );
            overallStartDate = new Date(sortedDates[0] + 'T00:00:00Z');
        } else if (this._activeCashflow?.Forecast_Start_Date__c) {
            overallStartDate = new Date(this._activeCashflow.Forecast_Start_Date__c + 'T00:00:00Z');
        } else {
            return this.createDefaultWeekColumns(numberOfWeeksToDisplay);
        }

        const columns = [];
        for (let i = 0; i < numberOfWeeksToDisplay; i++) {
            const currentDate = new Date(overallStartDate.valueOf());
            currentDate.setUTCDate(currentDate.getUTCDate() + i * 7);

            const dateStr = currentDate.toISOString().split('T')[0];
            columns.push({
                id: `week${i}-${dateStr}`,
                label: `${currentDate.getUTCMonth() + 1}/${currentDate.getUTCDate()}`,
                date: dateStr,
                formattedDate: `${currentDate.getUTCMonth() + 1}/${currentDate.getUTCDate()}/${String(
                    currentDate.getUTCFullYear()
                ).slice(-2)}`
            });
        }
        return columns;
    }

    createDefaultWeekColumns(numberOfWeeks) {
        let columns = [];
        let startDate = new Date();
        const localDay = startDate.getDay();
        const diff = startDate.getDate() - localDay + (localDay === 0 ? -6 : 1);
        startDate = new Date(startDate.setDate(diff));
        startDate.setUTCHours(0, 0, 0, 0);

        for (let i = 0; i < numberOfWeeks; i++) {
            const weekStartDate = new Date(startDate.valueOf());
            weekStartDate.setUTCDate(startDate.getUTCDate() + i * 7);

            const dateStr = weekStartDate.toISOString().split('T')[0];
            columns.push({
                id: `week${i}-default-${dateStr}`,
                label: `${weekStartDate.getUTCMonth() + 1}/${weekStartDate.getUTCDate()}`,
                date: dateStr,
                formattedDate: `${weekStartDate.getUTCMonth() + 1}/${weekStartDate.getUTCDate()}/${String(
                    weekStartDate.getUTCFullYear()
                ).slice(-2)}`
            });
        }
        return columns;
    }

    transformDataToCashflowRows(project, activeCashflow, forecastLines, weekColumns) {

        const forecastMapByCatAndWeek = new Map();
        const forecastMapByCatAndPlanned = new Map();

        forecastLines.forEach(parentLine => {
            const cat = parentLine.Line_Item_Category__c;
            const wk = parentLine.Week_Start_Date__c;  // “weekly” bucket
            const pd = parentLine.Planned_Date__c;     // fallback if no week

            if (cat && wk) {
                forecastMapByCatAndWeek.set(`${cat}_${wk}`, parentLine);
            }
            if (cat && pd) {
                forecastMapByCatAndPlanned.set(`${cat}_${pd}`, parentLine);
            }
        });

        // ——————————————————————————————————————————————————————  
        // 2) Helper: create an empty “weeks” array (as before)  
        // ——————————————————————————————————————————————————————  
        const createEmptyWeeksForDataRow = (rowId, rowLabel, rowType) => {
            return (weekColumns || []).map(col => {
                const hasPopover = POPOVER_ROW_TYPES.includes(rowType);
                return {
                    id: `cell-${rowId}-${col.id}`,
                    weekIdentifier: col.id,
                    value: 0,
                    weekLabel: col.label,
                    rowLabel,
                    cellClass: 'slds-text-align_center' + (hasPopover ? ' cell-clickable' : ''),
                    hasPopover,
                    date: col.date,
                    salesforceIds: [],
                    originalLineItems: [],
                    category: hasPopover ? rowLabel : null,
                    expenseCategory: hasPopover && rowType === 'DATA_EXPENSE' ? rowLabel : null,
                    variableFixed: hasPopover ? 'Fixed' : null,
                    paymentFrequency: hasPopover ? 'Weekly' : null,
                    weeklyAmount: hasPopover ? 0 : null,
                    paymentTerm: hasPopover ? 'Net 30' : null,
                    isDifference: rowType !== 'DATA_CALCULATED_SUMMARY' &&
                        rowType !== 'DATA_CALCULATED_TOTAL' &&
                        rowType !== 'DATA_READONLY'
                };
            });
        };

        // ——————————————————————————————————————————————————————  
        // 3) Helper: populate each week’s cell—first try Week_Start_Date__c, then Planned_Date__c  
        // ——————————————————————————————————————————————————————  
        const populateWeeksForRow = (weeksArray, categoryForForecast, rowType) => {
            weeksArray.forEach(weekCell => {
                // 3a) Try to match on Week_Start_Date__c first
                const wkKey = `${categoryForForecast}_${weekCell.date}`;
                let parent = forecastMapByCatAndWeek.get(wkKey);

                // 3b) If no match, fallback to Planned_Date__c
                if (!parent) {
                    const pdKey = `${categoryForForecast}_${weekCell.date}`;
                    parent = forecastMapByCatAndPlanned.get(pdKey);
                }

                if (parent) {
                    weekCell.value = parseFloat(parent.Planned_Amount__c || 0);
                    weekCell.salesforceIds = [parent.Id];
                    weekCell.originalLineItems = parent[CHILD_LINE_ITEMS_REL_NAME]
                        ? JSON.parse(JSON.stringify(parent[CHILD_LINE_ITEMS_REL_NAME]))
                        : [];
                    weekCell.weeklyAmount = weekCell.value;
                    weekCell.category = parent.Line_Item_Category__c;
                    weekCell.expenseCategory = parent.Line_Item_Category__c;

                    // …copy over your existing “pull in child‐detail defaults” logic…
                    if (weekCell.originalLineItems.length > 0) {
                        const repChild = weekCell.originalLineItems[0];
                        weekCell.variableFixed = repChild.Sub_Amount_Variation__c || parent.Sub_Amount_Variation__c || 'Fixed';
                        weekCell.paymentFrequency = repChild.Sub_Payment_Frequency__c || parent.Sub_Payment_Frequency__c || 'Weekly';
                        weekCell.paymentTerm = repChild.Sub_Payment_Terms__c || parent.Sub_Payment_Terms__c || 'Net 30';
                    } else {
                        weekCell.variableFixed = parent.Sub_Amount_Variation__c || 'Fixed';
                        weekCell.paymentFrequency = parent.Sub_Payment_Frequency__c || 'Weekly';
                        weekCell.paymentTerm = parent.Sub_Payment_Terms__c || 'Net 30';
                    }
                }

                // Finally, highlight “difference” style if needed
                weekCell.isDifference = (
                    rowType !== 'DATA_CALCULATED_SUMMARY' &&
                    rowType !== 'DATA_CALCULATED_TOTAL' &&
                    rowType !== 'DATA_READONLY'
                );
                if (weekCell.isDifference && POPOVER_ROW_TYPES.includes(rowType)) {
                    let cls = (weekCell.cellClass || '')
                        .replace('cell-clickable', '')
                        .replace('cell-difference wholelightblue', '')
                        .trim();
                    if (POPOVER_ROW_TYPES.includes(rowType)) cls += ' cell-clickable';
                    cls += ' cell-difference wholelightblue';
                    weekCell.cellClass = cls.trim();
                }
            });
        };

        const createActualDataRow = (rowId, label, type, level, categoryForForecast, isDifferenceOverride) => {
            let weeks = createEmptyWeeksForDataRow(rowId, label, type);
            if (categoryForForecast) {
                populateWeeksForRow(weeks, categoryForForecast, type);
            }

            const isDiffRow =
                typeof isDifferenceOverride === 'boolean'
                    ? isDifferenceOverride
                    : type !== 'DATA_CALCULATED_SUMMARY' && type !== 'DATA_CALCULATED_TOTAL' && type !== 'DATA_READONLY';

            weeks.forEach(w => {
                w.isDifference = isDiffRow;
                let cls = (w.cellClass || 'slds-text-align_center')
                    .replace('cell-clickable', '')
                    .replace('cell-difference wholelightblue', '')
                    .trim();
                if (POPOVER_ROW_TYPES.includes(type)) cls += ' cell-clickable';
                if (isDiffRow && POPOVER_ROW_TYPES.includes(type)) cls += ' cell-difference wholelightblue';
                w.cellClass = cls.trim();
            });

            return {
                id: rowId,
                label,
                isSectionHeader: false,
                isEditable: POPOVER_ROW_TYPES.includes(type),
                type,
                level,
                parentId: null,
                weeks,
                showTotalCell: true,
                calculatedTotal: weeks.reduce((sum, cell) => sum + (cell.value || 0), 0),
                rowClass: `data-row ${CALCULATED_ROW_TYPES.includes(type) ? 'calculated-row' : ''} ${type === 'DATA_CALCULATED_SUMMARY' ? 'summary-row' : ''
                    }`,
                indentStyle: `padding-left: ${level * 1.5}rem;`
            };
        };

        const newRows = [];
        const revenueLineItemCategories = new Set(['Invoice Submission', 'Retainage']);
        const financingLineItemCategoriesFromConfig = new Set(
            [
                activeCashflow?.MFLoanDisbursementCategory__c,
                activeCashflow?.MFLoanRepaymentCategory__c,
                activeCashflow?.OtherSSVCategory__c,
                activeCashflow?.AlternativeLoanCategory__c
            ].filter(Boolean)
        );

        // 1) PROJECT REVENUE
        newRows.push(createSectionHeader('PROJECT REVENUE'));
        newRows.push(
            createActualDataRow(
                'PayAppSubmitted',
                'Pay App to be Submitted',
                'DATA_REVENUE_EDITABLE',
                1,
                'Invoice Submission',
                true
            )
        );
        newRows.push(
            createActualDataRow('LessRetainage', 'Less Retainage', 'DATA_REVENUE_EDITABLE', 1, 'Retainage', true)
        );
        newRows.push(
            createActualDataRow('ProjectedNetPayApp', 'Projected Net Pay App', 'DATA_CALCULATED_TOTAL', 1, null, false)
        );

        // 2) PROJECT COSTS (use dynamic picklist values)
        newRows.push(createSectionHeader('PROJECT COSTS', true));

        // Map picklist options → array of strings
        console.log('expense category' + JSON.stringify(this.expenseCategoryOptions));
        // const allExpenseCategories = this.expenseCategoryOptions.map(opt => opt.value);
        const allExpenseCategories = (this.expenseCategoryOptions.length > 0)
            ? this.expenseCategoryOptions.map(opt => opt.value)
            : [
            ];
        console.log('allExpenseCategories category' + JSON.stringify(allExpenseCategories));
        // Filter out “revenue” or “financing” categories
        const expenseCategoriesForRows = allExpenseCategories.filter(
            cat => !revenueLineItemCategories.has(cat) && !financingLineItemCategoriesFromConfig.has(cat)
        );

        expenseCategoriesForRows.sort().forEach(category => {
            const rowId = `expense-${category.replace(/[^a-zA-Z0-9]/g, '')}`;
            newRows.push(createActualDataRow(rowId, category, 'DATA_EXPENSE', 1, category, true));
        });

        newRows.push(
            createActualDataRow(
                'TotalProjectCost',
                'Total Project Cost',
                'DATA_CALCULATED_TOTAL',
                1,
                null,
                false
            )
        );

        // 3) FINANCING SOURCES
        newRows.push(createSectionHeader('FINANCING SOURCES'));
        newRows.push(
            createActualDataRow(
                'MFLoanDisb',
                'MF Loan Disbursement',
                'DATA_FINANCING_EDITABLE',
                1,
                activeCashflow?.MFLoanDisbursementCategory__c || 'MF Loan Disbursement',
                true
            )
        );
        newRows.push(
            createActualDataRow('ReceiptPayApp', 'Receipt of Pay App', 'DATA_READONLY', 1, null, true)
        );
        newRows.push(
            createActualDataRow(
                'TotalSources',
                'Total Sources of Cash',
                'DATA_CALCULATED_TOTAL',
                1,
                null,
                false
            )
        );

        // 4) USES
        newRows.push(createSectionHeader('USES'));
        newRows.push(
            createActualDataRow(
                'MFLoanRepay',
                'MF Loan Repayment',
                'DATA_FINANCING_EDITABLE',
                1,
                activeCashflow?.MFLoanRepaymentCategory__c || 'MF Loan Repayment',
                true
            )
        );
        newRows.push(
            createActualDataRow(
                'ProjectCostsPaid',
                'Project Costs Paid That Week',
                'DATA_FINANCING_EDITABLE',
                1,
                null,
                true
            )
        );
        newRows.push(
            createActualDataRow(
                'otherSSV',
                "OTHER SSV PAYMENTS REQ'd",
                'DATA_FINANCING_EDITABLE',
                1,
                activeCashflow?.OtherSSVCategory__c || "OTHER SSV PAYMENTS REQ'd",
                true
            )
        );
        newRows.push(
            createActualDataRow(
                'alternativeLoanPayment',
                'Alternative Loan Payment',
                'DATA_FINANCING_EDITABLE',
                1,
                activeCashflow?.AlternativeLoanCategory__c || 'Alternative Loan Payment',
                true
            )
        );
        newRows.push(
            createActualDataRow(
                'TotalUses',
                'Total Uses of Cash',
                'DATA_CALCULATED_TOTAL',
                1,
                null,
                false
            )
        );

        // 5) CASH FLOW SUMMARY
        newRows.push(createSectionHeader('CASH FLOW SUMMARY'));
        newRows.push(
            createActualDataRow(
                'NetWeeklyCashFlow',
                'Net Weekly Cash Flow',
                'DATA_CALCULATED_SUMMARY',
                1,
                null,
                false
            )
        );
        newRows.push(
            createActualDataRow(
                'AccumulatedSurplus',
                'Accumulated Surplus/(Deficit)',
                'DATA_CALCULATED_SUMMARY',
                1,
                null,
                false
            )
        );

        return newRows;
    }

    recalculateAllTotals() {
        if (!this.cashFlowData.length || !this.weekColumns.length) return;

        const updatedData = JSON.parse(JSON.stringify(this.cashFlowData));
        const rowMap = updatedData.reduce((map, row) => {
            map[row.id] = row;
            return map;
        }, {});

        const safeNum = val => parseFloat(val) || 0;
        const safeFixed = (val, digits = 2) => safeNum(val).toFixed(digits);

        // 1) Recalculate derived rows week by week
        this.weekColumns.forEach((col, weekIdx) => {
            const getWeekVal = rowId => safeNum(rowMap[rowId]?.weeks[weekIdx]?.value);

            // Projected Net Pay App = PayAppSubmitted + LessRetainage
            if (rowMap.ProjectedNetPayApp) {
                rowMap.ProjectedNetPayApp.weeks[weekIdx].value = parseFloat(
                    safeFixed(getWeekVal('PayAppSubmitted') + getWeekVal('LessRetainage'))
                );
            }

            // TotalProjectCost = sum of all DATA_EXPENSE rows
            const expenseSum = updatedData
                .filter(r => r.type === 'DATA_EXPENSE')
                .reduce((sum, r) => sum + safeNum(r.weeks[weekIdx]?.value), 0);
            if (rowMap.TotalProjectCost) {
                rowMap.TotalProjectCost.weeks[weekIdx].value = parseFloat(safeFixed(expenseSum));
            }

            // ProjectCostsPaid = −expenseSum (unless manually set)
            if (rowMap.ProjectCostsPaid && !rowMap.ProjectCostsPaid.weeks[weekIdx].isManuallySet) {
                rowMap.ProjectCostsPaid.weeks[weekIdx].value = parseFloat(safeFixed(-expenseSum));
            }

            // ReceiptPayApp = same as ProjectedNetPayApp
            if (rowMap.ReceiptPayApp) {
                rowMap.ReceiptPayApp.weeks[weekIdx].value = parseFloat(
                    safeFixed(getWeekVal('ProjectedNetPayApp'))
                );
            }

            // TotalSources = MFLoanDisb + ReceiptPayApp
            if (rowMap.TotalSources) {
                rowMap.TotalSources.weeks[weekIdx].value = parseFloat(
                    safeFixed(getWeekVal('MFLoanDisb') + getWeekVal('ReceiptPayApp'))
                );
            }

            // TotalUses = −(MFLoanRepay + |ProjectCostsPaid| + otherSSV + alternativeLoanPayment)
            const projectCostsPaidMagnitude = Math.abs(getWeekVal('ProjectCostsPaid'));
            const totalPositiveUses =
                getWeekVal('MFLoanRepay') +
                projectCostsPaidMagnitude +
                getWeekVal('otherSSV') +
                getWeekVal('alternativeLoanPayment');
            if (rowMap.TotalUses) {
                rowMap.TotalUses.weeks[weekIdx].value = parseFloat(safeFixed(-totalPositiveUses));
            }

            // NetWeeklyCashFlow = TotalSources + TotalUses
            if (rowMap.NetWeeklyCashFlow) {
                rowMap.NetWeeklyCashFlow.weeks[weekIdx].value = parseFloat(
                    safeFixed(getWeekVal('TotalSources') + getWeekVal('TotalUses'))
                );
            }
        });

        // 2) Accumulate “AccumulatedSurplus” across weeks
        let accumulatedSurplusVal = 0;
        this.weekColumns.forEach((col, weekIdx) => {
            const netWeeklyFlow = safeNum(rowMap.NetWeeklyCashFlow?.weeks[weekIdx]?.value);
            accumulatedSurplusVal += netWeeklyFlow;
            if (rowMap.AccumulatedSurplus) {
                rowMap.AccumulatedSurplus.weeks[weekIdx].value = parseFloat(
                    safeFixed(accumulatedSurplusVal)
                );
            }
        });

        // 3) Recompute each row’s calculatedTotal
        updatedData.forEach(row => {
            if (!row.isSectionHeader) {
                row.calculatedTotal = parseFloat(
                    safeFixed(row.weeks.reduce((sum, cell) => sum + safeNum(cell.value), 0))
                );
                if (row.id === 'AccumulatedSurplus' && row.weeks.length) {
                    row.calculatedTotal = parseFloat(
                        safeFixed(row.weeks[row.weeks.length - 1].value)
                    );
                }
                row.weeks.forEach(cell => {
                    let cellClasses = cell.computedClass || cell.cellClass || 'slds-text-align_center';
                    cellClasses = cellClasses
                        .replace(' cell-positive-background', '')
                        .replace(' cell-negative-background', '')
                        .trim();
                    if (row.type === 'DATA_CALCULATED_SUMMARY') {
                        if (cell.value >= 0) cellClasses += ' cell-positive-background';
                        else if (cell.value < 0) cellClasses += ' cell-negative-background';
                    }
                    cell.computedClass = cellClasses.trim();
                });
            }
        });

        this.cashFlowData = updatedData;
    }

    // ────────────────────────────────────────────────────────────────────────────
    // 6) Helper: showToast
    // ────────────────────────────────────────────────────────────────────────────

    _showToast(title, message, variant) {
        this.dispatchEvent(
            new ShowToastEvent({
                title,
                message,
                variant,
                mode: 'dismissable'
            })
        );
    }
}