<template>
    <template if:true={isLoading}>
        <div class="spinnerHolder">
            <lightning-spinner alternative-text="Loading" size="large"></lightning-spinner>
        </div>
    </template>
    <template if:true={error}>
    </template>
    <template if:false={isLoading}>
        <div class="slds-card slds-m-around_medium">

            <div class="slds-grid slds-grid_align-center slds-p-bottom_small">
                <div class="slds-col">
                    <img
                        src={logoUrl}
                        alt="Mobilization Funding LLC"
                        style="max-height:70px; width:auto;"
                    />
                </div>
            </div>

            <!-- 2) CENTERED BANNER TEXT ROW -->
            <div class="slds-grid slds-grid_align-center slds-p-bottom_small">
                <div class="slds-col slds-size_11-of-12">
                    <p class="slds-text-align_center slds-text-title_caps"
                        style="font-size:0.8rem; line-height:1.2rem;">
                        PURSUANT TO CREDIT AND SECURITY AGREEMENT DATED JULY 3, 2018, BETWEEN VALLEY NATIONAL BANK AND
                        MOBILIZATION FUNDING, LLC, AS THE SAME MAY BE AMENDED OR MODIFIED FROM TIME TO TIME
                    </p>
                </div>
            </div>

            <!-- 3) CENTERED INFORMATIONAL PARAGRAPH ROW -->
            <div class="slds-grid slds-grid_align-center">
                <div class="slds-col slds-size_11-of-12">
                    <p class="slds-text-align_center slds-text-body_regular"
                        style="font-size:0.75rem; line-height:1.3rem;">
                        This Term sheet is being presented for the purpose of setting forth the terms upon which
                        Mobilization Funding II, LLC. would agree to advance Loan amount(s) to the undersigned Borrower.
                        This Term sheet is for informational purposes only and the terms set forth herein are not
                        binding on Mobilization Funding II, LLC. or upon the undersigned until such time as such terms
                        are set forth in a formal Loan Agreement and executed by all parties.
                    </p>
                </div>
            </div>
        </div>

        <div class="slds-p-horizontal_medium slds-p-vertical_medium">
            <table class="slds-table slds-table_cell-buffer slds-no-row-hover">
                <tbody>
                    <tr>
                        <td class="slds-text-title_bold">Customer:</td>
                        <td>{customerName}</td>
                        <td class="slds-text-title_bold">Approved MF Loan:</td>
                        <td>$ {approvedFunding}</td>
                    </tr>
                    <tr>
                        <td class="slds-text-title_bold">Project:</td>
                        <td>{projectName}</td>
                        <td class="slds-text-title_bold">Project Gross Contract Value:</td>
                        <td>{projectGross}</td>
                        <td class="slds-text-title_bold">Loan Date:</td>
                        <td>{loanDate}</td>
                    </tr>
                    <tr>
                        <td class="slds-text-title_bold"></td>
                        <td></td>
                        <td class="slds-text-title_bold">Estimated Gross Margin:</td>
                        <td>{estimatedGross} %</td>

                        <td class="slds-text-title_bold">Maturity Date:</td>
                        <td>{matureDate}</td>
                    </tr>
                    <!-- <tr>
                        <td colspan="2"></td>
                        <td class="slds-text-title_bold">Maturity Date:</td>
                        <td>{matureDate}</td>
                    </tr> -->
                </tbody>
            </table>
        </div>

        <div class="slds-p-horizontal_medium slds-p-vertical_small">
            <div class="slds-is-relative">
                <template if:true={isLoading}>
                    <div class="slds-is-absolute slds-align_absolute-center" style="z-index:9999;">
                        <lightning-spinner alternative-text="Loading..." size="medium"></lightning-spinner>
                    </div>
                </template>


                <template if:false={isLoading}>
                    <div class="slds-grid slds-grid_align-left">
                        <div class="slds-col slds-size_1-of-3">
                            <table class="slds-table slds-table_bordered slds-table_cell-buffer slds-no-row-hover">
                                <thead>
                                    <tr class="slds-text-title_caps slds-text-align_center">
                                        <th scope="col">Category</th>
                                        <th scope="col">Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <template for:each={projectCostRows} for:item="row">
                                        <template if:false={row.isSectionHeader}>
                                            <tr key={row.id}>
                                                <th scope="row" class="slds-truncate" title={row.label}>
                                                    {row.label}
                                                </th>
                                                <td class="slds-text-align_right">
                                                    <lightning-formatted-number value={row.calculatedTotal}
                                                        format-style="currency" currency-code="USD">
                                                    </lightning-formatted-number>
                                                </td>
                                            </tr>
                                        </template>
                                    </template>
                                </tbody>
                            </table>
                        </div>
                        <div class="slds-col slds-size_1-of-3">
                            <div class="slds-p-horizontal_medium slds-p-vertical_medium">
                                <table class="slds-table slds-table_cell-buffer slds-table_bordered slds-no-row-hover">
                                    <tbody>
                                        <tr>
                                            <td class="slds-text-title_bold">Borrower's Pre Financing Gross Profit:</td>
                                            <td>{borrowerPreFinancingGrossProfit}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                  
                        
                    
                </template>
            </div>
        </div>

        <div class="slds-p-horizontal_medium slds-p-vertical_medium">

            <div class="slds-p-horizontal_medium slds-p-vertical_medium">
                <!-- SLDS grid to place three tables side by side -->
                <div class="slds-grid slds-grid_pull-padded">

                    <!-- 1) Projected Disbursements -->
                    <div class="slds-col slds-p-horizontal_small slds-size_1-of-3">
                        <div class="slds-text-heading_small slds-text-align_center slds-m-bottom_small">
                            Projected Disbursements
                        </div>
                        <table class="slds-table slds-table_cell-buffer slds-table_bordered slds-no-row-hover">
                            <thead>
                                <tr class="slds-text-title_caps slds-text-align_center">
                                    <th scope="col">Date</th>
                                    <th scope="col">Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                <template for:each={mfDisbursmentWeeks} for:item="cell">
                                    <tr key={cell.id} class="slds-text-align_center">
                                        <!-- Display the date -->
                                        <td>{cell.date}</td>
                                        <!-- Display the amount/value -->
                                        <td>{cell.value}</td>
                                    </tr>
                                </template>
                            </tbody>
                            <tfoot>
                                <tr class="slds-text-title_bold slds-text-align_center">
                                    <td>Grand Total</td>
                                    <td>{mfLoanDisbursementTotal}</td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <!-- 2) Projected Pay Apps – Net Retainage -->
                    <div class="slds-col slds-p-horizontal_small slds-size_1-of-3">
                        <div class="slds-text-heading_small slds-text-align_center slds-m-bottom_small">
                            Projected Pay Apps - Net Retainage
                        </div>
                        <table class="slds-table slds-table_cell-buffer slds-table_bordered slds-no-row-hover">
                            <thead>
                                <tr class="slds-text-title_caps slds-text-align_center">
                                    <th scope="col">Date</th>
                                    <th scope="col">Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                <template for:each={PayAppWeeks} for:item="cell">
                                    <tr key={cell.id} class="slds-text-align_center">
                                        <!-- Display the date -->
                                        <td>{cell.date}</td>
                                        <!-- Display the amount/value -->
                                        <td>{cell.value}</td>
                                    </tr>
                                </template>
                            </tbody>
                            <tfoot>
                                <tr class="slds-text-title_bold slds-text-align_center">
                                    <td>Grand Total</td>
                                    <td>{payAppTotal}</td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <!-- 3) Repayment Schedule -->
                    <div class="slds-col slds-p-horizontal_small slds-size_1-of-3">
                        <div class="slds-text-heading_small slds-text-align_center slds-m-bottom_small">
                            Repayment Schedule
                        </div>
                        <table class="slds-table slds-table_cell-buffer slds-table_bordered slds-no-row-hover">
                            <thead>
                                <tr class="slds-text-title_caps slds-text-align_center">
                                    <th scope="col">Date</th>
                                    <th scope="col">Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                <template for:each={Weeks} for:item="cell">
                                    <tr key={cell.id} class="slds-text-align_center">
                                        <!-- Display the date -->
                                        <td>{cell.date}</td>
                                        <!-- Display the amount/value -->
                                        <td>{cell.value}</td>
                                    </tr>
                                </template>
                            </tbody>
                            <tfoot>
                                <tr class="slds-text-title_bold slds-text-align_center">
                                    <td>Grand Total</td>
                                    <td>$ 4,347,461.75</td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                </div>
            </div>
        </div>
        <div class="slds-grid slds-grid_align-left">
            <div class="slds-col slds-size_1-of-3">
                <div class="slds-p-horizontal_medium slds-p-vertical_medium">
                    <table class="slds-table slds-table_cell-buffer slds-no-row-hover">
                        <tbody>
                            <tr>
                                <td class="slds-text-title_bold">Projected Total Financing Expense:</td>
                                <td>$ {projectedTotalFinancingExpense}</td>

                            </tr>
                            <tr>
                                <td class="slds-text-title_bold">Projected Post Financing Gross Profit:</td>
                                <td>$ {projectedPostFinancingGrossProfit}</td>

                            </tr>
                            <tr>
                                <td class="slds-text-title_bold">Projected Adjusted Gross Margin:</td>
                                <td>{projectedAdjustedGrossMargin} %</td>

                            </tr>
                            <!-- <tr>
                        <td colspan="2"></td>
                        <td class="slds-text-title_bold">Maturity Date:</td>
                        <td>{matureDate}</td>
                    </tr> -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </template>
</template>