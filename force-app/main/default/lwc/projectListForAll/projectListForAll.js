import { LightningElement, api,track, wire } from 'lwc';
import getProjectsForOpportunity from '@salesforce/apex/mf123opp.getProjectsForOpportunity';
import isSandbox from '@salesforce/apex/mf123opp.isSandbox';
import { NavigationMixin } from "lightning/navigation";
import { CurrentPageReference } from 'lightning/navigation';
import { subscribe, MessageContext } from 'lightning/messageService';
import SCROLL_CHANNEL from '@salesforce/messageChannel/ScrollMessageChannel__c';
export default class ProjectListForAll extends NavigationMixin(LightningElement) {
    //@api projects;

    // handleViewAction(event) {
    //     const projectId = event.target.dataset.id;
    //     this.dispatchEvent(new CustomEvent('view', { detail: projectId }));
    // }

    // handleQuickAction(event) {
    //     const projectId = event.target.dataset.id;
    //     this.dispatchEvent(new CustomEvent('quickaction', { detail: projectId }));
    // }
    @api opportunityId;
    @track projects = [];
    @track error;
    @track oppId;
    @track isOppFromDetail = false;
    isSandbox;
    @track isLoading = false;
    @wire(CurrentPageReference)
    getPageReference(currentPageReference) {
        if (currentPageReference) {
            const { state } = currentPageReference;
            const oppIdFromUrl = state.id;
            if (oppIdFromUrl) {
                this.oppId = oppIdFromUrl;
                this.isOppFromDetail = true;
            }
        }
    }

    connectedCallback() {

        isSandbox()
            .then((result) => {
                this.isSandbox = result ? '/mf/s/' : '/s/';
            })
            .catch((error) => {
                console.error('Error checking sandbox status:', JSON.stringify(error));
            });

        if (this.oppId != undefined) {
            this.opportunityId = this.oppId;
        }
        this.fetchProjects();
    }

    fetchProjects() {
        this.isLoading = true;
        getProjectsForOpportunity({ opportunityId: this.opportunityId })
            .then(result => {
                console.log('OUTPUT fetchProjects result : ', JSON.stringify(result));

                this.projects = result.map(item => ({
                    Id: item.project.Id,
                    Name: item.project.Name,
                    Amount: item.project.MF_Loan_Amount__c 
                        ? this.formatCurrencyShort(item.project.MF_Loan_Amount__c) 
                        : '$0.00',
                    StartDate: item.project.Project_Start_Date__c,
                    feedItemCount: item.feedItemCount,
                    
                    MoneyAvailableToDrawOnTheLoan: item.project.Money_Available_to_Draw_on_the_Loan__c 
                        ? this.formatCurrencyShort(item.project.Money_Available_to_Draw_on_the_Loan__c) 
                        : '$0.00'
                }));
            })
            .catch(error => {
                this.error = error;
                console.error('Error fetching projects:', JSON.stringify(error));
            })
            .finally(() => {
            this.isLoading = false; // hide spinner
        });
    }

    formatAmount(amount) {
        if (amount === null || amount === undefined) return '';
        return parseFloat(amount).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }

    formatCurrencyShort(amount) {
        if (amount === null || amount === undefined) return '$0.00';
        const num = parseFloat(amount);
        if (num >= 1.0e9) {
            return `$${(num / 1.0e9).toFixed(2).replace(/\.00$/, '')}B`;
        } else if (num >= 1.0e6) {
            return `$${(num / 1.0e6).toFixed(2).replace(/\.00$/, '')}M`;
        } else if (num >= 1.0e3) {
            return `$${(num / 1.0e3).toFixed(2).replace(/\.00$/, '')}k`;
        } else {
            return `$${num.toFixed(2)}`;
        }
    }

    handleViewAction(event) {
        const projectId = event.target.dataset.id;
        this[NavigationMixin.Navigate]({
            type: 'standard__recordPage',
            attributes: {
                recordId: projectId,
                objectApiName: 'Project__c',
                actionName: 'view'
            }
        });
        // const projectId = event.target.dataset.id;
        // this[NavigationMixin.Navigate]({
        //     type: 'comm__namedPage',
        //     attributes: {
        //         name: 'Project_Detail_Custom_Page__c'
        //     },
        //     state: {
        //         id: projectId
        //     }
        // });
    }

    handleQuickAction(event) {
        const projectId = event.target.dataset.id;
        this[NavigationMixin.Navigate]({
            type: 'standard__webPage',
            attributes: {
                url: `${this.isSandbox}disbursement-request?recordId=${projectId}`
            }
        });
    }

    handleViewTransaction(event){
        const projectId = event.target.dataset.id;
        this[NavigationMixin.Navigate]({
            type: 'standard__webPage',
            attributes: {
                url: `${this.isSandbox}transaction-list?recordId=${projectId}`
            }
        });
    }

    get boxShadowClass() {
        return this.isOppFromDetail ? 'shadow-box-detail' : 'shadow-box-default';
    }

    @wire(MessageContext) messageContext;
    subscription;

    renderedCallback() {
        if (this.subscription || !this.messageContext) {
            return;
        }

        this.subscription = subscribe(this.messageContext, SCROLL_CHANNEL, (message) => {
            if (message.targetSection === 'projectsSection') {
                this.scrollToSection();
            }
        });
    }

    scrollToSection() {
        const section = this.template.querySelector('[data-id="projectsSection"]');
        console.log(section);
        if (section) {
            section.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    }

}