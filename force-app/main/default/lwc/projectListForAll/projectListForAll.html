<template>
    <template if:true={isLoading}>
    <!-- <lightning-spinner alternative-text="Loading" size="medium"></lightning-spinner> -->
    <div class="skeleton-wrapper">
            <svg class="skeleton-container" viewBox="0 0 800 150" preserveAspectRatio="xMinYMin meet"
                xmlns="http://www.w3.org/2000/svg">
                <!-- Placeholder for "Your Pending Loans" title -->
                <rect x="20" y="20" rx="4" ry="4" width="200" height="20" class="pulse" />

                <!-- First row of pending loan data -->
                <rect x="20" y="60" rx="3" ry="3" width="100" height="15" class="pulse" />
                <rect x="130" y="60" rx="3" ry="3" width="90" height="15" class="pulse" />
                <rect x="230" y="60" rx="3" ry="3" width="90" height="15" class="pulse" />
                <rect x="330" y="60" rx="3" ry="3" width="300" height="15" class="pulse" />
                <rect x="660" y="60" rx="5" ry="5" width="80" height="15" class="pulse" />

                <!-- Second row of pending loan data -->
                <rect x="20" y="100" rx="3" ry="3" width="100" height="15" class="pulse" />
                <rect x="130" y="100" rx="3" ry="3" width="90" height="15" class="pulse" />
                <rect x="230" y="100" rx="3" ry="3" width="90" height="15" class="pulse" />
                <rect x="330" y="100" rx="3" ry="3" width="300" height="15" class="pulse" />
                <rect x="660" y="100" rx="5" ry="5" width="80" height="15" class="pulse" />

            </svg>
        </div>
</template>

    <template if:true={projects}>
        <div data-id="projectsSection">
        <template if:true={isOppFromDetail} >
            <h3 class="title heading-title" >Projects</h3>
        </template>
        <div class="loan-card-project shadow-box-detail">
            <template if:false={isOppFromDetail} >
                <h3 class="title heading-title" >Projects</h3>
            </template>
         <table class="slds-table slds-table_cell-buffer">
            <colgroup>
    <col style="width:20%" />  <!-- PROJECT -->
    <col style="width:12%" />  <!-- START DATE -->
    <col style="width:13%" />  <!-- MESSAGES -->
    <col style="width:55%" />  <!-- ACTIONS -->
  </colgroup>
                <thead>
                    <tr class="slds-line-height_reset">
                        <th scope="col">
                            <div class="slds-truncate heading-detail loan-info-title" title="Project">PROJECT</div>
                        </th>
                        <th scope="col">
                            <div class="slds-truncate heading-detail loan-info-title" title="Start Date">START DATE</div>
                        </th>
                        <!-- <th scope="col">
                            <div class="slds-truncate heading-detail loan-info-title" title="Amount">AMOUNT</div>
                        </th>
                        <th scope="col">
                            <div class="slds-truncate heading-detail loan-info-title" title="Amount">AVAILABLE</div>
                        </th> -->
                        <th scope="col">
                            <div class="slds-truncate heading-detail loan-info-title" title="Messages">MESSAGES</div>
                        </th>
                        <th scope="col"></th> 
                    </tr>
                </thead>
                <tbody>
                    <template for:each={projects} for:item="project">
                        <tr key={project.id} class="slds-hint-parent">
                            <td>
                                <div class="slds-truncate heading-value" title={project.name}>{project.Name}</div>
                            </td>
                            <td>
                                <div class="slds-truncate heading-value" title={project.startDate}>{project.StartDate}</div>
                            </td>
                            <!-- <td>
                                <template lwc:if={project.Amount}>
                                    <div class="slds-truncate heading-value" title={project.amount}>{project.Amount}</div>
                                </template>
                            </td> 
                            <td>
                                <template lwc:if={project.MoneyAvailableToDrawOnTheLoan}>
                                    <div class="slds-truncate heading-value" title={project.MoneyAvailableToDrawOnTheLoan}>{project.MoneyAvailableToDrawOnTheLoan}</div>
                                </template>
                            </td>  -->
                            <td>
                                <div class="slds-truncate heading-value" title={project.messages}>{project.feedItemCount}</div>
                            </td>
                            <td class="slds-text-align_right heading-value">
                                <!-- <lightning-button label="VIEW PROJECT" onclick={handleViewAction} data-id={project.Id} style="--slds-c-button-radius-border: 0px;" class="green-border"></lightning-button>
                                <lightning-button class="slds-m-left_medium green-border" label="SUBMIT DISBURSEMENT" valabel="Submit Disbursement" onclick={handleQuickAction} data-id={project.Id} style="--slds-c-button-radius-border: 0px;" ></lightning-button> -->
                                <button class="slds-button slds-button_neutral custom-green-border" onclick={handleViewAction} data-id={project.Id}>VIEW PROJECT </button>
                                <button class="slds-button slds-button_neutral slds-m-left_medium custom-green-border" onclick={handleViewTransaction} data-id={project.Id}> VIEW TRANSACTIONS</button>
                                <button class="slds-button slds-button_neutral slds-m-left_medium custom-green-border" onclick={handleQuickAction} data-id={project.Id}> SUBMIT DISBURSEMENT</button>

                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>
        </div>
    </template>
    
</template>