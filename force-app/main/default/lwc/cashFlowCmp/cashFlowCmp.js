import { LightningElement, wire, track } from 'lwc';
import { CurrentPageReference, NavigationMixin } from 'lightning/navigation';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import communityId from "@salesforce/community/Id";
import { loadScript } from 'lightning/platformResourceLoader';
import iframeResizerLib from '@salesforce/resourceUrl/IframeResizer'; 
import getCashFlowBaseUrl from '@salesforce/apex/CashFlowConfigController.getCashFlowBaseUrl';


export default class CashFlowCmp extends NavigationMixin(LightningElement) {
    @track iframeUrl; 
    iFrameLibInitialized = false;

    @track acctId;
    @track opptyId;
    @track usrId;

    @track cashFlowBaseUrlFromCMDT;
    baseUrlError = false; // To track if base URL loading failed


    @wire(CurrentPageReference)
    getPageReference(currentPageReference) {
        if (currentPageReference) {
            console.log('currentPageReference --> ' + JSON.stringify(currentPageReference));
            const { state } = currentPageReference;
            this.acctId = state.acctId;
            console.log('accountId --> ' + this.acctId);
            this.usrId = state.usrId;
            console.log('userId --> ' + this.usrId);

            // Attempt to set iframe URL if base URL is already loaded
            if (this.cashFlowBaseUrlFromCMDT) {
                this.setIframeUrl();
            }
        }
    }

    // Add this @wire decorator for the new Apex method
    @wire(getCashFlowBaseUrl)
    wiredBaseUrl({ error, data }) {
        if (data) {
            console.log('cahsflow data --> ' + JSON.stringify(data));
            this.cashFlowBaseUrlFromCMDT = data; // Apex ensures it ends with '/'
            console.log('Fetched CashFlow Base URL --> ' + this.cashFlowBaseUrlFromCMDT);
            // Attempt to set iframe URL now that base URL is loaded
            if (this.acctId !== undefined && this.usrId !== undefined) { // Check if params are also ready
                this.setIframeUrl();
            }
        } else if (error) {
            console.error('Error loading CashFlow base URL:', JSON.stringify(error));
            this.showToast('Configuration Error', 'Could not load Cash Flow URL. Redirecting.', 'error');
            this.baseUrlError = true;
            this.redirectToHome();
        }
    }

    
    setIframeUrl() {
        // Guard: Do not proceed if base URL failed to load or isn't loaded yet
        if (this.baseUrlError || !this.cashFlowBaseUrlFromCMDT) {
            if (!this.baseUrlError) console.log('Waiting for base URL to construct iframe src...');
            return;
        }

        if ((this.acctId && this.usrId)) { // communityId check was removed as per your original connectedCallback logic for iframeUrl
            this.iframeUrl = `${this.cashFlowBaseUrlFromCMDT}?hide_layout=true&sf_user_id=${this.usrId}&sf_account_id=${this.acctId}`;
            console.log('Updated iframeUrl --> ' + this.iframeUrl);
        } else {
            //this.showToast('Error', 'Invalid Cash Flow iframe URL parameters. Redirecting to Home.', 'error');
            console.error('Missing acctId or usrId for iframe URL construction');
            this.redirectToHome();
        }
    }


    connectedCallback() {
        console.log('communityId --> ' + communityId);
        // The logic to set iframeUrl will now primarily happen in setIframeUrl()
        // after both page params and base URL are available.

        // The event listener setup remains, but origin check will be dynamic
        window.addEventListener('message', this.handleWindowMessage.bind(this), false);
    }

    // Ensure you have a way to remove the listener
    disconnectedCallback() {
        window.removeEventListener('message', this.handleWindowMessage.bind(this), false);
    }

    // New handler to make origin dynamic
    handleWindowMessage(event) {
        let expectedOrigin = "https://mobilizationfunding.com"; // Fallback
        if (this.cashFlowBaseUrlFromCMDT) {
            try {
                const urlObject = new URL(this.cashFlowBaseUrlFromCMDT);
                expectedOrigin = urlObject.origin;
            } catch (e) {
                console.error("Error parsing cashFlowBaseUrlFromCMDT for origin check:", e);
            }
        }

        console.log('event --> ' + JSON.stringify(event.data)); // Log event.data for easier debugging
        console.log(`Event origin: ${event.origin}, Expected origin: ${expectedOrigin}`);

        if (event.origin !== expectedOrigin) {
            return;
        }
        if (event.data?.type === 'MfWeb-CashFlow-Complete') {
            console.log('Cash Flow process completed. Redirecting to home page...');
            this.redirectToHome();
        }
    }

    renderedCallback() {
        if (this.iFrameLibInitialized || !this.iframeUrl || !this.cashFlowBaseUrlFromCMDT) { // Also wait for base URL
            return;
        }
        this.iFrameLibInitialized = true;

        loadScript(this, iframeResizerLib)
            .then(() => {
                const iframeEl = this.template.querySelector('iframe');
                if (iframeEl && window.iFrameResize) {
                    // --- MANDATORY UPDATE for iframeResizer checkOrigin ---
                    let dynamicCheckOrigin = ['https://mobilizationfunding.com']; // Fallback
                    try {
                        const urlObject = new URL(this.cashFlowBaseUrlFromCMDT);
                        dynamicCheckOrigin = [urlObject.origin];
                    } catch(e) {
                        console.error("Error parsing cashFlowBaseUrlFromCMDT for iframeResizer origin:", e);
                    }

                    window.iFrameResize({
                        log: true,
                        checkOrigin: dynamicCheckOrigin, // Use dynamic origin
                        heightCalculationMethod: 'max',
                        autoResize: true,
                        sizeWidth: true
                    }, iframeEl);
                    console.log('iframe-resizer initialized with origin:', dynamicCheckOrigin);
                }
            })
            .catch(error => {
                console.error('Failed to load iframe resizer:', error);
            });
    }

    redirectToHome() {
        this[NavigationMixin.Navigate]({
            type: 'standard__namedPage',
            attributes: {
                pageName: 'home'
            }
        });
    }

    showToast(title, message, variant) {
        this.dispatchEvent(new ShowToastEvent({ title, message, variant }));
    }
}