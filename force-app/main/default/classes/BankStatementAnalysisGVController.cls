@SuppressWarnings('PMD')
public with sharing class BankStatementAnalysisGVController {

    @AuraEnabled(cacheable=true)
    public static StatementData getStatementData(String recordId, String lastNMonths, String category) {
        try {
            if (String.isBlank(recordId)) {
                throw new AuraHandledException('recordId is required.');
            }
            if (String.isBlank(lastNMonths)) {
                throw new AuraHandledException('lastNMonths is required.');
            }
            if (String.isBlank(category)) {
                throw new AuraHandledException('category is required.');
            }

            String mfCategoryValue;
            StatementData result = new StatementData();
            List<TransactionData> transactionsData = new List<TransactionData>();

            Map<String, MF_Category__mdt> mfCategories = MF_Category__mdt.getAll();
            Map<String, Flinks_Category__mdt> flinksCategories = Flinks_Category__mdt.getAll();

            List<AnalysisCategory> creditAnalysisCategories = new List<AnalysisCategory>();
            List<AnalysisCategory> debitAnalysisCategories = new List<AnalysisCategory>();
            Map<Id, MF_Category__mdt> mfCategoryMap = new Map<Id, MF_Category__mdt>();

            for (MF_Category__mdt mfCategory : mfCategories.values()) {
                if (mfCategory.Active__c) {
                    mfCategoryMap.put(mfCategory.Id, mfCategory);
                    AnalysisCategory data = new AnalysisCategory();
                    data.category = mfCategory.Category__c;
                    data.viewOrder = Integer.valueOf(mfCategory.View_Order__c);
                    if (mfCategory.Analysis_Type__c == 'Credit') {
                        creditAnalysisCategories.add(data);
                    } else {
                        debitAnalysisCategories.add(data);
                    }
                }
            }

            List<Bank_Transaction__c> recentTransactions;
            if(Test.isRunningTest()) {
                recentTransactions= [ SELECT Transaction_Date__c 
                    FROM Bank_Transaction__c 
                    WHERE Bank_Account__c = :recordId 
                    Order By Order_Number__c,Transaction_Date__c DESC LIMIT 1
                ];
            } else {
                recentTransactions = [
                    SELECT Transaction_Date__c 
                    FROM Bank_Transaction__c 
                    WHERE Bank_Account__c = :recordId 
                    ORDER By Transaction_Date__c desc, Order_Number__c asc LIMIT 1
                ];
            }

            Bank_Transaction__c recentTransaction;
            Date lastTransactionDate;
            Date sevenMonthsBefore;
            Date lastMonthEnd;
            Date sixMonthsAgoStart;

            if (!recentTransactions.isEmpty()) {
                recentTransaction = recentTransactions[0];
                lastTransactionDate = recentTransaction.Transaction_Date__c;
                System.debug(lastTransactionDate);
                Date todayCheck = Date.today();
                Integer todayCheckMonth = todayCheck.month();
                Integer todayMonth = lastTransactionDate.month();
                if (todayMonth == todayCheckMonth) {
                    sevenMonthsBefore = lastTransactionDate.addMonths(-6);
                    lastMonthEnd = Date.newInstance(lastTransactionDate.year(), lastTransactionDate.month(), 1).addDays(-1);
                    sixMonthsAgoStart = Date.newInstance(lastMonthEnd.year(), lastMonthEnd.month(), 1).addMonths(-5);
                } else {
                    sevenMonthsBefore = lastTransactionDate.addMonths(-5);
                    Integer year  = lastTransactionDate.year();
                    Integer month = lastTransactionDate.month();
                    Integer lastDayOfMonth = Date.daysInMonth(year, month);
                    
                     lastMonthEnd = Date.newInstance(year, month, lastDayOfMonth);
                    sixMonthsAgoStart = Date.newInstance(lastMonthEnd.year(), lastMonthEnd.month(), 1).addMonths(-5);
                }
            } else {
               Date today = Date.today();
                sevenMonthsBefore = today.addMonths(-6);
                lastMonthEnd = Date.newInstance(today.year(), today.month(), 1).addDays(-1);
                sixMonthsAgoStart = Date.newInstance(lastMonthEnd.year(), lastMonthEnd.month(), 1).addMonths(-5);
            }

            Integer monthNumber = sevenMonthsBefore.month();
            Integer monthYear = sevenMonthsBefore.year();
            Integer monthsToQuery = Integer.valueOf(lastNMonths.trim());
            Date nMonthsAgoStart = Date.newInstance(lastMonthEnd.year(), lastMonthEnd.month(), 1).addMonths(-(monthsToQuery - 1));

            String queryString = ' SELECT Id, Name, Bank_Account__c, MF_Category__c, MF_Category_Manual_Override__c, AI_Category__c, Balance__c, Debit__c, Credit__c, Description__c, Category__c, Sub_Category__c, Transaction_Date__c, Order_Number__c FROM Bank_Transaction__c ';
            queryString += ' WHERE Bank_Account__c = :recordId ';
            queryString += ' AND Transaction_Date__c >= :sixMonthsAgoStart ';
            queryString += ' AND Transaction_Date__c <= :lastMonthEnd ORDER BY Order_Number__c DESC';
          

            for (Bank_Transaction__c transactionRecord : Database.query(queryString, AccessLevel.USER_MODE)) {
                TransactionData data = new TransactionData();
                data.transactionId = transactionRecord.Id;
                data.name = transactionRecord.Name;
                data.bankAccount = transactionRecord.Bank_Account__c;
                data.balance = transactionRecord?.Balance__c?.setScale(2);
                data.debit = transactionRecord?.Debit__c?.setScale(2);
                data.credit = transactionRecord?.Credit__c?.setScale(2);
                data.description = transactionRecord.Description__c;
                data.category = transactionRecord.Category__c;
                data.subCategory = transactionRecord.Sub_Category__c;
                data.transactionDate = transactionRecord.Transaction_Date__c;
                data.orderNumber = transactionRecord.Order_Number__c;

                if (category == 'MF_Category__c') {
                    mfCategoryValue = transactionRecord.MF_Category__c;
                } else if (category == 'AI_Category__c') {
                    mfCategoryValue = transactionRecord.AI_Category__c;
                } else if (category == 'MF_Category_Manual_Override__c') {
                    mfCategoryValue = transactionRecord.MF_Category_Manual_Override__c != null
                        ? transactionRecord.MF_Category_Manual_Override__c
                        : transactionRecord.AI_Category__c;
                }

                if (transactionRecord.Debit__c != null) {
                    data.mfCategory = mfCategoryValue;
                    data.analysisType = 'Debit';
                } else if (transactionRecord.Credit__c != null) {
                    data.mfCategory = mfCategoryValue;
                    data.analysisType = 'Credit';
                }

                transactionsData.add(data);
            }

            System.debug('transactionsData.size() -> ' + transactionsData.size());
            System.debug('transactionsData 0 -> ' + transactionsData[0]);
            System.debug('transactionsData[transactionsData.size() - 1] -> ' + transactionsData[transactionsData.size() - 1]);
            System.debug('transactionsData[transactionsData.size() - 1].transactionDate  -> ' + transactionsData[transactionsData.size() - 1].transactionDate );

            if (!transactionsData.isEmpty()) {
                // Always use fallback approach as the initial transaction
                List<Bank_Transaction__c> fallback = [
                    SELECT Id, Name, Bank_Account__c, Balance__c, Debit__c, Credit__c,
                        Description__c, Category__c, Sub_Category__c, Transaction_Date__c,
                        MF_Category__c, AI_Category__c, MF_Category_Manual_Override__c, Order_Number__c
                    FROM Bank_Transaction__c
                    WHERE Bank_Account__c = :recordId 
                    AND Transaction_Date__c < :sixMonthsAgoStart
                    Order by Order_Number__c DESC
                    LIMIT 1
                ];
                System.debug('fallback: ' + JSON.serialize(fallback));
                System.debug('transa date fallback '+transactionsData[0].transactionDate);
                if (fallback != null && !fallback.isEmpty()) {
                    Bank_Transaction__c txn = fallback[0];
                    TransactionData fallbackTransactionData = new TransactionData();
                    fallbackTransactionData.transactionId = txn.Id;
                    fallbackTransactionData.name = txn.Name;
                    fallbackTransactionData.bankAccount = txn.Bank_Account__c;
                    fallbackTransactionData.balance = txn.Balance__c != null ? txn.Balance__c.setScale(2) : null;
                    fallbackTransactionData.debit = txn.Debit__c != null ? txn.Debit__c.setScale(2) : null;
                    fallbackTransactionData.credit = txn.Credit__c != null ? txn.Credit__c.setScale(2) : null;
                    fallbackTransactionData.description = txn.Description__c;
                    fallbackTransactionData.category = txn.Category__c;
                    fallbackTransactionData.subCategory = txn.Sub_Category__c;
                    fallbackTransactionData.transactionDate = txn.Transaction_Date__c;
                    fallbackTransactionData.orderNumber = txn.Order_Number__c;
            
                    if (category == 'MF_Category__c') {
                        fallbackTransactionData.mfCategory = txn.MF_Category__c;
                    } else if (category == 'AI_Category__c') {
                        fallbackTransactionData.mfCategory = txn.AI_Category__c;
                    } else if (category == 'MF_Category_Manual_Override__c') {
                        fallbackTransactionData.mfCategory = txn.MF_Category_Manual_Override__c != null
                            ? txn.MF_Category_Manual_Override__c
                            : txn.AI_Category__c;
                    }
            
                    if (txn.Debit__c != null && txn.Debit__c != 0) {
                        fallbackTransactionData.analysisType = 'Debit';
                    } else if (txn.Credit__c != null && txn.Credit__c != 0) {
                        fallbackTransactionData.analysisType = 'Credit';
                    } else {
                        fallbackTransactionData.analysisType = 'Info';
                    }
            
                    result.initialTransaction = fallbackTransactionData;
                }
            
                result.transactions = transactionsData;
                result.months = returnPastNMonths(6, monthNumber, monthYear, false);
                result.creditAnalysisCategories = creditAnalysisCategories;
                result.debitAnalysisCategories = debitAnalysisCategories;
            }


            return result;
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage() + '-' + e.getStackTraceString());
        }
    }

    private static List<String> returnPastNMonths(Integer n, Integer startMonth, Integer startYear, Boolean addFullYear) {
        List<String> months = new List<String>{'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'July', 'Aug', 'Sept', 'Oct', 'Nov', 'Dec'};
        List<String> result = new List<String>();
        Date startDate = Date.newInstance(startYear, startMonth, 1); 

        for (Integer i = 0; i < n; i++) {
            Date tempDate = startDate.addMonths(i);
            Integer month = tempDate.month();
            Integer year = tempDate.year();
            if (addFullYear) {
                result.add(months[month - 1] + ' ' + year);
            } else {
                result.add(months[month - 1]);
            }
        }

        List<String> reversedResult = new List<String>();
        for (Integer i = result.size() - 1; i >= 0; i--) {
            reversedResult.add(result[i]);
        }

        return reversedResult;
    }

    public class StatementData {
        @AuraEnabled public List<String> months;
        @AuraEnabled public List<AnalysisCategory> creditAnalysisCategories;
        @AuraEnabled public List<AnalysisCategory> debitAnalysisCategories;
        @AuraEnabled public List<TransactionData> transactions;
        @AuraEnabled public TransactionData initialTransaction;
    }

    public class AnalysisCategory {
        @AuraEnabled public String category;
        @AuraEnabled public Integer viewOrder;
    }

    public class TransactionData {
        @AuraEnabled public String transactionId;
        @AuraEnabled public String name;
        @AuraEnabled public String bankAccount;
        @AuraEnabled public Decimal balance;
        @AuraEnabled public Decimal debit;
        @AuraEnabled public Decimal credit;
        @AuraEnabled public String description;
        @AuraEnabled public String category;
        @AuraEnabled public String subCategory;
        @AuraEnabled public Date transactionDate;
        @AuraEnabled public String analysisType;
        @AuraEnabled public String mfCategory;
        @AuraEnabled public Decimal orderNumber;
    }
}