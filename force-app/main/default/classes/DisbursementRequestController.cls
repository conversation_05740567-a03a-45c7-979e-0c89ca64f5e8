@SuppressWarnings('PMD')
public with sharing class DisbursementRequestController {
    /** 
     * 1. Fetch an existing Project__c record by Id 
     */
    @AuraEnabled(cacheable=true)
    public static Project__c getExistingProjectRecord(Id projectId) {
        if (projectId == null) {
            throw new AuraHandledException('Project Id cannot be null');
        }
        return [
            SELECT Id, Name
            FROM Project__c
            WHERE Id = :projectId
            LIMIT 1
        ];
    }

    /**
     * Wrapper for saveRequestedItemsAndLinkFiles response
     */
    public class SaveResult {
        @AuraEnabled public List<Id> noteIds;
        @AuraEnabled public List<Id> createdItems;
    }

    /**
     * 2. Save the requested items & link files
     */
    @AuraEnabled
    public static SaveResult saveRequestedItemsAndLinkFiles(
        Id projectId,
        String expenseType,
        String paymentMethod,
        String payeeAddress,
        String payeeCity,
        String payeeState,
        String payeeZip,
        String bankRouting,
        String bankAccount,
        String signatureName,
        String signatureBase64,
        String fileName,
        String fileBody
    ) {
        // TODO: implement record creation, ContentVersion upload, etc.
        SaveResult result = new SaveResult();
        result.noteIds      = new List<Id>();
        result.createdItems = new List<Id>();
        return result;
    }

    /**
     * Wrapper for generateContentDownloadUrlForSignature response
     */
    public class UrlResult {
        @AuraEnabled public Id noteId;
        @AuraEnabled public List<Id> requestedItems;
    }

    /**
     * 3. Generate download URL for the signature file
     */
    @AuraEnabled
    public static UrlResult generateContentDownloadUrlForSignature(
        List<Id> noteIds,
        List<Id> requestedItemIds
    ) {
        UrlResult r = new UrlResult();
        r.noteId         = (noteIds != null && !noteIds.isEmpty()) ? noteIds[0] : null;
        r.requestedItems = requestedItemIds != null ? requestedItemIds : new List<Id>();
        return r;
    }

    /**
     * Wrapper for saveNoteAndLinkFile response
     */
    public class LinkResult {
        @AuraEnabled public Id noteId;
        @AuraEnabled public List<Id> requestedItems;
    }

    /**
     * 4. Save the note and link it to the request items
     */
    @AuraEnabled
    public static LinkResult saveNoteAndLinkFile(
        Id noteId,
        List<Id> requestedItemIds
    ) {
        LinkResult r = new LinkResult();
        r.noteId         = noteId;
        r.requestedItems = requestedItemIds != null ? requestedItemIds : new List<Id>();
        return r;
    }

    /**
     * 5. Send an email about the disbursement request
     */
    @AuraEnabled
    public static void sendEmailOfDisbursementReqForm(
        Id noteId,
        List<Id> requestedItemIds
    ) {
        // TODO: implement Messaging.sendEmail logic
    }
}
