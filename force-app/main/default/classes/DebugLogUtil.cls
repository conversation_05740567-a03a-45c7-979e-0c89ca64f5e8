/**
 * @description This utility class provides a centralized and consistent approach to logging
 * across the application. It leverages Nebula Logger for robust logging capabilities
 * and includes conditional System.debug output, which can be enabled/disabled via a
 * Custom Permission ('Enable_Verbose_Debug_Logging'). The utility supports various log levels
 * (INFO, WARN, ERROR), method entry/exit logging, and the ability to add custom tags
 * to log entries for better filtering and analysis.
 * <AUTHOR>
 * @date 12 May 2025
*/
@SuppressWarnings('PMD')
public with sharing class DebugLogUtil {

    private static final String VERBOSE_DEBUG_PERMISSION = 'Enable_Verbose_Debug_Logging';
    private static Boolean canSystemDebug = null;

    /**
     * @description Checks if the current user has the verbose logging permission
     *              which enables additional System.debug output.
     *              Caches result per transaction.
     * @return Boolean True if System.debug should be used, false otherwise.
     */
    private static Boolean shouldSystemDebug() {
        // Caching the check result within a single transaction for performance
        if (canSystemDebug == null) {
            canSystemDebug = FeatureManagement.checkPermission(VERBOSE_DEBUG_PERMISSION);
            Nebula.Logger.info('System.debug Verbose Logging Check for User ' + UserInfo.getUserId() + ': ' + canSystemDebug);
        }
        return canSystemDebug;
    }

    // --- INFO Level ---

    public static void info(String message) {
        info(message, null, null);
    }

    public static void info(String message, List<String> tags) {
        info(message, null, tags);
    }

    public static void info(String message, List<Object> args) {
         info(message, args, null);
    }
    
    public static void log(String message) {
        info(message, null, null);
    }

    public static void log(String message, List<String> tags) {
        info(message, null, tags);
    }

    public static void log(String message, List<Object> args) {
         info(message, args, null);
    }

    /**
    * @description Logs a message at INFO level using Nebula Logger.
    *              Optionally adds tags and System.debug output if permission allows.
    * @param message The message string to log. Supports String.format placeholders.
    * @param args Optional list of arguments for String.format.
    * @param tags Optional list of string tags to add to the Nebula log entry.
    */
    public static void info(String message, List<Object> args, List<String> tags) {
        String formattedMessage = (args != null && !args.isEmpty()) ? String.format(message, args) : message;

        // Always log to Nebula
        // *** Assumes Nebula.Logger static methods accept a List<String> for tags. ***
        // *** Adapt if Nebula API requires instance methods or different tag handling. ***
        Nebula.Logger.info(formattedMessage).addTags(tags);

        // Conditionally log to System.debug
        if (shouldSystemDebug()) {
            String tagString = (tags != null && !tags.isEmpty()) ? ' Tags: ' + String.join(tags, ',') : '';
            System.debug(LoggingLevel.INFO + ' | DEBUG LOG | ' + formattedMessage + tagString);
        }
    }


    // --- WARN Level ---

    public static void warn(String message) {
        warn(message, null, null);
    }

     public static void warn(String message, List<String> tags) {
        warn(message, null, tags);
    }

    public static void warn(String message, List<Object> args) {
         warn(message, args, null);
    }

    /**
    * @description Logs a message at WARN level using Nebula Logger.
    *              Optionally adds tags and System.debug output if permission allows.
    * @param message The message string to log. Supports String.format placeholders.
    * @param args Optional list of arguments for String.format.
    * @param tags Optional list of string tags to add to the Nebula log entry.
    */
    public static void warn(String message, List<Object> args, List<String> tags) {
        String formattedMessage = (args != null && !args.isEmpty()) ? String.format(message, args) : message;

        // Always log to Nebula
        Nebula.Logger.warn(formattedMessage).addTags(tags);

        // Conditionally log to System.debug
        if (shouldSystemDebug()) {
            String tagString = (tags != null && !tags.isEmpty()) ? ' Tags: ' + String.join(tags, ',') : '';
            System.debug(LoggingLevel.WARN + ' | DEBUG LOG | ' + formattedMessage + tagString);
        }
    }

    // --- ERROR Level ---

    public static void error(String message) {
        error(message, null, null, null);
    }

    public static void error(String message, List<String> tags) {
        error(message, null, null, tags);
    }

    public static void error(Exception ex) {
        error(ex?.getMessage(), ex, null, null); // Use exception message if available
    }
    
    public static void error(Exception ex, List<String> tags) {
        error(ex?.getMessage(), ex, null, tags); // Use exception message if available
    }

    public static void error(String message, Exception ex) {
        error(message, ex, null, null);
    }
    
    public static void error(String message, Exception ex, List<String> tags) {
        error(message, ex, null, tags);
    }

    /**
    * @description Logs a message and/or an Exception at ERROR level using Nebula Logger.
    *              Optionally adds tags and System.debug output if permission allows.
    * @param message The message string to log.
    * @param ex Optional Exception object to log.
    * @param args Optional list of arguments for String.format (applied to message).
    * @param tags Optional list of string tags to add to the Nebula log entry.
    */
    public static void error(String message, Exception ex, List<Object> args, List<String> tags) {
        String formattedMessage = message;
        if (formattedMessage == null && ex != null) {
             formattedMessage = ex.getMessage(); // Default message from exception if primary is null
        }
        if (args != null && !args.isEmpty() && formattedMessage != null) {
            formattedMessage = String.format(formattedMessage, args);
        }
        
        // Always log to Nebula
        if (ex != null) {
             // *** Assumes Nebula.Logger.error handles Exception object correctly ***
            Nebula.Logger.error(formattedMessage, ex).addTags(tags);
        } else {
            Nebula.Logger.error(formattedMessage).addTags(tags);
        }

        // Conditionally log to System.debug
        if (shouldSystemDebug()) {
            String tagString = (tags != null && !tags.isEmpty()) ? ' Tags: ' + String.join(tags, ',') : '';
            String exceptionInfo = '';
            if (ex != null) {
                exceptionInfo = ' | Exception: ' + ex.getTypeName() + ': ' + ex.getMessage() + '\nStackTrace: ' + ex.getStackTraceString();
            }
            System.debug(LoggingLevel.ERROR + ' | DEBUG LOG | ' + formattedMessage + tagString + exceptionInfo);
        }
    }

    // --- Method Entry/Exit ---

    /**
    * @description Logs the start of a method using Nebula Logger at INFO level.
    *              Adds System.debug output if permission allows.
    * @param methodName The name of the method being entered.
    * @param params Optional map of parameter names to values.
    * @param tags Optional list of string tags.
    */
    public static void entry(String methodName, Map<String, Object> params, List<String> tags) {
        String msg = 'Entering Method: ' + methodName;
        if (params != null && !params.isEmpty()) {
            msg += ' | Params: ' + JSON.serialize(params, true); // Use pretty print for readability
        }
        
        // Always log to Nebula
        Nebula.Logger.info(msg).addTags(tags);

        // Conditionally log to System.debug
        if (shouldSystemDebug()) {
             String tagString = (tags != null && !tags.isEmpty()) ? ' Tags: ' + String.join(tags, ',') : '';
            System.debug(LoggingLevel.DEBUG + ' | METHOD ENTRY | ' + msg + tagString);
        }
    }
     // Overload without tags
    public static void entry(String methodName, Map<String, Object> params) {
        entry(methodName, params, null);
    }
     // Overload with only method name
    public static void entry(String methodName) {
        entry(methodName, null, null);
    }


    /**
    * @description Logs the exit of a method using Nebula Logger at INFO level.
    *              Adds System.debug output if permission allows.
    * @param methodName The name of the method being exited.
    * @param tags Optional list of string tags.
    */
    public static void exit(String methodName, List<String> tags) {
         String msg = 'Exiting Method: ' + methodName;
         
         // Always log to Nebula
         Nebula.Logger.info(msg).addTags(tags);

         // Conditionally log to System.debug
         if (shouldSystemDebug()) {
             String tagString = (tags != null && !tags.isEmpty()) ? ' Tags: ' + String.join(tags, ',') : '';
            System.debug(LoggingLevel.DEBUG + ' | METHOD EXIT | ' + msg + tagString);
        }
    }
    // Overload without tags
    public static void exit(String methodName) {
        exit(methodName, null);
    }

    // Save logs
    public static void saveLogs() {
        Nebula.Logger.saveLog();
    }

    // --- Transaction Control & Saving ---
    
    /**
    * @description Discards any unsaved log entries from the buffer for the current transaction.
    */
    public static void flushBuffer() {
        Nebula.Logger.flushBuffer();
    }

    /**
    * @description Suspends log saving for the current transaction. Any calls to saveLog() will be ignored
    * until resumeSaving() is called.
    */
    public static void suspendSaving() {
        Nebula.Logger.suspendSaving();
    }

    /**
    * @description Resumes log saving for the current transaction after it has been suspended.
    */
    public static void resumeSaving() {
        Nebula.Logger.resumeSaving();
    }
    
    /**
    * @description Sets the default save method for all subsequent saveLog() calls in the current transaction.
    * @param saveMethod The Nebula.Logger.SaveMethod enum to use as the default (e.g., EVENT_BUS, QUEUEABLE).
    */
    public static void setSaveMethod(Nebula.Logger.SaveMethod saveMethod) {
        Nebula.Logger.setSaveMethod(saveMethod);
    }
    
    /**
    * @description Saves all buffered log entries using the transaction's default save method.
    */
    public static void saveLog() {
        Nebula.Logger.saveLog();
    }
    
    /**
    * @description Saves all buffered log entries using a specified save method for this one call only.
    * @param saveMethod The Nebula.Logger.SaveMethod enum to use for this specific save operation.
    */
    public static void saveLog(Nebula.Logger.SaveMethod saveMethod) {
        Nebula.Logger.saveLog(saveMethod);
    }
}