@SuppressWarnings('PMD')
global without sharing class FlinksController {

    private static final String CLASS_NAME = 'FlinksController';
    private static final List<String> LOG_TAGS = new List<String>{CLASS_NAME};

    @future(callout=true)
    global static void getAccountDetailsFuture(String reqId) {
        getAccountDetails(reqId);
    }

    @AuraEnabled
    global static void getAccountDetails(String reqId) {
        Map<String, Object> params = new Map<String, Object>{'reqId' => reqId};
        DebugLogUtil.entry(CLASS_NAME + '.getAccountDetails', params, LOG_TAGS);
        Boolean logsSaved = false;

        try {
            Flinks_Configuration__c fc = Flinks_Configuration__c.getInstance();
            if (fc == null) {
                DebugLogUtil.error(CLASS_NAME + '.getAccountDetails: Flinks_Configuration__c custom setting not found.', LOG_TAGS);
                throw new AuraHandledException('Flinks configuration is missing. Please contact administrator.');
            }
            DebugLogUtil.info(CLASS_NAME + '.getAccountDetails: Flinks configuration loaded. RequestId: ' + reqId, LOG_TAGS);

            HttpRequest req = new HttpRequest();
            req.setHeader('Content-Type', 'application/json');
            String endpoint = fc.Base_Url__c + fc.Customer_Id__c + '/BankingServices/GetAccountsDetail';
            req.setEndpoint(endpoint);
            req.setMethod('POST');
            String requestBody = '{"RequestId" : "' + reqId + '", "WithTransactions" : true , "DaysOfTransactions" : "Days365" }';
            req.setBody(requestBody);

            DebugLogUtil.info(CLASS_NAME + '.getAccountDetails: Preparing Flinks callout. Endpoint: ' + endpoint + ', Method: POST, RequestId: ' + reqId, LOG_TAGS);
            // Avoid logging full requestBody if it contains sensitive info, or mask it.
            // DebugLogUtil.info(CLASS_NAME + '.getAccountDetails: Request Body: ' + requestBody, LOG_TAGS);


            Http http = new Http();
            HTTPResponse res;

            DebugLogUtil.info(CLASS_NAME + '.getAccountDetails: Sending HTTP request to Flinks for RequestId: ' + reqId, LOG_TAGS);
            res = http.send(req);
            DebugLogUtil.info(CLASS_NAME + '.getAccountDetails: Received HTTP response from Flinks. Status Code: ' + res.getStatusCode() + ', RequestId: ' + reqId, LOG_TAGS);

            // Existing logging - can be kept for its specific purpose or integrated/replaced
            UIT_Utility.LogFlinksCallout(requestBody, res.getBody(), null, 'GetAccountsDetail', reqId, res.getStatusCode(), null, false);

            if (res.getStatusCode() == 200 || res.getStatusCode() == 202) {
                DebugLogUtil.info(CLASS_NAME + '.getAccountDetails: Status code ' + res.getStatusCode() + '. Updating Bank Accounts Is_Get_Details_Running__c flag for RequestId: ' + reqId, LOG_TAGS);
                List<Bank_Account__c> bankAccs = [SELECT Id, Is_Get_Details_Running__c, Request_Id__c FROM Bank_Account__c WHERE Request_Id__c = :reqId];
                if (!bankAccs.isEmpty()) {
                    for (Bank_Account__c bankAcc : bankAccs) {
                        bankAcc.Is_Get_Details_Running__c = true;
                    }
                    update bankAccs;
                    DebugLogUtil.info(CLASS_NAME + '.getAccountDetails: Updated ' + bankAccs.size() + ' Bank_Account__c records.', LOG_TAGS);
                } else {
                    DebugLogUtil.info(CLASS_NAME + '.getAccountDetails: No Bank_Account__c records found to update for RequestId: ' + reqId, LOG_TAGS);
                }
            }
            DebugLogUtil.info(CLASS_NAME + '.getAccountDetails: Calling FlinksAsyncCallout.parseResponse for RequestId: ' + reqId, LOG_TAGS);
            FlinksAsyncCallout.parseResponse(res);

        } catch (Exception e) {
            DebugLogUtil.error(CLASS_NAME + '.getAccountDetails: Exception occurred for RequestId: ' + reqId, e, LOG_TAGS);
            // Existing exception logging
            UIT_Utility.LogException(UserInfo.getUserId(), e, 'FlinksController.getAccountDetails');
            throw new AuraHandledException(e.getMessage()); // Rethrow for Aura framework
        } finally {
            DebugLogUtil.exit(CLASS_NAME + '.getAccountDetails', LOG_TAGS);
            DebugLogUtil.saveLogs();
        }
    }

    @AuraEnabled
    global static List<Bank_Account__c> fetchBankAccounts() {
        DebugLogUtil.entry(CLASS_NAME + '.fetchBankAccounts', null, LOG_TAGS);
        List<Bank_Account__c> bankAccsToReturn = new List<Bank_Account__c>();
        Boolean logsSaved = false;

        try {
            Flinks_Configuration__c fc = Flinks_Configuration__c.getInstance();
            if (fc == null) {
                DebugLogUtil.error(CLASS_NAME + '.fetchBankAccounts: Flinks_Configuration__c custom setting not found.', LOG_TAGS);
                throw new AuraHandledException('Flinks configuration is missing. Please contact administrator.');
            }

            User loggedInUser = [SELECT Id, ContactId, Contact.Name FROM User WHERE Id = :UserInfo.getUserId()];
            DebugLogUtil.info(CLASS_NAME + '.fetchBankAccounts: Fetched logged in user: ' + loggedInUser.Id + ', ContactId: ' + loggedInUser.ContactId, LOG_TAGS);

            bankAccsToReturn = [
                SELECT Id, Authorize_Async_Request_Id__c, Name, Account_Id__c, Institution_Id__c,
                       Institution_Name__c, Is_Active__c, Type__c, Login_ID__c, Connection_Error_Code__c,
                       Connection_Error_Message__c, Contact__c, Contact__r.Name
                FROM Bank_Account__c
                WHERE Contact__c = :loggedInUser.ContactId
            ];
            DebugLogUtil.info(CLASS_NAME + '.fetchBankAccounts: Fetched ' + bankAccsToReturn.size() + ' bank accounts for ContactId: ' + loggedInUser.ContactId, LOG_TAGS);

            if (!bankAccsToReturn.isEmpty()) {
                Set<String> linkedLoginIds = new Set<String>();
                for (Bank_Account__c acc : bankAccsToReturn) {
                    if (acc.Is_Active__c) {
                        linkedLoginIds.add(acc.Login_ID__c);
                    }
                }
                DebugLogUtil.info(CLASS_NAME + '.fetchBankAccounts: Active linkedLoginIds: ' + String.join(new List<String>(linkedLoginIds), ', '), LOG_TAGS);

                List<Object> ineligibleCards = getIneligibleCardsInternal(); // Use internal helper
                DebugLogUtil.info(CLASS_NAME + '.fetchBankAccounts: Ineligible cards count: ' + (ineligibleCards != null ? ineligibleCards.size() : 0), LOG_TAGS);

                if (ineligibleCards.isEmpty() || linkedLoginIds.isEmpty()) {
                    DebugLogUtil.info(CLASS_NAME + '.fetchBankAccounts: No ineligible cards or no linked active accounts to process for errors.', LOG_TAGS);
                    return bankAccsToReturn;
                }

                Map<String, String> loginErrorCode = new Map<String, String>();
                for (Object obj : ineligibleCards) {
                    Map<String, Object> mp_Obj = (Map<String, Object>) obj;
                    if (linkedLoginIds.contains((String) mp_Obj.get('LoginId'))) {
                        loginErrorCode.put((String) mp_Obj.get('LoginId'), (String) mp_Obj.get('LastRefreshErrorCode'));
                    }
                }
                DebugLogUtil.info(CLASS_NAME + '.fetchBankAccounts: Login error codes for active accounts: ' + JSON.serialize(loginErrorCode), LOG_TAGS);

                if (!loginErrorCode.keySet().isEmpty()) {
                    String urlPrefix = mf123Opp.isSandbox() ? '/mf/s/' : '/s/';
                    String siteLink = '';
                    List<Site> sites = [SELECT Id, Name, Subdomain, UrlPathPrefix, Status FROM Site WHERE MasterLabel = 'Mobilization Funding' AND Status = 'Active' LIMIT 1]; // Simpler query
                    if(!sites.isEmpty()){
                        Site sit = sites[0];
                        urlPrefix = sit.UrlPathPrefix.startsWith('/') ? sit.UrlPathPrefix : '/' + sit.UrlPathPrefix;
                        if(!urlPrefix.endsWith('/')) urlPrefix += '/';

                        List<DomainSite> domainSites = [SELECT Id, Domain.Domain FROM DomainSite WHERE SiteId = :sit.Id LIMIT 1];
                        if(!domainSites.isEmpty()){
                             siteLink = 'https://' + domainSites[0].Domain.Domain + urlPrefix + 'linkedaccounts';
                             DebugLogUtil.info(CLASS_NAME + '.fetchBankAccounts: Site link for re-authentication: ' + siteLink, LOG_TAGS);
                        } else {
                            DebugLogUtil.warn(CLASS_NAME + '.fetchBankAccounts: No DomainSite found for SiteId: ' + sit.Id, LOG_TAGS);
                        }
                    } else {
                         DebugLogUtil.warn(CLASS_NAME + '.fetchBankAccounts: No active Site found with MasterLabel "Mobilization Funding".', LOG_TAGS);
                    }


                    String accountNamesWithError = '';
                    List<Bank_Account__c> accountsToUpdate = new List<Bank_Account__c>();

                    for (Bank_Account__c bkAcct : bankAccsToReturn) {
                        if (loginErrorCode.containsKey(bkAcct.Login_ID__c)) {
                            accountNamesWithError += (String.isBlank(accountNamesWithError) ? '' : ', ') + bkAcct.Name + '(' + bkAcct.Institution_Name__c + ')';
                            bkAcct.Is_Active__c = false;
                            String errorCode = loginErrorCode.get(bkAcct.Login_ID__c);
                            if (errorCode != null) {
                                bkAcct.Connection_Error_Code__c = errorCode;
                                bkAcct.Connection_Error_Message__c = FlinkNightlyRefresh.FlinksErrorCodeMap.get(errorCode); // Assuming FlinkNightlyRefresh is accessible
                            } else {
                                bkAcct.Connection_Error_Code__c = null;
                                bkAcct.Connection_Error_Message__c = 'Connection of this account got revoked!';
                            }
                            accountsToUpdate.add(new Bank_Account__c(Id = bkAcct.Id, Is_Active__c = bkAcct.Is_Active__c, Connection_Error_Code__c = bkAcct.Connection_Error_Code__c, Connection_Error_Message__c = bkAcct.Connection_Error_Message__c));
                        }
                    }

                    if (String.isNotBlank(accountNamesWithError)) {
                        DebugLogUtil.info(CLASS_NAME + '.fetchBankAccounts: Accounts with connection issues: ' + accountNamesWithError, LOG_TAGS);
                        List<Messaging.SingleEmailMessage> emails = new List<Messaging.SingleEmailMessage>();
                        String contactPrimaryName = bankAccsToReturn.isEmpty() || bankAccsToReturn[0].Contact__r == null ? 'Valued Customer' : bankAccsToReturn[0].Contact__r.Name;

                        Messaging.SingleEmailMessage messageToContact = new Messaging.SingleEmailMessage();
                        messageToContact.setHtmlBody('Hi ' + contactPrimaryName + ',<br/>Connection of your some Accounts got interrupted - ' + accountNamesWithError + '<br/><br/>Please login <a href="' + siteLink + '">here</a> to reauthenticate.');
                        messageToContact.setSubject('Connection of Accounts got interrupted');
                        if(!bankAccsToReturn.isEmpty() && bankAccsToReturn[0].Contact__c != null){
                            messageToContact.setTargetObjectId(bankAccsToReturn[0].Contact__c);
                            emails.add(messageToContact);
                        }


                        if (fc.email__c != null) {
                            Messaging.SingleEmailMessage messageToSupport = new Messaging.SingleEmailMessage();
                            messageToSupport.setHtmlBody('Hi,<br/>Connection of some Accounts got interrupted for Contact ' + contactPrimaryName + ' (' + (bankAccsToReturn.isEmpty() ? 'N/A' : bankAccsToReturn[0].Contact__c) + ') - Accounts: ' + accountNamesWithError);
                            messageToSupport.setSubject('Flinks: Connection of Accounts got interrupted');
                            messageToSupport.setToAddresses(fc.email__c.split(','));
                            emails.add(messageToSupport);
                        }

                        if (!emails.isEmpty()) {
                            DebugLogUtil.info(CLASS_NAME + '.fetchBankAccounts: Sending ' + emails.size() + ' email notifications.', LOG_TAGS);
                            Messaging.SendEmailResult[] r = Messaging.sendEmail(emails);
                             for(Messaging.SendEmailResult er : r){
                                if(!er.isSuccess()){
                                     DebugLogUtil.warn(CLASS_NAME + '.fetchBankAccounts: Email sending failed: ' + er.getErrors()[0].getStatusCode() + ' - ' + er.getErrors()[0].getMessage(), LOG_TAGS);
                                }
                            }
                        }
                    }

                    if (!accountsToUpdate.isEmpty()) {
                        DebugLogUtil.info(CLASS_NAME + '.fetchBankAccounts: Updating ' + accountsToUpdate.size() + ' bank accounts with error status.', LOG_TAGS);
                        update accountsToUpdate;
                    }
                }
            }
            return bankAccsToReturn;
        } catch (Exception e) {
            DebugLogUtil.error(CLASS_NAME + '.fetchBankAccounts: Exception occurred.', e, LOG_TAGS);
            UIT_Utility.LogException(UserInfo.getUserId(), e, 'FlinksController.fetchBankAccounts');
            throw new AuraHandledException(e.getMessage());
        } finally {
            DebugLogUtil.exit(CLASS_NAME + '.fetchBankAccounts', LOG_TAGS);
            DebugLogUtil.info(CLASS_NAME + '.fetchBankAccounts: Attempting to save logs.', LOG_TAGS);
            DebugLogUtil.saveLogs();
        }
    }

    @AuraEnabled
    global static Map<String, String> getAccRec() {
        DebugLogUtil.entry(CLASS_NAME + '.getAccRec', null, LOG_TAGS);
        Map<String, String> returnMap = new Map<String, String>();
        Boolean logsSaved = false;
        try {
            Flinks_Configuration__c fc = Flinks_Configuration__c.getInstance();
            if (fc == null) {
                DebugLogUtil.error(CLASS_NAME + '.getAccRec: Flinks_Configuration__c custom setting not found.', LOG_TAGS);
                throw new AuraHandledException('Flinks configuration is missing.');
            }

            User loggedInUser = [SELECT Id, ContactId, Account.Manual_Upload__c FROM User WHERE Id = :UserInfo.getUserId()];
            DebugLogUtil.info(CLASS_NAME + '.getAccRec: Fetched logged in user: ' + loggedInUser.Id, LOG_TAGS);

            Boolean manualUpload = loggedInUser.Account != null && loggedInUser.Account.Manual_Upload__c;
            returnMap.put('showUpload', String.valueOf(manualUpload));
            returnMap.put('iframeUrl', fc.Iframe_Url__c);
            DebugLogUtil.info(CLASS_NAME + '.getAccRec: showUpload: ' + manualUpload + ', iframeUrl: ' + (fc.Iframe_Url__c != null ? 'Set' : 'Not Set'), LOG_TAGS);
            return returnMap;
        } catch (Exception e) {
            DebugLogUtil.error(CLASS_NAME + '.getAccRec: Exception occurred.', e, LOG_TAGS);
            UIT_Utility.LogException(UserInfo.getUserId(), e, 'FlinksController.getAccRec');
            throw new AuraHandledException(e.getMessage());
        } finally {
            DebugLogUtil.exit(CLASS_NAME + '.getAccRec', LOG_TAGS);
            DebugLogUtil.info(CLASS_NAME + '.getAccRec: Attempting to save logs.', LOG_TAGS);
            DebugLogUtil.saveLogs();
        }
    }

    // Internal helper to avoid AuraEnabled on a method called by another AuraEnabled method if not needed directly from client
    private static List<Object> getIneligibleCardsInternal() {
        DebugLogUtil.entry(CLASS_NAME + '.getIneligibleCardsInternal', null, LOG_TAGS);
        try {
            Flinks_Configuration__c fc = Flinks_Configuration__c.getInstance();
            if (fc == null || String.isBlank(fc.Bearer_Token__c)) {
                DebugLogUtil.error(CLASS_NAME + '.getIneligibleCardsInternal: Flinks config or Bearer Token is missing.', LOG_TAGS);
                return new List<Object>(); // Or throw, depending on desired behavior
            }

            HttpRequest req = new HttpRequest();
            String endpoint = fc.Base_Url__c + fc.Customer_Id__c + '/BankingServices/GetNightlyRefreshStatus';
            req.setEndpoint(endpoint);
            req.setMethod('GET');
            req.setHeader('Authorization', 'Bearer ' + fc.Bearer_Token__c);
            DebugLogUtil.info(CLASS_NAME + '.getIneligibleCardsInternal: Calling endpoint: ' + endpoint, LOG_TAGS);

            Http http = new Http();
            HTTPResponse res = http.send(req);
            DebugLogUtil.info(CLASS_NAME + '.getIneligibleCardsInternal: Response Status: ' + res.getStatusCode(), LOG_TAGS);
            // DebugLogUtil.info(CLASS_NAME + '.getIneligibleCardsInternal: Response Body: ' + res.getBody(), LOG_TAGS); // Careful with PII

            UIT_Utility.LogFlinksCallout(null, res.getBody(), null, 'GetNightlyRefreshStatus', null, res.getStatusCode(), null, false);

            if (res.getStatusCode() == 200 && String.isNotBlank(res.getBody())) {
                Map<String, Object> mp_StrObj = (Map<String, Object>) JSON.deserializeUntyped(res.getBody());
                List<Object> ineligible = (List<Object>) mp_StrObj.get('IneligibleCards');
                DebugLogUtil.info(CLASS_NAME + '.getIneligibleCardsInternal: Found ' + (ineligible != null ? ineligible.size() : 0) + ' ineligible cards.', LOG_TAGS);
                return ineligible != null ? ineligible : new List<Object>();
            } else {
                DebugLogUtil.warn(CLASS_NAME + '.getIneligibleCardsInternal: Call failed or empty response. Status: ' + res.getStatusCode(), LOG_TAGS);
                return new List<Object>();
            }
        } catch (Exception e) {
            DebugLogUtil.error(CLASS_NAME + '.getIneligibleCardsInternal: Exception occurred.', e, LOG_TAGS);
            UIT_Utility.LogException(UserInfo.getUserId(), e, 'FlinksController.getIneligibleCards'); // Matched original method name for this log
            // Not throwing AuraHandledException as it's an internal helper
            return new List<Object>(); // Return empty on error to prevent breaking caller
        } finally {
            DebugLogUtil.exit(CLASS_NAME + '.getIneligibleCardsInternal', LOG_TAGS);
            DebugLogUtil.saveLogs();
        }
        
    }

    @AuraEnabled
    public static List<Object> getIneligibleCards() {
        // This method is now a simple wrapper if getIneligibleCardsInternal does the work.
        // Or, if it's meant to be directly callable and save its own logs:
        DebugLogUtil.entry(CLASS_NAME + '.getIneligibleCards (AuraEnabled)', null, LOG_TAGS);
        List<Object> result = new List<Object>();
        Boolean logsSaved = false;
        try {
            result = getIneligibleCardsInternal(); // Calls the actual logic
            return result;
        } catch (Exception e) { // Should ideally not happen if internal handles it, but as a safeguard
            DebugLogUtil.error(CLASS_NAME + '.getIneligibleCards (AuraEnabled): Exception occurred.', e, LOG_TAGS);
            UIT_Utility.LogException(UserInfo.getUserId(), e, 'FlinksController.getIneligibleCards');
            throw new AuraHandledException(e.getMessage());
        } finally {
            DebugLogUtil.exit(CLASS_NAME + '.getIneligibleCards (AuraEnabled)', LOG_TAGS);
            DebugLogUtil.info(CLASS_NAME + '.getIneligibleCards (AuraEnabled): Attempting to save logs.', LOG_TAGS);
            DebugLogUtil.saveLogs();
        }
    }


    @AuraEnabled
    public static Flinks_Configuration__c getFlinkConfig() {
        DebugLogUtil.entry(CLASS_NAME + '.getFlinkConfig', null, LOG_TAGS);
        Boolean logsSaved = false;
        try {
            Flinks_Configuration__c config = Flinks_Configuration__c.getInstance();
            DebugLogUtil.info(CLASS_NAME + '.getFlinkConfig: Flinks configuration requested. Is null: ' + (config == null), LOG_TAGS);
            return config;
        } catch (Exception e) {
            DebugLogUtil.error(CLASS_NAME + '.getFlinkConfig: Exception occurred.', e, LOG_TAGS);
            throw new AuraHandledException('Error retrieving Flinks Configuration: ' + e.getMessage());
        } finally {
            DebugLogUtil.exit(CLASS_NAME + '.getFlinkConfig', LOG_TAGS);
            DebugLogUtil.info(CLASS_NAME + '.getFlinkConfig: Attempting to save logs.', LOG_TAGS);
            DebugLogUtil.saveLogs();
        }
    }

    @AuraEnabled
    public static void removeAccount(String loginId) {
        Map<String, Object> params = new Map<String, Object>{'loginId' => loginId};
        DebugLogUtil.entry(CLASS_NAME + '.removeAccount', params, LOG_TAGS);
        Boolean logsSaved = false;
        try {
            List<Bank_Account__c> bankAccs = [SELECT Id, Login_ID__c, Request_Id__c FROM Bank_Account__c WHERE Login_ID__c = :loginId];
            if (bankAccs.isEmpty()) {
                DebugLogUtil.warn(CLASS_NAME + '.removeAccount: No bank accounts found for LoginId: ' + loginId, LOG_TAGS);
                return; // Or throw error
            }
            DebugLogUtil.info(CLASS_NAME + '.removeAccount: Found ' + bankAccs.size() + ' accounts for LoginId: ' + loginId, LOG_TAGS);

            Flinks_Configuration__c fc = Flinks_Configuration__c.getInstance();
            if (fc == null) {
                DebugLogUtil.error(CLASS_NAME + '.removeAccount: Flinks_Configuration__c custom setting not found.', LOG_TAGS);
                throw new AuraHandledException('Flinks configuration is missing.');
            }

            HttpRequest req = new HttpRequest();
            req.setHeader('Content-Type', 'application/json');
            String endpoint = fc.Base_Url__c + fc.Customer_Id__c + '/BankingServices/DeleteCard/' + loginId;
            req.setEndpoint(endpoint);
            req.setMethod('DELETE');
            DebugLogUtil.info(CLASS_NAME + '.removeAccount: Calling DeleteCard endpoint: ' + endpoint, LOG_TAGS);

            Http http = new Http();
            HTTPResponse res = http.send(req);
            DebugLogUtil.info(CLASS_NAME + '.removeAccount: DeleteCard response Status: ' + res.getStatusCode(), LOG_TAGS);

            String reqIdForLog = !bankAccs.isEmpty() ? bankAccs[0].Request_Id__c : null;
            UIT_Utility.LogFlinksCallout(null, res.getBody(), loginId, 'DeleteCard', reqIdForLog, res.getStatusCode(), null, false);

            if (res.getStatusCode() == 200) {
                DebugLogUtil.info(CLASS_NAME + '.removeAccount: Flinks DeleteCard successful. Deleting local Bank_Account__c records.', LOG_TAGS);
                delete bankAccs;
                DebugLogUtil.info(CLASS_NAME + '.removeAccount: Deleted ' + bankAccs.size() + ' local Bank_Account__c records.', LOG_TAGS);
            } else {
                DebugLogUtil.warn(CLASS_NAME + '.removeAccount: Flinks DeleteCard call failed or did not return 200. Status: ' + res.getStatusCode() + '. Body: ' + res.getBody(), LOG_TAGS);
                // Optionally throw an exception if local delete should not proceed
            }

        } catch (Exception e) {
            DebugLogUtil.error(CLASS_NAME + '.removeAccount: Exception occurred for LoginId: ' + loginId, e, LOG_TAGS);
            UIT_Utility.LogException(UserInfo.getUserId(), e, 'FlinksController.removeAccount');
            throw new AuraHandledException(e.getMessage());
        } finally {
            DebugLogUtil.exit(CLASS_NAME + '.removeAccount', LOG_TAGS);
            DebugLogUtil.info(CLASS_NAME + '.removeAccount: Attempting to save logs.', LOG_TAGS);
            DebugLogUtil.saveLogs();
        }
    }


    @AuraEnabled
    global static List<Bank_Account__c> getAllAccRecs() {
        DebugLogUtil.entry(CLASS_NAME + '.getAllAccRecs', null, LOG_TAGS);
        List<Bank_Account__c> bankAccs;
        Boolean logsSaved = false;
        try {
            bankAccs = [
                SELECT Id, Request_Id__c, Is_Get_Details_Running__c, Statement_Uploaded__c,
                       Authorize_Async_Request_Id__c, Name, Account_Id__c, Institution_Id__c,
                       Institution_Name__c, Is_Active__c, Type__c, Login_ID__c, Connection_Error_Code__c,
                       Connection_Error_Message__c, Contact__c, Contact__r.Name, Current_Balance__c
                FROM Bank_Account__c
            ];
            DebugLogUtil.info(CLASS_NAME + '.getAllAccRecs: Fetched ' + (bankAccs != null ? bankAccs.size() : 0) + ' bank account records.', LOG_TAGS);
            return bankAccs;
        } catch (Exception e) {
            DebugLogUtil.error(CLASS_NAME + '.getAllAccRecs: Exception occurred.', e, LOG_TAGS);
            UIT_Utility.LogException(UserInfo.getUserId(), e, 'FlinksController.getAllAccRecs');
            throw new AuraHandledException(e.getMessage());
        } finally {
            DebugLogUtil.exit(CLASS_NAME + '.getAllAccRecs', LOG_TAGS);
            DebugLogUtil.info(CLASS_NAME + '.getAllAccRecs: Attempting to save logs.', LOG_TAGS);
            DebugLogUtil.saveLogs();
        }
    }

    @AuraEnabled
    global static void runRefreshBatch() {
        DebugLogUtil.entry(CLASS_NAME + '.runRefreshBatch', null, LOG_TAGS);
        Boolean logsSaved = false;
        try {
            DebugLogUtil.info(CLASS_NAME + '.runRefreshBatch: Calling FlinkNightlyRefresh.getIneligibleCards().', LOG_TAGS);
            // Assuming FlinkNightlyRefresh.getIneligibleCards() might be a batch or queueable start
            // Or if it's synchronous and does significant work:
            FlinkNightlyRefresh.getIneligibleCards(); // Ensure this class/method is accessible
            DebugLogUtil.info(CLASS_NAME + '.runRefreshBatch: FlinkNightlyRefresh.getIneligibleCards() called successfully.', LOG_TAGS);
        } catch (Exception e) {
            DebugLogUtil.error(CLASS_NAME + '.runRefreshBatch: Exception occurred.', e, LOG_TAGS);
            UIT_Utility.LogException(UserInfo.getUserId(), e, 'FlinksController.runRefreshBatch');
            throw new AuraHandledException(e.getMessage());
        } finally {
            DebugLogUtil.exit(CLASS_NAME + '.runRefreshBatch', LOG_TAGS);
            DebugLogUtil.info(CLASS_NAME + '.runRefreshBatch: Attempting to save logs.', LOG_TAGS);
            DebugLogUtil.saveLogs();
        }
    }
}