@SuppressWarnings('PMD')
public without sharing class AttachmentCompController {
   
    @AuraEnabled
    public static void uploadFiles(Id recordId, List<String> contentDocumentIds, Boolean isInternal) {
        try {
            
            List<ContentDocumentLink> linksToCreate = new List<ContentDocumentLink>();

            for (String contentDocumentId : contentDocumentIds) {
                ContentDocumentLink link = new ContentDocumentLink();
                link.ContentDocumentId = contentDocumentId;
                link.LinkedEntityId = recordId;
                link.ShareType = 'V'; 
                link.Visibility = isInternal ? 'InternalUsers' : 'AllUsers';
                linksToCreate.add(link);

            }

            insert linksToCreate;

        } catch (Exception e) {
            System.debug('Exception occurred: ' + e.getMessage());
            throw new AuraHandledException(e.getMessage());
        }
    }

    // @AuraEnabled
    // public static List<ContentDocument> getFilesOfRecord(Id recordId) {
    //     List<ContentDocument> contentDocuments = new List<ContentDocument>();

    //     try {
    //         contentDocuments = getContentDocumentsOfRecordIds(new List<Id>{ recordId });

    //         return contentDocuments;
    //     } catch (Exception e) {
    //         System.debug('Exception occurred: ' + e.getMessage() + ' -- ' + e.getStackTraceString());
    //         throw new AuraHandledException(e.getMessage() + ' -- ' + e.getStackTraceString());
    //     }
    // }

 public class FileFetchResult {
        @AuraEnabled public List<ContentDocument> parentFiles { get; set; }
        @AuraEnabled public List<ContentDocument> childFiles  { get; set; }
    }

    @AuraEnabled
    public static FileFetchResult getFilesOfRecord(Id recordId) {
        FileFetchResult result = new FileFetchResult();
        result.parentFiles = new List<ContentDocument>();
        result.childFiles  = new List<ContentDocument>();

        try {
            // 1) Always fetch the parent record’s files:
            result.parentFiles = getContentDocumentsOfRecordIds(new List<Id>{ recordId });

            // 2) See if we need to roll up any child objects
            String masterApiName = recordId.getSObjectType().getDescribe().getName();
            List<Custom_Files_Related_Rollups__mdt> configs = [
                SELECT Roll_up__c, Parent__c
                  FROM Custom_Files_Related_Rollups__mdt
                 WHERE MasterLabel = :masterApiName
            ];
            if (!configs.isEmpty()) {
                // gather all child IDs
                Set<Id> childIds = new Set<Id>();
                for (Custom_Files_Related_Rollups__mdt cfg : configs) {
                    String childObject   = cfg.Roll_up__c;
                    String lookupField   = cfg.Parent__c;
                    for (SObject child : Database.query(
                        'SELECT Id FROM ' + childObject +
                        ' WHERE ' + lookupField + ' = :recordId'
                    )) {
                        childIds.add((Id)child.get('Id'));
                    }
                }
                if (!childIds.isEmpty()) {
                    // fetch files for those child records
                    result.childFiles = getContentDocumentsOfRecordIds(
                        new List<Id>(childIds)
                    );
                }
            }

            return result;
        }
        catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }


    private static List<ContentDocument> getContentDocumentsOfRecordIds(List<Id> recordIds) {
        Boolean isOpportunity = recordIds[0].getSObjectType() == Opportunity.SObjectType;
        List<ContentDocumentLink> contentDocumentLinks = [
            SELECT Id, ContentDocumentId, LinkedEntityId
            FROM ContentDocumentLink
            WHERE LinkedEntityId IN :recordIds
            WITH SECURITY_ENFORCED
        ];


        Set<Id> contentDocumentIds = new Set<Id>();
        for (ContentDocumentLink cdl : contentDocumentLinks) {
            contentDocumentIds.add(cdl.ContentDocumentId);
        }
         List<ContentDocument> contentDocuments;
            if (isOpportunity) {
                contentDocuments = [
                    SELECT
                        Id,
                        Title,
                        ContentSize,
                        Description,
                        FileExtension,
                        FileType,
                        LatestPublishedVersionId,
                        LatestPublishedVersion.Dropbox_Url__c,
                        LatestPublishedVersion.Dropbox_Sync_Status__c,
                        ContentDocument.CreatedBy.FirstName,
                        ContentDocument.CreatedBy.LastName,
                        ContentModifiedDate,
                        (SELECT DistributionPublicUrl, ContentDownloadUrl FROM ContentDistributions)
                    FROM ContentDocument
                    WHERE Id IN :contentDocumentIds AND FileExtension != 'snote' AND Title != 'Uploaded Signature'
                    WITH SECURITY_ENFORCED
                    ORDER BY ContentModifiedDate DESC
                ];
            } else {
                contentDocuments = [
                    SELECT
                        Id,
                        Title,
                        ContentSize,
                        Description,
                        FileExtension,
                        FileType,
                        LatestPublishedVersionId,
                        LatestPublishedVersion.Dropbox_Url__c,
                        LatestPublishedVersion.Dropbox_Sync_Status__c,
                        ContentDocument.CreatedBy.FirstName,
                        ContentDocument.CreatedBy.LastName,
                        ContentModifiedDate
                    FROM ContentDocument
                    WHERE Id IN :contentDocumentIds AND FileExtension != 'snote'
                    WITH SECURITY_ENFORCED
                    ORDER BY ContentModifiedDate DESC
                ];
            }
        return contentDocuments;
    }

    @AuraEnabled
    public static void deleteFile(String contentDocumentId) {
        try {
            ContentDocument cd = [SELECT Id FROM ContentDocument WHERE Id = :contentDocumentId LIMIT 1];
            delete cd;
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }    
    
    // @AuraEnabled
    // public static List<ContentDocumentWrapper> getFilesOfRecordSF(Id recordId, Boolean showAllData) {
    //     List<ContentDocumentWrapper> result = new List<ContentDocumentWrapper>();
    
    //     try {
    //         Boolean isOpportunity = recordId.getSObjectType() == Opportunity.SObjectType;
    
    //         List<ContentDocumentLink> contentDocumentLinks = [
    //             SELECT 
    //                 Id, 
    //                 ContentDocumentId, 
    //                 LinkedEntityId, 
    //                 Visibility
    //             FROM ContentDocumentLink
    //             WHERE LinkedEntityId = :recordId
    //             WITH SECURITY_ENFORCED
    //         ];
    
    //         Set<Id> contentDocumentIds = new Set<Id>();
    //         for (ContentDocumentLink cdl : contentDocumentLinks) {
    //             contentDocumentIds.add(cdl.ContentDocumentId);
    //         }
    
    //         List<ContentDocument> contentDocuments;
    //         Integer limitClause = showAllData ? 2000 : 5; 
    //         if (isOpportunity) {
    //             contentDocuments = [
    //                 SELECT
    //                     Id,
    //                     Title,
    //                     ContentSize,
    //                     Description,
    //                     FileExtension,
    //                     FileType,
    //                     LatestPublishedVersionId,
    //                     LatestPublishedVersion.Dropbox_Url__c,
    //                     LatestPublishedVersion.Dropbox_Sync_Status__c,
    //                     ContentDocument.CreatedBy.FirstName,
    //                     ContentDocument.CreatedBy.LastName,
    //                     ContentModifiedDate,
    //                     (SELECT DistributionPublicUrl, ContentDownloadUrl FROM ContentDistributions)
    //                 FROM ContentDocument
    //                 WHERE Id IN :contentDocumentIds AND FileExtension != 'snote'
    //                 WITH SECURITY_ENFORCED
    //                 ORDER BY ContentModifiedDate DESC LIMIT :limitClause
    //             ];
    //         } else {
    //             contentDocuments = [
    //                 SELECT
    //                     Id,
    //                     Title,
    //                     ContentSize,
    //                     Description,
    //                     FileExtension,
    //                     FileType,
    //                     LatestPublishedVersionId,
    //                     LatestPublishedVersion.Dropbox_Url__c,
    //                     LatestPublishedVersion.Dropbox_Sync_Status__c,
    //                     ContentDocument.CreatedBy.FirstName,
    //                     ContentDocument.CreatedBy.LastName,
    //                     ContentModifiedDate
    //                 FROM ContentDocument
    //                 WHERE Id IN :contentDocumentIds AND FileExtension != 'snote'
    //                 WITH SECURITY_ENFORCED
    //                 ORDER BY ContentModifiedDate DESC LIMIT :limitClause
    //             ];
    //         }
    
    //         // Map ContentDocumentIds to their corresponding ContentDocuments
    //         Map<Id, ContentDocument> contentDocumentMap = new Map<Id, ContentDocument>();
    //         for (ContentDocument doc : contentDocuments) {
    //             contentDocumentMap.put(doc.Id, doc);
    //         }
    
    //         // Build wrapper results
    //         for (ContentDocumentLink cdl : contentDocumentLinks) {
    //             ContentDocument doc = contentDocumentMap.get(cdl.ContentDocumentId);
    //             if (doc != null) {
    //                 ContentDocumentWrapper wrapper = new ContentDocumentWrapper();
    //                 wrapper.contentDocument = doc;
    //                 wrapper.contentDocumentLink = cdl;
    //                 result.add(wrapper);
    //             }
    //         }
    //     } catch (Exception e) {
    //         System.debug('Exception occurred: ' + e.getMessage() + ' -- ' + e.getStackTraceString());
    //         throw new AuraHandledException(e.getMessage() + ' -- ' + e.getStackTraceString());
    //     }
    
    //     return result;
    // }

    @AuraEnabled
public static List<ContentDocumentWrapper> getFilesOfRecordSF(Id recordId, Boolean showAllData) {
    List<ContentDocumentWrapper> result = new List<ContentDocumentWrapper>();

    try {
        Boolean isOpportunity = recordId.getSObjectType() == Opportunity.SObjectType;

        // ─── PARENT RECORD LINKS & DOCUMENTS ────────────────────────────
        List<ContentDocumentLink> contentDocumentLinks = [
            SELECT Id, ContentDocumentId, LinkedEntityId, Visibility
              FROM ContentDocumentLink
             WHERE LinkedEntityId = :recordId
             WITH SECURITY_ENFORCED
        ];

        Set<Id> contentDocumentIds = new Set<Id>();
        for (ContentDocumentLink cdl : contentDocumentLinks) {
            contentDocumentIds.add(cdl.ContentDocumentId);
        }

        Integer limitClause = showAllData ? 2000 : 5;
        List<ContentDocument> contentDocuments;
        if (isOpportunity) {
            contentDocuments = [
                SELECT
                  Id, Title, ContentSize, Description, FileExtension, FileType,
                  LatestPublishedVersionId,
                  LatestPublishedVersion.Dropbox_Url__c,
                  LatestPublishedVersion.Dropbox_Sync_Status__c,
                  ContentDocument.CreatedBy.FirstName,
                  ContentDocument.CreatedBy.LastName,
                  ContentModifiedDate,
                  (SELECT DistributionPublicUrl, ContentDownloadUrl FROM ContentDistributions)
                FROM ContentDocument
               WHERE Id IN :contentDocumentIds
                 AND FileExtension != 'snote'
               WITH SECURITY_ENFORCED
               ORDER BY ContentModifiedDate DESC
               LIMIT :limitClause
            ];
        } else {
            contentDocuments = [
                SELECT
                  Id, Title, ContentSize, Description, FileExtension, FileType,
                  LatestPublishedVersionId,
                  LatestPublishedVersion.Dropbox_Url__c,
                  LatestPublishedVersion.Dropbox_Sync_Status__c,
                  ContentDocument.CreatedBy.FirstName,
                  ContentDocument.CreatedBy.LastName,
                  ContentModifiedDate
                FROM ContentDocument
               WHERE Id IN :contentDocumentIds
                 AND FileExtension != 'snote'
               WITH SECURITY_ENFORCED
               ORDER BY ContentModifiedDate DESC
               LIMIT :limitClause
            ];
        }

        Map<Id, ContentDocument> contentDocumentMap = new Map<Id, ContentDocument>();
        for (ContentDocument doc : contentDocuments) {
            contentDocumentMap.put(doc.Id, doc);
        }

        for (ContentDocumentLink cdl : contentDocumentLinks) {
            ContentDocument doc = contentDocumentMap.get(cdl.ContentDocumentId);
            if (doc != null) {
                ContentDocumentWrapper wrapper = new ContentDocumentWrapper();
                wrapper.contentDocument     = doc;
                wrapper.contentDocumentLink = cdl;
                result.add(wrapper);
            }
        }

        // ─── CHILD ROLL-UP CONFIGURATION ────────────────────────────────
        String masterApiName = recordId
            .getSObjectType()
            .getDescribe()
            .getName();

        List<Custom_Files_Related_Rollups__mdt> rollups = [
            SELECT Roll_up__c, Parent__c
              FROM Custom_Files_Related_Rollups__mdt
             WHERE MasterLabel = :masterApiName
        ];

        if (!rollups.isEmpty()) {
            // 1) gather child record IDs
            Set<Id> childIds = new Set<Id>();
            for (Custom_Files_Related_Rollups__mdt cfg : rollups) {
                String childObject  = cfg.Roll_up__c;
                String lookupField  = cfg.Parent__c;
                for (SObject child : Database.query(
                    'SELECT Id FROM ' + childObject +
                    ' WHERE ' + lookupField + ' = :recordId'
                )) {
                    childIds.add((Id)child.get('Id'));
                }
            }

            if (!childIds.isEmpty()) {
                // 2) fetch their ContentDocumentLinks
                List<ContentDocumentLink> childLinks = [
                    SELECT Id, ContentDocumentId, LinkedEntityId, Visibility
                      FROM ContentDocumentLink
                     WHERE LinkedEntityId IN :childIds
                     WITH SECURITY_ENFORCED
                ];

                // 3) query the matching ContentDocuments
                Set<Id> childDocIds = new Set<Id>();
                for (ContentDocumentLink cdl : childLinks) {
                    childDocIds.add(cdl.ContentDocumentId);
                }

                List<ContentDocument> childDocs;
                if (isOpportunity) {
                    childDocs = [
                        SELECT
                          Id, Title, ContentSize, Description, FileExtension, FileType,
                          LatestPublishedVersionId,
                          LatestPublishedVersion.Dropbox_Url__c,
                          LatestPublishedVersion.Dropbox_Sync_Status__c,
                          ContentDocument.CreatedBy.FirstName,
                          ContentDocument.CreatedBy.LastName,
                          ContentModifiedDate,
                          (SELECT DistributionPublicUrl, ContentDownloadUrl FROM ContentDistributions)
                        FROM ContentDocument
                       WHERE Id IN :childDocIds
                         AND FileExtension != 'snote'
                       WITH SECURITY_ENFORCED
                       ORDER BY ContentModifiedDate DESC
                       LIMIT :limitClause
                    ];
                } else {
                    childDocs = [
                        SELECT
                          Id, Title, ContentSize, Description, FileExtension, FileType,
                          LatestPublishedVersionId,
                          LatestPublishedVersion.Dropbox_Url__c,
                          LatestPublishedVersion.Dropbox_Sync_Status__c,
                          ContentDocument.CreatedBy.FirstName,
                          ContentDocument.CreatedBy.LastName,
                          ContentModifiedDate
                        FROM ContentDocument
                       WHERE Id IN :childDocIds
                         AND FileExtension != 'snote'
                       WITH SECURITY_ENFORCED
                       ORDER BY ContentModifiedDate DESC
                       LIMIT :limitClause
                    ];
                }

                Map<Id, ContentDocument> childDocMap = new Map<Id, ContentDocument>();
                for (ContentDocument doc : childDocs) {
                    childDocMap.put(doc.Id, doc);
                }

                for (ContentDocumentLink cdl : childLinks) {
                    ContentDocument doc = childDocMap.get(cdl.ContentDocumentId);
                    if (doc != null) {
                        ContentDocumentWrapper wrapper = new ContentDocumentWrapper();
                        wrapper.contentDocument     = doc;
                        wrapper.contentDocumentLink = cdl;
                        result.add(wrapper);
                    }
                }
            }
        }

    } catch (Exception e) {
        System.debug('Exception occurred: ' + e.getMessage() + ' -- ' + e.getStackTraceString());
        throw new AuraHandledException(e.getMessage() + ' -- ' + e.getStackTraceString());
    }

    return result;
}

    
    public class ContentDocumentWrapper {
        @AuraEnabled public ContentDocument contentDocument { get; set; }
        @AuraEnabled public ContentDocumentLink contentDocumentLink { get; set; }
    }

}