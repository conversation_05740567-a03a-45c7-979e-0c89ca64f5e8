@SuppressWarnings('PMD')
global without sharing class FlinksAsyncCallout implements Schedulable, Database.AllowsCallouts {

    private static final String CLASS_NAME = 'FlinksAsyncCallout';
    private static final List<String> LOG_TAGS = new List<String>{CLASS_NAME};

    global String reqId;

    global FlinksAsyncCallout() {
        DebugLogUtil.entry(CLASS_NAME + '.constructor (default)', null, LOG_TAGS);
        DebugLogUtil.exit(CLASS_NAME + '.constructor (default)', LOG_TAGS);
    }

    global FlinksAsyncCallout(String reqId) {
        DebugLogUtil.entry(CLASS_NAME + '.constructor (with reqId)', new Map<String, Object>{'reqId' => reqId}, LOG_TAGS);
        this.reqId = reqId;
        DebugLogUtil.exit(CLASS_NAME + '.constructor (with reqId)', LOG_TAGS);
    }

    global void execute(SchedulableContext sc) {
        Map<String, Object> params = new Map<String, Object>{
            'SchedulableContext' => sc,
            'TriggerId' => sc.getTriggerId(),
            'reqId' => this.reqId
        };
        DebugLogUtil.entry(CLASS_NAME + '.execute', params, LOG_TAGS);

        try {
            DebugLogUtil.info('Calling Flinks for reqId: ' + this.reqId, LOG_TAGS);
            callFlinks(this.reqId);

            DebugLogUtil.info('Aborting Schedulable job with TriggerId: ' + sc.getTriggerId(), LOG_TAGS);
            System.abortJob(sc.getTriggerId()); // Abort to prevent rescheduling by system if it was a one-off scheduled job
        } catch (Exception e) {
            DebugLogUtil.error('Exception in Schedulable execute method.', e, new List<Object>{this.reqId}, LOG_TAGS);
            // Optionally, rethrow or handle to allow Salesforce to retry if appropriate for schedulable errors
        }
        DebugLogUtil.exit(CLASS_NAME + '.execute', LOG_TAGS);
    }

    @future(callout=true)
    global static void callFlinks(String reqIdParam) {
        Map<String, Object> params = new Map<String, Object>{'reqId' => reqIdParam};
        DebugLogUtil.entry(CLASS_NAME + '.callFlinks (future)', params, LOG_TAGS);
        try {
            Flinks_Configuration__c fc = Flinks_Configuration__c.getInstance();
            if (fc == null) {
                DebugLogUtil.error('Flinks_Configuration__c custom setting not found.', LOG_TAGS);
                // Consider how to handle this - perhaps throw an exception to indicate a critical config error
                throw new CalloutException('Flinks_Configuration__c not found.');
            }
            DebugLogUtil.info('Flinks configuration loaded. Base_Url__c: ' + fc.Base_Url__c, LOG_TAGS);

            HttpRequest req = new HttpRequest();
            req.setHeader('Content-Type', 'application/json');
            String endpoint = fc.Base_Url__c + fc.Customer_Id__c + '/BankingServices/GetAccountsDetailAsync/' + reqIdParam;
            req.setEndpoint(endpoint);
            req.setMethod('GET');
            req.setTimeout(120000);

            DebugLogUtil.info('Preparing Flinks callout. Endpoint: ' + endpoint + ', Method: GET, Timeout: 120000ms',
                new List<Object>{reqIdParam}, LOG_TAGS);

            Http http = new Http();
            HTTPResponse res;

            DebugLogUtil.info('Sending HTTP request to Flinks for reqId: ' + reqIdParam, LOG_TAGS);
            res = http.send(req);
            DebugLogUtil.info('Received HTTP response from Flinks. Status Code: ' + res.getStatusCode() + ', reqId: ' + reqIdParam, LOG_TAGS);

            // Existing logging - can be kept for its specific purpose
            UIT_Utility.LogFlinksCallout(null, res.getBody(), null, 'GetAccountsDetailAsync', reqIdParam, res.getStatusCode(), null, false);

            DebugLogUtil.info('Calling parseResponse for reqId: ' + reqIdParam, LOG_TAGS);
            FlinksAsyncCallout.parseResponse(res);

        } catch (Exception e) {
            DebugLogUtil.error('Exception in callFlinks future method for reqId: ' + reqIdParam, e, LOG_TAGS);
            // Existing exception logging
            UIT_Utility.LogException(UserInfo.getUserId(), e, 'FlinksAsyncCallout.callFlinks');
        }
        DebugLogUtil.exit(CLASS_NAME + '.callFlinks (future)', LOG_TAGS);
    }

    global static void parseResponse(HTTPResponse res) {
        Map<String, Object> params = new Map<String, Object>{
            'statusCode' => res.getStatusCode()
            // Not logging full body here by default to avoid large log entries / PII, but can be added if needed
        };
        DebugLogUtil.entry(CLASS_NAME + '.parseResponse', params, LOG_TAGS);

        try {
            DebugLogUtil.info('Response Body Snippet (first 500 chars): ' + (res.getBody() != null ? res.getBody().left(500) : 'null'), LOG_TAGS);
            DebugLogUtil.info('Response Status Code: ' + res.getStatusCode(), LOG_TAGS);

            if (res.getStatusCode() == 200) {
                DebugLogUtil.info('Status Code 200: Processing successful response.', LOG_TAGS);
                Map<String, Object> mp_StrObj = (Map<String, Object>) JSON.deserializeUntyped(res.getBody());
                DebugLogUtil.info('Successfully deserialized 200 response body.', LOG_TAGS);
                parseSuccessResponse(mp_StrObj);

            } else if (res.getStatusCode() == 202 && !System.isBatch()) { // Added check for isBatch as per original logic
                DebugLogUtil.info('Status Code 202: Request accepted, processing asynchronously. Rescheduling.', LOG_TAGS);
                Datetime scheduleTime = Datetime.now().addSeconds(10);
                String hour = String.valueOf(scheduleTime.hour());
                String min = String.valueOf(scheduleTime.minute());
                String ss = String.valueOf(scheduleTime.second());

                String nextFireTime = ss + ' ' + min + ' ' + hour + ' * * ?';
                DebugLogUtil.info('Calculated next fire time for rescheduling: ' + nextFireTime, LOG_TAGS);

                Map<String, Object> mp_StrObj = (Map<String, Object>) JSON.deserializeUntyped(res.getBody());
                String newReqId = (String) mp_StrObj.get('RequestId');
                if (newReqId != null) {
                    String jobName = 'FlinksAsyncCallout-' + newReqId;
                    DebugLogUtil.info('Scheduling new FlinksAsyncCallout job. Name: ' + jobName + ', RequestId: ' + newReqId, LOG_TAGS);
                    System.schedule(jobName, nextFireTime, new FlinksAsyncCallout(newReqId));
                } else {
                    DebugLogUtil.warn('RequestId not found in 202 response body, cannot reschedule.', LOG_TAGS);
                }
            } else {
                DebugLogUtil.warn('Unhandled status code: ' + res.getStatusCode() + '. Body: ' + res.getBody(), LOG_TAGS);
            }
        } catch (Exception e) {
            DebugLogUtil.error('Exception in parseResponse method.', e, LOG_TAGS);
            // Existing exception logging
            UIT_Utility.LogException(UserInfo.getUserId(), e, 'FlinksAsyncCallout.parseResponse');
        }
        DebugLogUtil.exit(CLASS_NAME + '.parseResponse', LOG_TAGS);
    }

    global static void parseSuccessResponse(Map<String, Object> resMap) {
        DebugLogUtil.entry(CLASS_NAME + '.parseSuccessResponse', null, LOG_TAGS); // Not logging full resMap to avoid PII exposure in logs
        try {
            Boolean sendEmail = false;
            String contactName = '';

            List<Bank_Account__c> bankAccsToUpsert = new List<Bank_Account__c>();
            List<Bank_Transaction__c> transactionsToUpsert = new List<Bank_Transaction__c>();

            Map<String, Object> loginMap = (Map<String, Object>) resMap.get('Login');
            String loginId = loginMap != null ? (String) loginMap.get('Id') : null;
            DebugLogUtil.info('Processing success response for LoginId: ' + loginId, LOG_TAGS);


            User loggedInUser = [SELECT Id, ContactId, Contact.Name FROM User WHERE Id = :UserInfo.getUserId()];
            Id relatedId = loggedInUser.ContactId;
            String contextUserName = UserInfo.getName();
            DebugLogUtil.info('Determining related Contact. Logged in User: ' + contextUserName + ', ContactId: ' + relatedId, LOG_TAGS);


            if (relatedId == null && loginId != null) {
                DebugLogUtil.info('Logged in user has no direct ContactId. Querying Flinks_Logs__c using LoginId: ' + loginId, LOG_TAGS);
                List<Flinks_Logs__c> flogs = [
                    SELECT Id, Contact__c, Contact__r.Name, Upload_From_Admin__c
                    FROM Flinks_Logs__c WHERE Login_Id__c = :loginId
                    ORDER BY CreatedDate DESC LIMIT 1
                ];
                if(!flogs.isEmpty()){
                    Flinks_Logs__c ce = flogs[0];
                    relatedId = ce.Contact__c;
                    sendEmail = ce.Upload_From_Admin__c;
                    contactName = ce.Contact__r.Name;
                    DebugLogUtil.info('Found Flinks_Log. Related ContactId: ' + relatedId + ', SendEmail: ' + sendEmail + ', ContactName: ' + contactName, LOG_TAGS);
                } else {
                    DebugLogUtil.warn('No Flinks_Logs__c record found for LoginId: ' + loginId, LOG_TAGS);
                }
            } else if (relatedId != null) {
                contactName = loggedInUser.Contact.Name;
                DebugLogUtil.info('Using Contact from logged in user. ContactName: ' + contactName, LOG_TAGS);
            } else {
                 DebugLogUtil.warn('Could not determine related ContactId. LoggedInUser.ContactId is null and LoginId is null or no matching Flinks Log.', LOG_TAGS);
            }

            List<Object> accounts = (List<Object>) resMap.get('Accounts');
            if (accounts != null) {
                DebugLogUtil.info('Processing ' + accounts.size() + ' accounts from response.', LOG_TAGS);
                for (Object obj : accounts) {
                    Map<String, Object> accData = (Map<String, Object>) obj;
                    String accountId = String.valueOf(accData.get('Id'));
                    DebugLogUtil.info('Processing account with Flinks Account Id: ' + accountId, LOG_TAGS);

                    // ... (rest of your Bank_Account__c mapping logic) ...
                    // For brevity, I'm not repeating all field mappings but you should keep them.
                    // Add specific logs for key data points if needed, e.g.,
                    // DebugLogUtil.info('Account Number: ' + (String)accData.get('AccountNumber'), LOG_TAGS);

                    // --- Order Number Logic ---
                    Decimal existingHighest = 1;
                    List<Bank_Account__c> existingAccounts = [SELECT Id FROM Bank_Account__c WHERE Account_Id__c = :accountId LIMIT 1];
                    Bank_Account__c existingAccount = !existingAccounts.isEmpty() ? existingAccounts[0] : null;

                    if (existingAccount != null) {
                        DebugLogUtil.info('Existing Bank_Account__c found with SF Id: ' + existingAccount.Id + ' for Flinks Account Id: ' + accountId, LOG_TAGS);
                        List<Bank_Transaction__c> existingTransactions = [
                            SELECT Order_Number__c FROM Bank_Transaction__c
                            WHERE Bank_Account__c = :existingAccount.Id
                            ORDER BY Order_Number__c DESC LIMIT 1
                        ];
                        if (!existingTransactions.isEmpty() && existingTransactions[0].Order_Number__c != null) {
                            existingHighest  = existingTransactions[0].Order_Number__c;
                            DebugLogUtil.info('Highest existing transaction Order_Number__c is ' + existingHighest , LOG_TAGS);
                        } else {
                             DebugLogUtil.info('No existing transactions found or Order_Number__c is null for account ' + existingAccount.Id, LOG_TAGS);
                        }
                    } else {
                        DebugLogUtil.info('No existing Bank_Account__c found for Flinks Account Id: ' + accountId + '. New account will be created.', LOG_TAGS);
                    }
                    // --- End Order Number Logic ---

                    Bank_Account__c ba = new Bank_Account__c();
                    // (Your Bank_Account__c field mapping continues here)
                    ba.Name = (String)accData.get('AccountNumber');
                    ba.Account_Id__c = accountId;
                    Map<String,Object> BalanceMap = (Map<String,Object>)JSON.deserializeUntyped(JSON.serialize(accData.get('Balance')));
                    Map<String,Object> HolderMap = (Map<String,Object>)JSON.deserializeUntyped(JSON.serialize(accData.get('Holder')));
                    Map<String,Object> HolderAddMap = (HolderMap != null && HolderMap.get('Address') != null) ? (Map<String,Object>)JSON.deserializeUntyped(JSON.serialize(HolderMap.get('Address'))) : new Map<String,Object>();

                    ba.Available_Balance__c = (BalanceMap != null && BalanceMap.get('Available') != null) ? (Decimal)BalanceMap.get('Available') : null;
                    ba.Current_Balance__c = (BalanceMap != null && BalanceMap.get('Current') != null) ? (Decimal)BalanceMap.get('Current') : null;
                    ba.Currency__c = (String)accData.get('Currency');
                    ba.Holder_City__c = (String)HolderAddMap.get('City');
                    ba.Holder_Country__c = (String)HolderAddMap.get('Country');
                    ba.Holder_Email__c = (HolderMap != null) ? (String)HolderMap.get('Email') : null;
                    ba.Holder_Name__c = (HolderMap != null) ? (String)HolderMap.get('Name') : null;
                    ba.Holder_Phone__c = (HolderMap != null) ? (String)HolderMap.get('PhoneNumber') : null;
                    ba.Holder_PoBox__c = (String)HolderAddMap.get('POBox');
                    ba.Holder_PostalCode__c = (String)HolderAddMap.get('PostalCode');
                    ba.Holder_Province__c = (String)HolderAddMap.get('Province');
                    ba.Holder_Street__c = (String)HolderAddMap.get('CivicAddress');
                    ba.Institution_Name__c = (String)resMap.get('Institution');
                    ba.Institution_Id__c = String.valueOf(resMap.get('InstitutionId'));
                    ba.Institution_Number__c = (String)accData.get('InstitutionNumber');
                    ba.Login_Id__c = loginId;
                    ba.Request_Id__c = (String)resMap.get('RequestId');
                    ba.Transit_Number__c = (String)accData.get('TransitNumber');
                    ba.Type__c = (String)accData.get('Type');
                    ba.contact__c = relatedId;
                    ba.Is_Active__c = true;
                    ba.Document_Upload_Institution__c = (String)resMap.get('DocumentUploadInstitution');
                    ba.Statement_Uploaded__c = ba.Document_Upload_Institution__c != null;
                    ba.Institution_Name__c = ba.Document_Upload_Institution__c != null ? ba.Document_Upload_Institution__c : ba.Institution_Name__c;
                    ba.Authorize_Async_Request_Id__c = null;
                    ba.Connection_Error_Code__c = null;
                    ba.Connection_Error_Message__c = null;
                    ba.Is_Get_Details_Running__c = false;

                    bankAccsToUpsert.add(ba);
                    DebugLogUtil.info('Prepared Bank_Account__c for upsert: ' + ba.Account_Id__c, LOG_TAGS);

                    List<Object> accTransactions = (List<Object>) accData.get('Transactions');
                    if (accTransactions != null) {
                        Integer newCount = accTransactions.size();
                        Decimal startNum = existingHighest + newCount;
                        Integer idx = 0;
                        DebugLogUtil.info('Processing ' + accTransactions.size() + ' transactions for account ' + accountId, LOG_TAGS);
                        for (Object transactionObj : accTransactions) {
                            Map<String, Object> transactionMap = (Map<String, Object>) transactionObj;
                            Bank_Transaction__c transactionRec = new Bank_Transaction__c();
                            // (Your Bank_Transaction__c field mapping)
                            transactionRec.Balance__c = transactionMap.get('Balance') != null ? (Decimal)transactionMap.get('Balance') : null;
                            transactionRec.Credit__c = transactionMap.get('Credit') != null ? (Decimal)transactionMap.get('Credit') : null; // Note: Original code used 'Balance' for Credit and Debit, verify if this is correct.
                            transactionRec.Debit__c = transactionMap.get('Debit') != null ? (Decimal)transactionMap.get('Debit') : null;   // Assuming there are actual Credit/Debit fields. If not, adjust.
                            transactionRec.Category__c = (String)transactionMap.get('Category');
                            transactionRec.Code__c = (String)transactionMap.get('Code');
                            transactionRec.Description__c = (String)transactionMap.get('Description');
                            transactionRec.Sub_Category__c = (String)transactionMap.get('SubCategory');
                            transactionRec.Transaction_Id__c = (String)transactionMap.get('Id');
                            Object dateValue = transactionMap.get('Date');
                            if(dateValue instanceOf String && String.isNotBlank((String)dateValue)){
                                transactionRec.Transaction_Date__c = Date.valueOf((String)dateValue);
                            }
                            transactionRec.Bank_Account__r = new Bank_Account__c(Account_Id__c = accountId); // Relate by external ID
                            transactionRec.Order_Number__c = startNum - idx;
                            //orderNumber++;
                            transactionsToUpsert.add(transactionRec);
                            idx++;
                        }
                         DebugLogUtil.info('Added ' + accTransactions.size() + ' transactions for account ' + accountId + ' to upsert list.', LOG_TAGS);
                    }
                }
            } else {
                DebugLogUtil.info('No accounts found in the response payload.', LOG_TAGS);
            }


            if (resMap.get('DocumentUploadInstitution') != null) {
                String requestIdForDetails = (String) resMap.get('RequestId');
                DebugLogUtil.info('DocumentUploadInstitution found. Calling FlinksController.getAccountDetails for RequestId: ' + requestIdForDetails, LOG_TAGS);
                FlinksController.getAccountDetailsFuture(requestIdForDetails);
            }

            if (!bankAccsToUpsert.isEmpty()) {
                DebugLogUtil.info('Upserting ' + bankAccsToUpsert.size() + ' Bank_Account__c records.', LOG_TAGS);
                Database.UpsertResult[] baResults = Database.upsert(bankAccsToUpsert, Bank_Account__c.Account_Id__c, false);
                // Optionally log upsert results
                 Integer successCount = 0;
                for(Database.UpsertResult ur : baResults){ if(ur.isSuccess()){ successCount++;}}
                DebugLogUtil.info(successCount + ' Bank Accounts upserted successfully out of ' + baResults.size(), LOG_TAGS);
            } else {
                DebugLogUtil.info('No Bank_Account__c records to upsert.', LOG_TAGS);
            }

            if (!transactionsToUpsert.isEmpty()) {
                DebugLogUtil.info('Upserting ' + transactionsToUpsert.size() + ' Bank_Transaction__c records.', LOG_TAGS);
                Database.UpsertResult[] txResults = Database.upsert(transactionsToUpsert, Bank_Transaction__c.Transaction_Id__c, false);
                Integer successCount = 0;
                for(Database.UpsertResult ur : txResults){ if(ur.isSuccess()){ successCount++;}}
                DebugLogUtil.info(successCount + ' Bank Transactions upserted successfully out of ' + txResults.size(), LOG_TAGS);


                if (loginId != null && resMap.get('RequestId') != null) {
                    DebugLogUtil.info('Enqueueing CategorizationProcessor job for LoginId: ' + loginId + ', RequestId: ' + (String) resMap.get('RequestId'), LOG_TAGS);
                    System.enqueueJob(new CategorizationProcessor(loginId, (String) resMap.get('RequestId')));
                } else {
                    DebugLogUtil.warn('Cannot enqueue CategorizationProcessor: LoginId or RequestId is null.',
                        new List<Object>{loginId, (String)resMap.get('RequestId')}, LOG_TAGS);
                }
            } else {
                DebugLogUtil.info('No Bank_Transaction__c records to upsert.', LOG_TAGS);
            }

            if (sendEmail) {
                Flinks_Configuration__c fc = Flinks_Configuration__c.getInstance();
                if (fc != null && String.isNotBlank(fc.email__c)) {
                    List<Messaging.SingleEmailMessage> emails = new List<Messaging.SingleEmailMessage>();
                    Messaging.SingleEmailMessage message = new Messaging.SingleEmailMessage();
                    message.setHtmlBody('Hi,<br/>Uploaded Statement Processed for ' + contactName);
                    message.setSubject('Uploaded Statement Processed');
                    message.setToAddresses(fc.email__c.split(','));
                    emails.add(message);

                    DebugLogUtil.info('Sending email notification for processed statement to: ' + fc.email__c, LOG_TAGS);
                    Messaging.SendEmailResult[] r = Messaging.sendEmail(emails);
                    for(Messaging.SendEmailResult er : r){
                        if(er.isSuccess()){
                             DebugLogUtil.info('Email sent successfully.', LOG_TAGS);
                        } else {
                            DebugLogUtil.warn('Email sending failed: ' + er.getErrors()[0].getMessage(), LOG_TAGS);
                        }
                    }
                } else {
                    DebugLogUtil.warn('SendEmail is true, but Flinks configuration for email is missing or email address is blank.', LOG_TAGS);
                }
            }

        } catch (Exception e) {
            DebugLogUtil.error('Exception in parseSuccessResponse method.', e, LOG_TAGS);
            // Existing exception logging
            UIT_Utility.LogException(UserInfo.getUserId(), e, 'FlinksAsyncCallout.parseSuccessResponse');
        }
        DebugLogUtil.exit(CLASS_NAME + '.parseSuccessResponse', LOG_TAGS);
        DebugLogUtil.saveLogs();
    }

    /**
     * Invocable method to process Flinks response from a string request body
     * This method can be called from Flow
     *
     * @param requests List of FlinksProcessRequest containing the response body string
     * @return List of FlinksProcessResponse with processing results
     */
    @InvocableMethod(label='Process Flinks Response' description='Processes Flinks API response from string and calls parseSuccessResponse method' category='Flinks')
    global static List<FlinksProcessResponse> processResponseFromString(List<FlinksProcessRequest> requests) {
        final String METHOD_NAME = CLASS_NAME + '.processResponseFromString';
        DebugLogUtil.entry(METHOD_NAME, new Map<String, Object>{'requestCount' => requests?.size()}, LOG_TAGS);

        List<FlinksProcessResponse> responses = new List<FlinksProcessResponse>();
        String actualResponseBody; // To store the response body, whether from input or file

        try {
            for (FlinksProcessRequest request : requests) {
                FlinksProcessResponse response = new FlinksProcessResponse();
                response.requestId = request.requestId;
                actualResponseBody = request.responseBody; // Default to using the provided body

                try {
                    DebugLogUtil.info('Processing request with RequestId: ' + request.requestId, LOG_TAGS);

                    // Check if the response body is a placeholder for an attachment
                    if (String.isNotBlank(request.requestId) && 
                        'Response body is too large and has been stored as an Attachment. Please check the Files related to this Flinks Log record.'.equals(request.responseBody)) {
                        
                        DebugLogUtil.info('Response body indicates data is in an attachment for RequestId: ' + request.requestId + '. Attempting to fetch attachment.', LOG_TAGS);
                        List<Attachment> attachments = [
                            SELECT Body 
                            FROM Attachment 
                            WHERE ParentId = :request.requestId 
                            AND Name = :('FlinksResponse_' + request.requestId + '.txt') 
                            LIMIT 1
                        ];

                        if (!attachments.isEmpty() && attachments[0].Body != null) {
                            actualResponseBody = attachments[0].Body.toString();
                            DebugLogUtil.info('Successfully fetched and decoded attachment body for RequestId: ' + request.requestId, LOG_TAGS);
                        } else {
                            response.isSuccess = false;
                            response.errorMessage = 'Response indicated attachment, but attachment was not found or was empty for Flinks Log ID: ' + request.requestId;
                            DebugLogUtil.warn(response.errorMessage, LOG_TAGS);
                            responses.add(response);
                            continue; // Skip to the next request
                        }
                    }

                    if (String.isBlank(actualResponseBody)) {
                        response.isSuccess = false;
                        response.errorMessage = 'Response body is null or empty after potential attachment retrieval';
                        DebugLogUtil.warn(response.errorMessage + ' for RequestId: ' + request.requestId, LOG_TAGS);
                    } else {
                        // Parse the JSON response body
                        Map<String, Object> responseMap = (Map<String, Object>) JSON.deserializeUntyped(actualResponseBody);
                        DebugLogUtil.info('Successfully parsed JSON response for RequestId: ' + request.requestId, LOG_TAGS);

                        // Call the existing parseSuccessResponse method
                        parseSuccessResponse(responseMap);

                        response.isSuccess = true;
                        response.message = 'Response processed successfully';
                        DebugLogUtil.info('Successfully processed response for RequestId: ' + request.requestId, LOG_TAGS);
                    }
                } catch (JSONException jsonEx) {
                    response.isSuccess = false;
                    response.errorMessage = 'Invalid JSON format: ' + jsonEx.getMessage();
                    DebugLogUtil.error('JSON parsing error for RequestId: ' + request.requestId, jsonEx, LOG_TAGS);
                } catch (Exception ex) {
                    response.isSuccess = false;
                    response.errorMessage = 'Processing error: ' + ex.getMessage();
                    DebugLogUtil.error('Processing error for RequestId: ' + request.requestId, ex, LOG_TAGS);
                }

                responses.add(response);
            }
        } catch (Exception e) {
            DebugLogUtil.error('Exception in processResponseFromString method', e, LOG_TAGS);
            FlinksProcessResponse errorResponse = new FlinksProcessResponse();
            errorResponse.isSuccess = false;
            errorResponse.errorMessage = 'Unexpected error: ' + e.getMessage();
            responses.add(errorResponse);
        }

        DebugLogUtil.exit(METHOD_NAME, LOG_TAGS);
        DebugLogUtil.saveLogs();
        return responses;
    }

    /**
     * Input wrapper class for the invocable method
     */
    global class FlinksProcessRequest {
        @InvocableVariable(label='Response Body' description='The JSON response body string from Flinks API' required=true)
        global String responseBody;

        @InvocableVariable(label='Request ID' description='Flinks Log Id for tracking' required=false)
        global String requestId;

    }

    /**
     * Output wrapper class for the invocable method
     */
    global class FlinksProcessResponse {
        @InvocableVariable(label='Success' description='Indicates if the processing was successful')
        global Boolean isSuccess;

        @InvocableVariable(label='Message' description='Success or error message')
        global String message;

        @InvocableVariable(label='Error Message' description='Detailed error message if processing failed')
        global String errorMessage;

        @InvocableVariable(label='Request ID' description='Flinks Log Id for tracking')
        global String requestId;
    }
}