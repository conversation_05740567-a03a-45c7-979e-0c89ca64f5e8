@isTest
public class UIT_UtilityTest {
        
    @IsTest
    static void testLogException() {
     
        try {
            Integer i = 1 / 0;
        } catch (Exception ex) {
            Test.startTest();
            UIT_Utility.LogException(null, ex,'UTilityTest');
            UIT_Utility.LogExceptionStub(null, ex,'UTilityTest');
            Test.stopTest();
        }

        // Perform assertions
        Custom_Exception__c[] ceList = [SELECT Id FROM Custom_Exception__c];
        System.assertEquals(1, ceList.size(), 'Custom_Exception__c record should be inserted');
    }
    
    @IsTest
    static void testLogException1() {			
        try {
            Integer i = 1 / 0;
        } catch (Exception ex) {
            Test.startTest();
            UIT_Utility.LogException(null, ex);
            Test.stopTest();
        }

        // Perform assertions
        Custom_Exception__c[] ceList = [SELECT Id FROM Custom_Exception__c];
        System.assertEquals(1, ceList.size(), 'Custom_Exception__c record should be inserted');
    }
    
    @IsTest
    static void testLogRequest() {
           Account testAccount = new Account(
            Name = 'Test Account'
        );
        insert testAccount;
        
        // Create a test Opportunity
        Opportunity testOpportunity = new Opportunity(
            Name = 'Test Opportunity',
            AccountId = testAccount.Id,
            CloseDate = Date.today().addDays(30),
            StageName = 'Prospecting',
            Amount = 10000,
            Type = 'New Business',
            LeadSource = 'Web',
            Probability = 50,
            CampaignId = null,
            Supporting_Docs__c = 'http://example.com/docs',
            Signed_App__c = Date.today(),
            Description = 'Test Opportunity Description'
        );
        insert testOpportunity;

        // Create a test Project related to the Opportunity
        Project__c testProject = new Project__c(
            Name = 'Test Project',
            Loan_Opportunity__c = testOpportunity.Id,
            Email_for_Servicing_Updates__c = '<EMAIL>'
        );
        insert testProject;

        String testJsonReq = '{"key": "value"}';

        Test.startTest();
        UIT_Utility.LogRequestAndResponse(testProject.Id, testJsonReq, testJsonReq);
        Test.stopTest();

    }

    @IsTest
    static void testLogRequestAndResponse() {
 
        String testJsonReq = '{"key": "value"}';
        List<ID>  testIds = new List<ID>(); 
        testIds.add(null);
    
        Test.startTest();
        UIT_Utility.LogRequestAndResponse(testIds, 200, testJsonReq, testJsonReq);
        Custom_Exception__c ce = UIT_Utility.LogRequestAndResponseStub('', 200, testJsonReq, testJsonReq);
        Test.stopTest();

    }

    @IsTest
    static void testLogRequestAndResponse1() {
  
        String testJsonReq = '{"key": "value"}';
        
        Test.startTest();
        UIT_Utility.LogRequestAndResponse('', 200, testJsonReq,testJsonReq);
        Test.stopTest();

    }
    
    
    
    @IsTest
    static void testLogFlinksCallout_LongJsonRes() {
        // Detach current user's ContactId to force use of contactId parameter
        User currentUser = [SELECT Id, ContactId FROM User WHERE Id = :UserInfo.getUserId()];
        currentUser.ContactId = null;
        update currentUser;
        
        // Create a fallback Contact to pass as contactId parameter
        Contact fallbackContact = new Contact(LastName = 'FallbackContact');
        insert fallbackContact;
        
        // Build a long JSON string (>131010 characters)
        String chunk = '';
        for (Integer i = 0; i < 1000; i++) {
            chunk += 'x';
        }
        String longJson = '';
        for (Integer i = 0; i < 132; i++) {
            longJson += chunk;
        }
        // Now longJson.length() ≈132,000, which is >131010
        
        String jsonReq = '{"long":"req"}';
        String loginId = 'longLogin';
        String flinksFunction = 'LongFunc';
        String reqId = 'longReq';
        Integer statusCode = 500;
        
        Test.startTest();
            UIT_Utility.LogFlinksCallout(
                jsonReq, 
                longJson, 
                loginId, 
                flinksFunction, 
                reqId, 
                statusCode, 
                /*contactId=*/ fallbackContact.Id, 
                /*isAdmin=*/ true
            );
        Test.stopTest();
        
        // Verify Flinks_Logs__c record with placeholder response was created
        List<Flinks_Logs__c> flogs = [
            SELECT Id, Contact__c, User__c, Login_Id__c, Flinks_Function__c, 
                   Request_Json__c, Response_Json__c, Status_Code__c, Is_Response_Body_In_Files__c 
            FROM Flinks_Logs__c
            WHERE Request_Id__c = :reqId
        ];
        System.assertEquals(1, flogs.size(), 'One Flinks_Logs__c record should be inserted');
        Flinks_Logs__c fl = flogs[0];
        System.assertEquals(fallbackContact.Id, fl.Contact__c, 
                            'Contact__c should match fallbackContact');
        System.assertEquals(loginId, fl.Login_Id__c, 'Login_Id__c should match');
        System.assertEquals(flinksFunction, fl.Flinks_Function__c, 'Flinks_Function__c should match');
        System.assertEquals(jsonReq, fl.Request_Json__c, 'Request_Json__c should match');
        System.assertEquals(true, fl.Is_Response_Body_In_Files__c, 
                            'Is_Response_Body_In_Files__c should be true for long response');
        System.assert(fl.Response_Json__c.startsWith('Response body is too large'),
                      'Response_Json__c should contain the placeholder text');
        
        // Verify an Attachment was created under the Flinks_Logs__c record
        String expectedFileName = 'FlinksResponse_' + fl.Id + '.txt';
List<Attachment> atts = [
    SELECT Id, ParentId, Name, Body
    FROM Attachment
    WHERE ParentId = :fl.Id
      AND Name      = :expectedFileName
];
        System.assertEquals(1, atts.size(), 
                            'One Attachment should be created for the long JSON response');
        Attachment att = atts[0];
        // Check first 100 characters to confirm it matches
        String decoded = att.Body.toString();
        System.assertEquals(longJson.substring(0, 100), decoded.substring(0, 100), 
                            'Attachment body should match the first 100 chars of original JSON');
    }
    
}