@SuppressWarnings('PMD.AvoidDeeplyNestedIfStmts, PMD.CognitiveComplexity, PMD.ExcessiveMethodLength, PMD.NcssMethodCount, PMD.StdCyclomaticComplexity, PMD')
public without sharing class cashFlowSessionController {

    private static final String CLASS_NAME = 'cashFlowSessionController';
    private static final List<String> LOG_TAGS = new List<String>{CLASS_NAME, 'CashflowProcessing'};
    private static final List<String> DML_TAGS = new List<String>{CLASS_NAME, 'DML_OPERATION'};
    private static final List<String> API_TAGS = new List<String>{CLASS_NAME, 'API_CALL'};
    private static final List<String> PARSING_TAGS = new List<String>{CLASS_NAME, 'PARSING'};

    // Wrapper class to hold prepared SObjects for DML
    private class PreparedObjects {
        List<Cashflow_Line_Item_Child__c> childrenToUpsert = new List<Cashflow_Line_Item_Child__c>();
        Map<String, Cashflow_Line_Item__c> parentsToUpsert = new Map<String, Cashflow_Line_Item__c>();
        List<Cashflow_Line_Item_Junction__c> junctionsToInsert = new List<Cashflow_Line_Item_Junction__c>();
    }

    @AuraEnabled
    public static Map<String, Object> createRecordsFromDynamoData(String accountId, String dynamoJsonString, String projectId) {
        final String METHOD_NAME = 'createRecordsFromDynamoData';
        DebugLogUtil.entry(CLASS_NAME + '.' + METHOD_NAME, null, LOG_TAGS);

        Map<String, Object> result = new Map<String, Object>{'success' => false};
        if (String.isBlank(dynamoJsonString) || String.isBlank(projectId)) {
            result.put('errorMessage', 'JSON data and ProjectId are required.');
            return result;
        }

        Map<String, Object> dynamoData;
        try {
            dynamoData = (Map<String, Object>) JSON.deserializeUntyped(dynamoJsonString);
        } catch (Exception e) {
            result.put('errorMessage', 'Error parsing input JSON: ' + e.getMessage());
            return result;
        }

        SavePoint sp = Database.setSavepoint();
        try {
            // Step 1: Create and Insert Cashflow__c to get its ID FIRST.
            Cashflow__c cashflow = createCashflowRecord(dynamoData, projectId);
            insert cashflow;
            result.put('cashflowId', cashflow.Id);

            // Step 2: Prepare line items, PASSING IN the new cashflow ID to ensure uniqueness.
            PreparedObjects preparedData = prepareLineItems(dynamoData, cashflow.Id);
            
            if (preparedData.childrenToUpsert.isEmpty()) {
                result.put('success', true);
                result.put('message', 'Cashflow created, but no line items were found in the JSON.');
                return result;
            }

            // Step 3: Perform bulk DML.
            upsert preparedData.childrenToUpsert Cashflow_Line_Item_Child__c.Fields.External_Node_Id__c;
            upsert preparedData.parentsToUpsert.values() Cashflow_Line_Item__c.Fields.External_Week_Id__c;
            insert preparedData.junctionsToInsert;
            
            result.put('success', true);

        } catch (Exception e) {
            Database.rollback(sp);
            DebugLogUtil.error(METHOD_NAME + ': Error during record creation.', e, DML_TAGS);
            result.put('success', false);
            result.put('errorMessage', 'Error processing data: ' + e.getMessage() + ' Stacktrace: ' + e.getStackTraceString());
        }
        return result;
    }

    /**
     * @description Prepares a Cashflow__c SObject from the 'details' map with hardcoded mappings.
     */
    private static Cashflow__c createCashflowRecord(Map<String, Object> dynamoData, String projectId) {
        Map<String, Object> details = getMapValue(dynamoData, 'details');
        
        Cashflow__c cashflow = new Cashflow__c(
            Project__c = projectId,
            Name = getStringValue(details, 'projectName'),
            Total_Project_Value__c = getDecimalValue(details, 'totalValue'),
            Estimated_Direct_Payroll__c = getDecimalValue(details, 'totalEstimatedDirectPayroll'),
            Estimated_Subcontract_Labor__c = getDecimalValue(details, 'totalEstimatedSubcontractLabor'),
            Estimated_Material_Cost__c = getDecimalValue(details, 'totalEstimatedMaterial'),
            Estimated_Equipment_Rental_Cost__c = getDecimalValue(details, 'totalEstimatedEquipmentRental'),
            Estimated_Bond_Premium_Cost__c = getDecimalValue(details, 'totalEstimatedBondPremium'),
            Estimated_Misc_Expense_Cost__c = getDecimalValue(details, 'totalEstimatedMiscExpenses'),
            Retainage_Percentage__c = getDecimalValue(details, 'retainagePercent'),
            Project_Duration_Weeks__c = getDecimalValue(details, 'projectLengthWeeks'),
            Pay_App_Frequency__c = getStringValue(details, 'payAppFrequency'),
            Payment_Delay_Days__c = getDecimalValue(details, 'paymentDelayDays'),
            General_Contractor_Contract_Owner__c = getStringValue(details, 'contractorName'),
            Estimated_Margin_Percent__c = getDecimalValue(details, 'marginPercent'),
            Initial_Amount_Received__c = getDecimalValue(details, 'moneyReceivedToDate'),
            Joint_Checks_Required__c = getBooleanValue(details, 'jointChecks'),
            Project_Start_Date__c = getDateValue(details, 'startDate'),
            Forecast_Start_Date__c = getDateValue(details, 'computed.weekStart'),
            Forecast_End_Date__c = getDateValue(details, 'computed.weekdEnd'),
            Status__c = 'Active',
            Forecast_Date__c = getDateValue(details, 'computed.weekStart')
        );
        
        return cashflow;
    }
    
    /**
     * @description Main processing method to parse income/expenses and prepare SObject lists in memory.
     */
    private static PreparedObjects prepareLineItems(Map<String, Object> dynamoData, Id cashflowId) {
        PreparedObjects po = new PreparedObjects();

        // Process Income Items
        Map<String, Object> incomeMap = getMapValue(dynamoData, 'income');
        if (incomeMap != null && incomeMap.get('lineItems') instanceof List<Object>) {
            for (Object itemObj : (List<Object>)incomeMap.get('lineItems')) {
                Map<String, Object> itemData = (Map<String, Object>)itemObj;
                Cashflow_Line_Item_Child__c child = createChildItem(itemData, 'Project Revenue', itemData, null);
                if(child != null) {
                    po.childrenToUpsert.add(child);
                    aggregateAndLink(child, po.parentsToUpsert, po.junctionsToInsert, cashflowId);
                }
            }
        }

        // Process Expense Items
        if (dynamoData.get('expenses') instanceof List<Object>) {
            for (Object expenseGroupObj : (List<Object>)dynamoData.get('expenses')) {
                Map<String, Object> groupData = (Map<String, Object>)expenseGroupObj;
                if (groupData.get('lineItems') instanceof List<Object>) {
                    for (Object itemObj : (List<Object>)groupData.get('lineItems')) {
                        Cashflow_Line_Item_Child__c child = createChildItem((Map<String, Object>)itemObj, 'Project Cost', null, groupData);
                        if(child != null) {
                             po.childrenToUpsert.add(child);
                             aggregateAndLink(child, po.parentsToUpsert, po.junctionsToInsert, cashflowId);
                        }
                    }
                }
            }
        }
        return po;
    }

    /**
     * @description Creates a single Cashflow_Line_Item_Child__c object in memory from a JSON node.
     */
     private static Cashflow_Line_Item_Child__c createChildItem(Map<String, Object> itemData, String type, Map<String, Object> groupData) {
        String nodeId = getStringValue(itemData, 'id');
        if(String.isBlank(nodeId)) return null;

        Cashflow_Line_Item_Child__c child = new Cashflow_Line_Item_Child__c(
            External_Node_Id__c = nodeId,
            Type__c = type,
            Line_Item_Category__c = getStringValue(groupData, 'category'),
            Planned_Amount__c = getDecimalValue(itemData, 'amount'),
            Week_Start_Date__c = getDateValue(itemData, 'week'),
            Planned_Date__c = getDateValue(itemData, 'weekDue')
        );
        
        // Denormalize fields from the parent expense group onto the child
        if(type == 'Project Cost'){
            child.Parent_Expense_Id__c = getStringValue(groupData, 'id');
            child.Expense_Schedule__c = getStringValue(groupData, 'schedule');
            child.Payment_Frequency__c = getStringValue(groupData, 'paymentFrequency');
            child.Parent_Expense_Total_Amount__c = getDecimalValue(groupData, 'amount');
            child.Payment_Terms__c = getStringValue(groupData, 'terms');

            String category = child.Line_Item_Category__c;
            if(category == 'subContractors'){
                 child.Subcontractor_Name__c = getStringValue(groupData, 'name');
            } else if (category == 'materialOrders'){
                 child.Material_Order_Name__c = getStringValue(groupData, 'name');
            }
        }

        return child;
    }
    
    /**
     * @description Aggregates a child's amount into a parent record and creates the junction object.
     */
    private static void aggregateAndLink(Cashflow_Line_Item_Child__c child, Map<String, Cashflow_Line_Item__c> parents, List<Cashflow_Line_Item_Junction__c> junctions, Id cashflowId) {
        if(child.Week_Start_Date__c == null || String.isBlank(child.Line_Item_Category__c)) return;

        String externalWeekId = cashflowId + '_' + child.Line_Item_Category__c + '_' + child.Week_Start_Date__c.format();

        Cashflow_Line_Item__c parent = parents.get(externalWeekId);
        if (parent == null) {
            parent = new Cashflow_Line_Item__c(
                Cashflow__c = cashflowId,
                External_Week_Id__c = externalWeekId, 
                Line_Item_Category__c = child.Line_Item_Category__c, 
                Week_Start_Date__c = child.Week_Start_Date__c, 
                Type__c = child.Type__c, 
                Planned_Amount__c = child.Planned_Amount__c
            );
            parents.put(externalWeekId, parent);
        } else {
            parent.Planned_Amount__c = (parent.Planned_Amount__c == null ? 0 : parent.Planned_Amount__c) + (child.Planned_Amount__c == null ? 0 : child.Planned_Amount__c);
        }

        Cashflow_Line_Item_Junction__c junction = new Cashflow_Line_Item_Junction__c();
        junction.putSObject('Cashflow_Line_Item__r', new Cashflow_Line_Item__c(External_Week_Id__c = externalWeekId));
        junction.putSObject('Cashflow_Line_Item_Child__r', new Cashflow_Line_Item_Child__c(External_Node_Id__c = child.External_Node_Id__c));
        junctions.add(junction);
    }
    
    // --- GENERIC HELPER METHODS ---
    private static Map<String, Object> getMapValue(Map<String, Object> data, String key) {
        if (data != null && data.get(key) instanceof Map<String, Object>) { return (Map<String, Object>) data.get(key); }
        return null;
    }

    private static String getStringValue(Map<String, Object> data, String key) {
        return (data != null && data.containsKey(key) && data.get(key) != null) ? String.valueOf(data.get(key)) : null;
    }
    
    private static Boolean getBooleanValue(Map<String, Object> data, String key) {
        String val = getStringValue(data, key);
        return 'yes'.equalsIgnoreCase(val) || 'true'.equalsIgnoreCase(val);
    }

    private static Decimal getDecimalValue(Map<String, Object> data, String key) {
        Object val = (data != null) ? data.get(key) : null;
        if (val instanceof Decimal || val instanceof Double || val instanceof Integer) { return (Decimal)val; }
        try { return String.isBlank(String.valueOf(val)) ? null : Decimal.valueOf(String.valueOf(val)); }
        catch (Exception e) { return null; }
    }

    private static Date getDateValue(Map<String, Object> data, String key) {
        Object val = (data != null) ? data.get(key) : null;
        try { return String.isBlank(String.valueOf(val)) ? null : Date.valueOf(String.valueOf(val)); }
        catch (Exception e) { return null; }
    }
    
    
    // --- OTHER PUBLIC METHODS (UNCHANGED) ---
    @AuraEnabled(cacheable=true)
    public static List<Cash_Flow_Session__c> getRecords(){
        return [SELECT Id, Name, UpdatedAt__c, LatestStep__c, DeletedAt__c, AccountId__c, UserId__c, Configuration__c,
                CurrentStep__c, SessionId__c, VideosProgress__c, CompletedAt__c, CreatedAt__c, Processed_JSON__c
                FROM Cash_Flow_Session__c Order by CreatedAt__c DESC LIMIT 50000];
    }
    
    @AuraEnabled
    public static Map<String,Object> shareSessionWithApex(String endpointUrl) {
       final String METHOD_NAME = 'shareSessionWithApex';
        Map<String, Object> params = new Map<String, Object>{'endpointUrl' => endpointUrl};
        DebugLogUtil.entry(CLASS_NAME + '.' + METHOD_NAME, params, API_TAGS);
        
        Map<String,Object> returnMap = new Map<String, Object>();
        try {
            HttpRequest req = new HttpRequest();
            req.setEndpoint(endpointUrl);
            req.setMethod('POST');
            Http http = new Http();
            
            HttpResponse res = http.send(req);
            
            returnMap.put('statusCode', res.getStatusCode());
            returnMap.put('status', res.getStatus());
            returnMap.put('body', res.getBody());
        } catch (Exception e) {
            DebugLogUtil.error(METHOD_NAME + ': Exception during callout.', e, API_TAGS);
            throw new AuraHandledException('Error in sharing session: ' + e.getMessage());
        }
        return returnMap;
    }
}