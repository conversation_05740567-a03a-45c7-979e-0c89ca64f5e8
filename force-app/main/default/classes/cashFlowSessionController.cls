@SuppressWarnings('PMD.AvoidDeeplyNestedIfStmts, PMD.CognitiveComplexity, PMD.ExcessiveMethodLength, PMD.NcssMethodCount, PMD.StdCyclomaticComplexity, PMD')
public without sharing class cashFlowSessionController {
    
    private static final String CLASS_NAME = 'cashFlowSessionController';
    private static final List<String> LOG_TAGS = new List<String>{CLASS_NAME, 'CashflowProcessing'};
        private static final List<String> DML_TAGS = new List<String>{CLASS_NAME, 'DML_OPERATION'};
            private static final List<String> API_TAGS = new List<String>{CLASS_NAME, 'API_CALL'};
                private static final List<String> PARSING_TAGS = new List<String>{CLASS_NAME, 'PARSING'};
                    
                    @AuraEnabled(cacheable=true)
                    public static List<Cash_Flow_Session__c> getRecords(){
                        final String METHOD_NAME = 'getRecords';
                        DebugLogUtil.entry(CLASS_NAME + '.' + METHOD_NAME, null, LOG_TAGS);
                        
                        List<Cash_Flow_Session__c> cfsrecords = new List<Cash_Flow_Session__c>();
                        try {
                            cfsrecords = [
                                SELECT Id, Name, UpdatedAt__c, LatestStep__c, DeletedAt__c, AccountId__c, UserId__c, Configuration__c,
                                CurrentStep__c, SessionId__c, VideosProgress__c, CompletedAt__c, CreatedAt__c
                                FROM Cash_Flow_Session__c
                                LIMIT 50000
                            ];
                            DebugLogUtil.info(METHOD_NAME + ': Fetched ' + cfsrecords.size() + ' Cash_Flow_Session__c records.', LOG_TAGS);
                        } catch (Exception e) {
                            DebugLogUtil.error(METHOD_NAME + ': Error fetching Cash_Flow_Session__c records.', e, LOG_TAGS);
                            throw new AuraHandledException('Error fetching records: ' + e.getMessage());
                        }
                        
                        DebugLogUtil.exit(CLASS_NAME + '.' + METHOD_NAME, LOG_TAGS);
                        return cfsrecords;
                    }
    
    @AuraEnabled
    public static Map<String,Object> shareSessionWithApex(String endpointUrl) {
        final String METHOD_NAME = 'shareSessionWithApex';
        Map<String, Object> params = new Map<String, Object>{'endpointUrl' => endpointUrl};
            DebugLogUtil.entry(CLASS_NAME + '.' + METHOD_NAME, params, API_TAGS);
        
        Map<String,Object> returnMap = new Map<String, Object>();
        try {
            HttpRequest req = new HttpRequest();
            req.setEndpoint(endpointUrl);
            req.setMethod('POST');
            Http http = new Http();
            
            DebugLogUtil.info(METHOD_NAME + ': Sending POST request to endpoint: ' + endpointUrl, API_TAGS);
            HttpResponse res = http.send(req);
            
            returnMap.put('statusCode', res.getStatusCode());
            returnMap.put('status', res.getStatus());
            returnMap.put('body', res.getBody());
            
            DebugLogUtil.info(METHOD_NAME + ': Response received. Status Code: ' + res.getStatusCode() + ', Status: ' + res.getStatus(), API_TAGS);
            
            if (res.getStatusCode() >= 200 && res.getStatusCode() < 300) {
                DebugLogUtil.info(METHOD_NAME + ': Callout successful.', API_TAGS);
            } else {
                DebugLogUtil.error(METHOD_NAME + ': Error from callout. Status Code: ' + res.getStatusCode() + ', Body: ' + res.getBody(), API_TAGS);
            }
            DebugLogUtil.exit(CLASS_NAME + '.' + METHOD_NAME, API_TAGS);
            return returnMap;
        } catch (Exception e) {
            DebugLogUtil.error(METHOD_NAME + ': Exception during callout.', e, API_TAGS);
            DebugLogUtil.exit(CLASS_NAME + '.' + METHOD_NAME, API_TAGS);
            throw new AuraHandledException('Error in sharing session: ' + e.getMessage());
        }
    }
    
    public class MappingRule {
        @AuraEnabled public String dynamoDbId { get; set; }
        @AuraEnabled public String salesforceObject { get; set; }
        @AuraEnabled public String salesforceFieldApiName { get; set; }
        @AuraEnabled public String fieldName { get; set; }
        @AuraEnabled public String intakeFormPageSection { get; set; }
        @AuraEnabled public String intakeType { get; set; }
        
        
        public String getDynamoSection() {
            if (String.isNotBlank(dynamoDbId) && dynamoDbId.contains(' - ')) {
                return dynamoDbId.substringBefore(' - ').trim();
            }
            return null;
        }
        public String getDynamoFieldKey() {
            if (String.isNotBlank(dynamoDbId) && dynamoDbId.contains(' - ')) {
                String key = dynamoDbId.substringAfter(' - ').trim();
                if (key.contains('[]')) {
                    key = key.substringBefore('[').trim();
                }
                return key;
            }
            return dynamoDbId;
        }
    }
    
    private static final String CASHFLOW_SOBJECT_TYPE = 'Cashflow__c';
    private static final String CASHFLOW_LINE_ITEM_SOBJECT_TYPE = 'Cashflow_Line_Item_Child__c';
    private static final String PROJECT_SOBJECT_TYPE = 'Project__c';
    
    @AuraEnabled
    public static Map<String, Object> createRecordsFromDynamoData(String accountId, String dynamoJsonString, String projectId) {
        final String METHOD_NAME = 'createRecordsFromDynamoData';
        Map<String, Object> logParams = new Map<String, Object>{
            'accountId' => accountId,
                'dynamoJsonStringLength' => dynamoJsonString?.length()
                };
                    DebugLogUtil.entry(CLASS_NAME + '.' + METHOD_NAME, logParams, LOG_TAGS);
        
        Map<String, Object> result = new Map<String, Object>{'success' => false};
            Map<String, Object> dynamoData;
        List<MappingRule> mappingRules;
        
        try {
            DebugLogUtil.info(METHOD_NAME + ': Starting parsing of input JSON data and fetching mapping rules.', PARSING_TAGS);
            if (String.isNotBlank(dynamoJsonString)) {
                dynamoData = DynamoHelper.parse(dynamoJsonString);
                DebugLogUtil.info(METHOD_NAME + ': DynamoDB JSON parsed successfully using DynamoHelper.', PARSING_TAGS);
            } else {
                dynamoData = new Map<String, Object>();
                DebugLogUtil.warn(METHOD_NAME + ': DynamoDB JSON string is blank. Initialized empty data map.', PARSING_TAGS);
            }
            
            // Get mapping rules from MFDynamoMappingService
            String mappingRulesJsonFromService = MFDynamoMappingService.getMappingRulesAsJson();
            if (String.isNotBlank(mappingRulesJsonFromService)) {
                mappingRules = (List<MappingRule>) JSON.deserialize(mappingRulesJsonFromService, List<MappingRule>.class);
                DebugLogUtil.info(METHOD_NAME + ': Mapping JSON from service parsed successfully. Number of rules: ' + (mappingRules != null ? mappingRules.size() : 0), PARSING_TAGS);
            } else {
                mappingRules = new List<MappingRule>();
                DebugLogUtil.warn(METHOD_NAME + ': Mapping JSON string from service is blank. Initialized empty mapping rules list.', PARSING_TAGS);
            }
            
        } catch (AuraHandledException e) {
            DebugLogUtil.error(METHOD_NAME + ': AuraHandledException during input parsing or mapping retrieval.', e, PARSING_TAGS);
            result.put('errorMessage', 'Error processing input data: ' + e.getMessage());
            DebugLogUtil.exit(CLASS_NAME + '.' + METHOD_NAME, LOG_TAGS);
            return result;
        } catch (Exception e) {
            DebugLogUtil.error(METHOD_NAME + ': Unexpected exception during input parsing or mapping retrieval.', e, PARSING_TAGS);
            result.put('errorMessage', 'An unexpected error occurred during input parsing/mapping retrieval: ' + e.getMessage());
            DebugLogUtil.exit(CLASS_NAME + '.' + METHOD_NAME, LOG_TAGS);
            return result;
        }
        
        if (String.isBlank(accountId)) {
            DebugLogUtil.warn(METHOD_NAME + ': AccountId is required but was blank.', LOG_TAGS);
            result.put('errorMessage', 'AccountId is required.');
            DebugLogUtil.exit(CLASS_NAME + '.' + METHOD_NAME, LOG_TAGS);
            return result;
        }
        
        if (mappingRules == null || mappingRules.isEmpty()) {
            DebugLogUtil.warn(METHOD_NAME + ': No mapping rules found or retrieved from service. Cannot proceed with record creation.', LOG_TAGS);
            result.put('errorMessage', 'No mapping rules are configured or retrieved. Please contact your administrator.');
            DebugLogUtil.exit(CLASS_NAME + '.' + METHOD_NAME, LOG_TAGS);
            return result;
        }
        
        Map<String, List<MappingRule>> rulesBySfObject = new Map<String, List<MappingRule>>();
        for (MappingRule rule : mappingRules) {
            if (rule.salesforceObject != null) {
                if (!rulesBySfObject.containsKey(rule.salesforceObject)) {
                    rulesBySfObject.put(rule.salesforceObject, new List<MappingRule>());
                }
                rulesBySfObject.get(rule.salesforceObject).add(rule);
            }
        }
        DebugLogUtil.info(METHOD_NAME + ': Mapping rules grouped by Salesforce object.', LOG_TAGS);
        
        SavePoint sp = Database.setSavepoint();
        DebugLogUtil.info(METHOD_NAME + ': Savepoint set.', DML_TAGS);
        
        try {
            String projectNameFromDynamo = getPathValueAsString(dynamoData, 'details.projectName');
            DebugLogUtil.info(METHOD_NAME + ': Project name from dynamoData "details.projectName": ' + projectNameFromDynamo, LOG_TAGS);
            
            List<MappingRule> projectMappingRules = rulesBySfObject.get(PROJECT_SOBJECT_TYPE);
            if (projectMappingRules == null) projectMappingRules = new List<MappingRule>();
            
            if (String.isBlank(projectNameFromDynamo)) {
                for(MappingRule rule : projectMappingRules) {
                    if(rule.salesforceFieldApiName != null && rule.salesforceFieldApiName.equalsIgnoreCase('Name') &&
                       rule.salesforceObject != null && rule.salesforceObject.equalsIgnoreCase(PROJECT_SOBJECT_TYPE) &&
                       rule.dynamoDbId != null) {
                           String[] parts = rule.dynamoDbId.split(' - ');
                           if(parts.size() == 2) {
                               projectNameFromDynamo = getPathValueAsString(dynamoData, parts[0].trim() + '.' + parts[1].trim());
                               DebugLogUtil.info(METHOD_NAME + ': Project name derived from mapping rule: ' + projectNameFromDynamo, LOG_TAGS);
                           }
                           break;
                       }
                }
            }
            if (String.isBlank(projectNameFromDynamo)) {
                String companyName = getPathValueAsString(dynamoData, 'details.userCompany');
                projectNameFromDynamo = String.isNotBlank(companyName) ? companyName + ' Project' : 'Default Project';
                DebugLogUtil.info(METHOD_NAME + ': Project name derived from company name or default: ' + projectNameFromDynamo, LOG_TAGS);
                
                String cashflowNameCandidate = getPathValueAsString(dynamoData, 'details.projectName');
                if(String.isNotBlank(cashflowNameCandidate)) {
                    projectNameFromDynamo = cashflowNameCandidate;
                    DebugLogUtil.info(METHOD_NAME + ': Project name updated from cashflowNameCandidate: ' + projectNameFromDynamo, LOG_TAGS);
                }
            }
            
            if (String.isBlank(projectNameFromDynamo)) {
                DebugLogUtil.warn(METHOD_NAME + ': Project Name could not be determined from DynamoDB data for Project creation.', LOG_TAGS);
                result.put('errorMessage', 'Project Name could not be determined from DynamoDB data for Project creation.');
                DebugLogUtil.exit(CLASS_NAME + '.' + METHOD_NAME, LOG_TAGS);
                return result;
            }
            
            DebugLogUtil.info(METHOD_NAME + ': Attempting to get or create Project with name: ' + projectNameFromDynamo + ' for AccountId: ' + accountId, LOG_TAGS);
            // Project__c project = getOrCreateProject(accountId, projectNameFromDynamo, dynamoData, projectMappingRules);
            Project__c project = new Project__c(Id = projectId);
            
            if (project == null || project.Id == null) {
                DebugLogUtil.error(METHOD_NAME + ': Failed to get or create Project.', LOG_TAGS);
                result.put('errorMessage', 'Failed to get or create Project.');
                Database.rollback(sp);
                DebugLogUtil.warn(METHOD_NAME + ': Rolled back savepoint due to project creation failure.', DML_TAGS);
                DebugLogUtil.exit(CLASS_NAME + '.' + METHOD_NAME, LOG_TAGS);
                return result;
            }
            result.put('projectId', project.Id);
            result.put('projectName',projectNameFromDynamo);
            DebugLogUtil.info(METHOD_NAME + ': Project successfully retrieved/created. ProjectId: ' + project.Id, LOG_TAGS);
            
            Cashflow__c cashflow = new Cashflow__c();
            cashflow.Project__c = project.Id;
            
            Map<String, Object> detailsMap = getMapValue(dynamoData, 'details');
            Map<String, Object> typeMap = getMapValue(dynamoData, 'type');
            Map<String, Object> metaMap = getMapValue(dynamoData, 'meta');
            
            if (rulesBySfObject.containsKey(CASHFLOW_SOBJECT_TYPE)) {
                DebugLogUtil.info(METHOD_NAME + ': Applying mapping rules for Cashflow__c.', LOG_TAGS);
                for (MappingRule rule : rulesBySfObject.get(CASHFLOW_SOBJECT_TYPE)) {
                    String dynamoSectionKey = rule.getDynamoSection();
                    String dynamoFieldKey = rule.getDynamoFieldKey();
                    Object rawValue = null;
                    
                    if (dynamoSectionKey != null && dynamoFieldKey != null) {
                        Map<String, Object> sourceMap = null;
                        if (dynamoSectionKey.equalsIgnoreCase('details')) {sourceMap = detailsMap;}
                        else if (dynamoSectionKey.equalsIgnoreCase('type')) {sourceMap = typeMap;}
                        else if (dynamoSectionKey.equalsIgnoreCase('meta')) {sourceMap = metaMap;}
                        else {sourceMap = getMapValue(dynamoData, dynamoSectionKey);}
                        
                        if (sourceMap != null) {rawValue = sourceMap.get(dynamoFieldKey);}
                    }
                    if (rawValue != null) {
                        assignFieldValue(cashflow, rule.salesforceFieldApiName, rawValue);
                    }
                }
            }
            DebugLogUtil.info(METHOD_NAME + ': Inserting Cashflow__c record.', DML_TAGS);
            insert cashflow;
            result.put('cashflowId', cashflow.Id);
            result.put('cashflowName', cashflow.Name);
            DebugLogUtil.info(METHOD_NAME + ': Cashflow__c inserted successfully. CashflowId: ' + cashflow.Id, DML_TAGS);
            
            List<Cashflow_Line_Item_Child__c> childItemsToInsert = new List<Cashflow_Line_Item_Child__c>();
            //List<Cashflow_Line_Item__c>  parentItemsToInsert = new List<Cashflow_Line_Item__c>();
            List<Cashflow_Line_Item__c> lineItemsToInsert = new List<Cashflow_Line_Item__c>();
            List<MappingRule> cliRules = rulesBySfObject.get(CASHFLOW_LINE_ITEM_SOBJECT_TYPE);
            if (cliRules == null) {cliRules = new List<MappingRule>();}
            
            DebugLogUtil.info(METHOD_NAME + ': Processing payment lists and complex items for Cashflow Line Items.', LOG_TAGS);
            processPaymentList(dynamoData, 'payAppSchedule', 'payAppAllocations', 'Invoice Submission', cashflow.Id, cliRules, childItemsToInsert, null, null);
            processPaymentList(dynamoData, 'invoiceSchedule', 'invoiceSchedule', 'Invoice Submission', cashflow.Id, cliRules, childItemsToInsert, null, null);
            processPaymentList(dynamoData, 'payroll', 'payrollAllocations', 'Direct Payroll', cashflow.Id, cliRules, childItemsToInsert, null, null);
            
            processComplexItemList(dynamoData, 'subContractors', 'subContractors', 'Subcontractor Labor', cashflow.Id, cliRules, childItemsToInsert);
            processComplexItemList(dynamoData, 'materials', 'materialOrders', 'Material', cashflow.Id, cliRules, childItemsToInsert);
            processComplexItemList(dynamoData, 'equipment', 'equipmentVendors', 'Equipment Rental', cashflow.Id, cliRules, childItemsToInsert);
            
            Map<String, Object> bondPremiumMap = getMapValue(dynamoData, 'bondPremium');
            if (bondPremiumMap != null && 'yes'.equalsIgnoreCase(String.valueOf(bondPremiumMap.get('hasBondPremium')))) {
                DebugLogUtil.info(METHOD_NAME + ': Processing bond premium.', LOG_TAGS);
                Cashflow_Line_Item__c bondCli = new Cashflow_Line_Item__c(Cashflow__c = cashflow.Id, Line_Item_Category__c = 'Bond Premium');
                assignFieldValue(bondCli, 'Planned_Amount__c', bondPremiumMap.get('totalAmount'));
                assignFieldValue(bondCli, 'Planned_Date__c', bondPremiumMap.get('dueDate'));
                applyGeneralCLIRules(bondCli, bondPremiumMap, 'bondPremium', cliRules, false);
                lineItemsToInsert.add(bondCli);
            }
            
            processComplexItemList(dynamoData, 'miscExpenses', 'miscExpenses', 'Miscellaneous Expense', cashflow.Id, cliRules, childItemsToInsert);
            
            if (!childItemsToInsert.isEmpty()) {
                DebugLogUtil.info(METHOD_NAME + ': Inserting ' + childItemsToInsert.size() + ' Cashflow_Line_Item__c records.', DML_TAGS);
                insert childItemsToInsert;
                List<Id> lineItemIds = new List<Id>();
                for(Cashflow_Line_Item_child__c li : childItemsToInsert) {
                    lineItemIds.add(li.Id);
                }
                result.put('ChildItemIds', lineItemIds);
                DebugLogUtil.info(METHOD_NAME + ': ' + lineItemIds.size() + ' Cashflow_Line_Item_child__c records inserted successfully.', DML_TAGS);
            } else {
                DebugLogUtil.info(METHOD_NAME + ': No Cashflow_Line_Item__c records to insert.', LOG_TAGS);
            }
            
            // First, build a nested map:  category → (weekStartDate → totalAmount)
            Map<String, Map<Date, Decimal>> sumByCategoryAndWeek = new Map<String, Map<Date, Decimal>>();
            
            for (Cashflow_Line_Item_Child__c child : childItemsToInsert) {
                Date   week = child.Week_Start_Date__c;
                String cat  = child.Line_Item_Category__c;
                Decimal amt = child.Planned_Amount__c == null ? 0 : child.Planned_Amount__c;
                
                if (week == null || cat == null) {
                    // skip if missing data
                    continue;
                }
                
                if (!sumByCategoryAndWeek.containsKey(cat)) {
                    sumByCategoryAndWeek.put(cat, new Map<Date, Decimal>());
                }
                Map<Date, Decimal> weekMap = sumByCategoryAndWeek.get(cat);
                
                Decimal existing = weekMap.containsKey(week) && weekMap.get(week) != null
                    ? weekMap.get(week)
                    : 0;
                weekMap.put(week, existing + amt);
            }
            
            
            for (String cat : sumByCategoryAndWeek.keySet()) {
                for (Date week : sumByCategoryAndWeek.get(cat).keySet()) {
                    Decimal totalAmt = sumByCategoryAndWeek.get(cat).get(week);
                    
                    Cashflow_Line_Item__c summary = new Cashflow_Line_Item__c(
                        Cashflow__c            = cashflow.Id,
                        Line_Item_Category__c  = cat,
                        Week_Start_Date__c     = week,
                        Planned_Amount__c      = totalAmt
                    );
                    lineItemsToInsert.add(summary);
                }
            }
            
            // Insert all parent line items
            if (!lineItemsToInsert.isEmpty()) {
                DebugLogUtil.info(METHOD_NAME + ': Inserting ' + lineItemsToInsert.size() + ' parent line items.', DML_TAGS);
                insert lineItemsToInsert;
            }
            
            Map<String, Id> parentIdByCategory = new Map<String, Id>();
            for (Cashflow_Line_Item__c parent : lineItemsToInsert) {
                // assumes Line_Item_Category__c is non-null on your parent records
                parentIdByCategory.put(parent.Line_Item_Category__c, parent.Id);
            }
            
            
            // Assign the correct parent to each child record
            
            List<Cashflow_Line_Item_Child__c> childrenToUpdate = new List<Cashflow_Line_Item_Child__c>();
            for (Cashflow_Line_Item_Child__c child : childItemsToInsert) {
                String cat = child.Line_Item_Category__c;
                Id parentId = parentIdByCategory.get(cat);
                if (parentId != null) {
                    child.Cashflow_Line_Item__c = parentId;
                    childrenToUpdate.add(child);
                } else {
                    // optional: log or handle cases where you couldn’t find a summary for this category
                    DebugLogUtil.warn(
                        METHOD_NAME + ': No parent summary found for child category → ' + cat,
                        DML_TAGS
                    );
                }
            }
            
            
            // Bulk update your children
            
            if (!childrenToUpdate.isEmpty()) {
                DebugLogUtil.info(
                    METHOD_NAME + ': Linking ' + childrenToUpdate.size() + ' children to their parents.',
                    DML_TAGS
                );
                update childrenToUpdate;
            }
            
            List<Id> parentIds = new List<Id>();
            for (Cashflow_Line_Item__c p : lineItemsToInsert) {parentIds.add(p.Id);}
            result.put('lineItemIds', parentIds);
            result.put('success', true);
            DebugLogUtil.info(METHOD_NAME + ': Record creation process completed successfully.', LOG_TAGS);
            
        } catch (Exception e) {
            Database.rollback(sp);
            DebugLogUtil.error(METHOD_NAME + ': Error during record creation process. Transaction rolled back.', e, DML_TAGS);
            result.put('success', false);
            result.put('errorMessage', 'Error processing data: ' + e.getMessage() + ' Stacktrace: ' + e.getStackTraceString());
        }
        DebugLogUtil.exit(CLASS_NAME + '.' + METHOD_NAME, LOG_TAGS);
        DebugLogUtil.saveLogs();
        return result;
    }
    
    @TestVisible
    private static Project__c getOrCreateProject(String accountId, String projectName, Map<String, Object> dynamoData, List<MappingRule> projectRules) {
        final String METHOD_NAME = 'getOrCreateProject';
        Map<String, Object> params = new Map<String, Object>{'accountId' => accountId, 'projectName' => projectName, 'projectRulesCount' => projectRules?.size()};
            DebugLogUtil.entry(CLASS_NAME + '.' + METHOD_NAME, params, LOG_TAGS);
        
        String effectiveProjectName = projectName;
        if (String.isBlank(effectiveProjectName)) {
            effectiveProjectName = 'Default Project for Account ' + accountId;
            DebugLogUtil.warn(METHOD_NAME + ': Project name was blank, using default: ' + effectiveProjectName, LOG_TAGS);
        }
        
        List<Project__c> existingProjects;
        try {
            // Corrected query to include AccountId for uniqueness
            existingProjects = [
                SELECT Id, Name, Account_Name__c
                FROM Project__c
                WHERE Name = :effectiveProjectName AND Account_Name__c = :accountId
                LIMIT 1
            ];
        } catch (Exception e) {
            DebugLogUtil.error(METHOD_NAME + ': SOQL Exception while querying for existing projects with name: ' + effectiveProjectName + ' and AccountId: ' + accountId, e, LOG_TAGS);
            DebugLogUtil.exit(CLASS_NAME + '.' + METHOD_NAME, LOG_TAGS);
            return null;
        }
        
        Project__c project;
        if (!existingProjects.isEmpty()) {
            project = existingProjects[0];
            DebugLogUtil.info(METHOD_NAME + ': Existing project found. Id: ' + project.Id + ', Name: ' + project.Name, LOG_TAGS);
        } else {
            DebugLogUtil.info(METHOD_NAME + ': No existing project found with name: ' + effectiveProjectName + ' for AccountId: ' + accountId + '. Creating new project.', LOG_TAGS);
            project = new Project__c(
                Account_Name__c = accountId, // Assuming Account_Name__c is the API name of the lookup to Account
                Name = effectiveProjectName
            );
            
            if (projectRules != null) {
                Map<String, Object> detailsMap = getMapValue(dynamoData, 'details');
                DebugLogUtil.info(METHOD_NAME + ': Applying ' + projectRules.size() + ' project mapping rules.', LOG_TAGS);
                for (MappingRule rule : projectRules) {
                    if (rule.salesforceObject != null && rule.salesforceObject.equalsIgnoreCase(PROJECT_SOBJECT_TYPE) &&
                        rule.salesforceFieldApiName != null && !rule.salesforceFieldApiName.equalsIgnoreCase('Name') &&
                        !rule.salesforceFieldApiName.equalsIgnoreCase('Account_Name__c')) { 
                            String dynamoSectionKey = rule.getDynamoSection();
                            String dynamoFieldKey = rule.getDynamoFieldKey();
                            Object rawValue = null;
                            if (dynamoSectionKey != null && dynamoFieldKey != null) {
                                Map<String, Object> sourceMap = null;
                                if (dynamoSectionKey.equalsIgnoreCase('details')) {sourceMap = detailsMap;}
                                else {sourceMap = getMapValue(dynamoData, dynamoSectionKey);}
                                
                                if (sourceMap != null){ rawValue = sourceMap.get(dynamoFieldKey);}
                            }
                            if (rawValue != null) {
                                assignFieldValue(project, rule.salesforceFieldApiName, rawValue);
                            }
                        }
                }
            }
            try {
                DebugLogUtil.info(METHOD_NAME + ': Inserting new project: ' + effectiveProjectName, DML_TAGS);
                insert project;
                DebugLogUtil.info(METHOD_NAME + ': New project inserted successfully. Id: ' + project.Id, DML_TAGS);
            } catch (Exception e) {
                DebugLogUtil.error(METHOD_NAME + ': Error creating project: ' + effectiveProjectName + ' for account ' + accountId, e, DML_TAGS);
                DebugLogUtil.exit(CLASS_NAME + '.' + METHOD_NAME, LOG_TAGS);
                return null;
            }
        }
        DebugLogUtil.exit(CLASS_NAME + '.' + METHOD_NAME, LOG_TAGS);
        return project;
    }
    
    private static void processPaymentList(Map<String, Object> sourceDataMap, String sectionName, String listKeyName, String category, Id cashflowId, List<MappingRule> cliRules, List<Cashflow_Line_Item_child__c> lineItemsToInsert, Map<String, Object> parentItemData, String parentItemDynamoSectionPath) {
        final String METHOD_NAME = 'processPaymentList';
        Map<String, Object> logParams = new Map<String, Object>{
            'sectionName' => sectionName, 'listKeyName' => listKeyName, 'category' => category, 'cashflowId' => cashflowId,
                'cliRulesCount' => cliRules?.size(), 'hasParentItemData' => (parentItemData != null), 'parentItemDynamoSectionPath' => parentItemDynamoSectionPath
                };
                    DebugLogUtil.entry(CLASS_NAME + '.' + METHOD_NAME, logParams, LOG_TAGS);
        
        Map<String, Object> sectionMapToUse = sourceDataMap; // Default to sourceDataMap if sectionName is not a direct key (e.g. for itemData.payments)
        if (sourceDataMap.containsKey(sectionName) && sourceDataMap.get(sectionName) instanceof Map<String,Object>) {
            sectionMapToUse = getMapValue(sourceDataMap, sectionName);
        }
        
        
        if (sectionMapToUse == null || sectionMapToUse.get(listKeyName) == null) {
            DebugLogUtil.warn(METHOD_NAME + ': Section map or list for key "' + listKeyName + '" in section "' + sectionName + '" is null. SourceMap keys: ' + String.valueOf(sourceDataMap.keySet()) + '. Skipping.', LOG_TAGS);
            DebugLogUtil.exit(CLASS_NAME + '.' + METHOD_NAME, LOG_TAGS);
            return;
        }
        
        Object paymentsListObj = sectionMapToUse.get(listKeyName);
        if (paymentsListObj == null || !(paymentsListObj instanceof List<Object>)) {
            DebugLogUtil.warn(METHOD_NAME + ': Payments list object for key "' + listKeyName + '" is null or not a list. Skipping.', LOG_TAGS);
            DebugLogUtil.exit(CLASS_NAME + '.' + METHOD_NAME, LOG_TAGS);
            return;
        }
        
        List<Object> actualPaymentsList = (List<Object>) paymentsListObj;
        DebugLogUtil.info(METHOD_NAME + ': Processing ' + actualPaymentsList.size() + ' payment items from section: ' + sectionName + ', list: ' + listKeyName, LOG_TAGS);
        
        for (Object paymentObj : actualPaymentsList) {
            if(!(paymentObj instanceof Map<String, Object>)) {
                DebugLogUtil.warn(METHOD_NAME + ': Payment object is not a Map. Skipping. Object: ' + String.valueOf(paymentObj), LOG_TAGS);
                continue;
            }
            Map<String, Object> paymentData = (Map<String, Object>) paymentObj;
            Cashflow_Line_Item_child__c cli = new Cashflow_Line_Item_child__c(Line_Item_Category__c = category);
            
            assignFieldValue(cli, 'Week_Start_Date__c', paymentData.get('date'));
            assignFieldValue(cli, 'Planned_Amount__c', paymentData.get('amount'));
            
            String baseDynamoPathForRules = sectionName + ' - ' + listKeyName + '[]';
            if(parentItemDynamoSectionPath != null){ // This means 'payments' is a sub-list
                // The parentItemDynamoSectionPath is like "subContractors - subContractors[]"
                // The listKeyName for payments is "payments"
                // So base path should be "subContractors - subContractors[].payments[]"
                baseDynamoPathForRules = parentItemDynamoSectionPath + '.' + listKeyName + '[]';
            }
            
            
            for(MappingRule rule : cliRules){
                if(rule.dynamoDbId != null && rule.dynamoDbId.startsWith(baseDynamoPathForRules)){
                    String propertyNameInPayment;
                    String tempName = rule.dynamoDbId.substring(baseDynamoPathForRules.length());
                    if (tempName.startsWith('.')) { propertyNameInPayment = tempName.substring(1).trim(); }
                    else { propertyNameInPayment = tempName.trim(); }
                    
                    if(String.isNotBlank(propertyNameInPayment) && paymentData.containsKey(propertyNameInPayment)){
                        assignFieldValue(cli, rule.salesforceFieldApiName, paymentData.get(propertyNameInPayment));
                    }
                }
            }
            
            if (parentItemData != null && parentItemDynamoSectionPath != null) {
                applyGeneralCLIRules(cli, parentItemData, parentItemDynamoSectionPath, cliRules, true);
            }
            lineItemsToInsert.add(cli);
        }
        DebugLogUtil.exit(CLASS_NAME + '.' + METHOD_NAME, LOG_TAGS);
    }
    
    private static void processComplexItemList(Map<String, Object> dynamoData, String sectionName, String listKeyName, String category, Id cashflowId, List<MappingRule> cliRules, List<Cashflow_Line_Item_child__c> lineItemsToInsert) {
        final String METHOD_NAME = 'processComplexItemList';
        Map<String, Object> logParams = new Map<String, Object>{
            'sectionName' => sectionName, 'listKeyName' => listKeyName, 'category' => category, 'cashflowId' => cashflowId, 'cliRulesCount' => cliRules?.size()
                };
                    DebugLogUtil.entry(CLASS_NAME + '.' + METHOD_NAME, logParams, LOG_TAGS);
        
        Map<String, Object> sectionMap = getMapValue(dynamoData, sectionName);
        if (sectionMap == null) {
            DebugLogUtil.info(METHOD_NAME + ': Section map for "' + sectionName + '" is null. Skipping.', LOG_TAGS);
            DebugLogUtil.exit(CLASS_NAME + '.' + METHOD_NAME, LOG_TAGS);
            return;
        }
        
        boolean hasSectionFlag = false;
        String[] possibleHasKeys = new String[]{
            'has' + sectionName.substring(0,1).toUpperCase() + sectionName.substring(1),
                'has' + sectionName.toLowerCase(),
                'has' + sectionName.capitalize()
                };
                    
                    for(String hasKey : possibleHasKeys){
                        if(sectionMap.containsKey(hasKey)){
                            hasSectionFlag = 'yes'.equalsIgnoreCase(String.valueOf(sectionMap.get(hasKey)));
                            if(hasSectionFlag){ break;}
                        }
                    }
        DebugLogUtil.info(METHOD_NAME + ': Section "' + sectionName + '", hasSectionFlag: ' + hasSectionFlag, LOG_TAGS);
        
        if (!hasSectionFlag) {
            Object listObj = sectionMap.get(listKeyName);
            if (listObj != null && listObj instanceof List<Object> && !((List<Object>)listObj).isEmpty()){
                hasSectionFlag = true;
                DebugLogUtil.info(METHOD_NAME + ': Section "' + sectionName + '", hasSectionFlag set to true based on non-empty list.', LOG_TAGS);
            } else if (listObj == null && !sectionMap.containsKey(listKeyName)) {
                DebugLogUtil.info(METHOD_NAME + ': Section "' + sectionName + '", listKeyName "' + listKeyName + '" does not exist. Skipping.', LOG_TAGS);
                DebugLogUtil.exit(CLASS_NAME + '.' + METHOD_NAME, LOG_TAGS); return;
            } else if (listObj == null && sectionMap.containsKey(listKeyName)) {
                DebugLogUtil.info(METHOD_NAME + ': Section "' + sectionName + '", listKeyName "' + listKeyName + '" exists but is null. Skipping.', LOG_TAGS);
                DebugLogUtil.exit(CLASS_NAME + '.' + METHOD_NAME, LOG_TAGS); return;
            } else if (listObj != null && !(listObj instanceof List<Object>)) {
                DebugLogUtil.warn(METHOD_NAME + ': Expected a list for ' + listKeyName + ' in section ' + sectionName + ' but found: ' + String.valueOf(listObj) + '. Skipping.', LOG_TAGS);
                DebugLogUtil.exit(CLASS_NAME + '.' + METHOD_NAME, LOG_TAGS); return;
            } else if (!hasSectionFlag) {
                DebugLogUtil.info(METHOD_NAME + ': Section "' + sectionName + '", hasSectionFlag is false after all checks. Skipping.', LOG_TAGS);
                DebugLogUtil.exit(CLASS_NAME + '.' + METHOD_NAME, LOG_TAGS); return;
            }
        }
        
        Object itemsListObj = sectionMap.get(listKeyName);
        if (itemsListObj == null || !(itemsListObj instanceof List<Object>)) {
            DebugLogUtil.warn(METHOD_NAME + ': Items list object for key "' + listKeyName + '" in section "' + sectionName + '" is null or not a list. Skipping further processing for this section.', LOG_TAGS);
            DebugLogUtil.exit(CLASS_NAME + '.' + METHOD_NAME, LOG_TAGS);
            return;
        }
        List<Object> actualItemsList = (List<Object>) itemsListObj;
        DebugLogUtil.info(METHOD_NAME + ': Processing ' + actualItemsList.size() + ' complex items from section: ' + sectionName + ', list: ' + listKeyName, LOG_TAGS);
        
        String itemDynamoSectionPathForRules = sectionName + ' - ' + listKeyName + '[]';
        
        for (Object itemObj : actualItemsList) {
            if(!(itemObj instanceof Map<String,Object>)) {
                DebugLogUtil.warn(METHOD_NAME + ': Item object is not a Map. Skipping. Object: ' + String.valueOf(itemObj), LOG_TAGS);
                continue;
            }
            Map<String, Object> itemData = (Map<String, Object>) itemObj;
            String amountVariation = String.valueOf(itemData.get('amountVariation'));
            DebugLogUtil.info(METHOD_NAME + ': Processing item with amountVariation: ' + amountVariation, LOG_TAGS);
            
            Object paymentsSubListObj = itemData.get('payments');
            if ('variable'.equalsIgnoreCase(amountVariation) && paymentsSubListObj != null && paymentsSubListObj instanceof List<Object>) {
                DebugLogUtil.info(METHOD_NAME + ': Variable amount, processing payments sub-list.', LOG_TAGS);
                // When calling processPaymentList for a sub-list (e.g., payments within a materialOrder),
                // the 'sourceDataMap' is itemData, 'sectionName' for the payments list is 'payments' (key within itemData),
                // and 'listKeyName' is also 'payments'.
                processPaymentList(itemData, 'payments', 'payments', category, cashflowId, cliRules, lineItemsToInsert, itemData, itemDynamoSectionPathForRules);
            } else {
                DebugLogUtil.info(METHOD_NAME + ': Fixed amount or no payments sub-list, creating single line item.', LOG_TAGS);
                Cashflow_Line_Item_child__c cli = new Cashflow_Line_Item_child__c( Line_Item_Category__c = category);
                applyGeneralCLIRules(cli, itemData, itemDynamoSectionPathForRules, cliRules, false);
                
                if (itemData.containsKey('amount') && !itemData.containsKey('payments')) {
                    assignFieldValue(cli, 'Planned_Amount__c', itemData.get('amount'));
                }
                if (itemData.containsKey('paymentDate') && !itemData.containsKey('payments')) {
                    assignFieldValue(cli, 'Planned_Date__c', itemData.get('paymentDate'));
                }
                lineItemsToInsert.add(cli);
            }
        }
        DebugLogUtil.exit(CLASS_NAME + '.' + METHOD_NAME, LOG_TAGS);
    }
    
    private static void applyGeneralCLIRules(SObject record, Map<String, Object> itemData, String itemPathPrefixForRules, List<MappingRule> cliRules, Boolean isSubItemMappingLogic) {
        // final String METHOD_NAME = 'applyGeneralCLIRules'; // Logging removed for brevity in this frequently called method
        
        for (MappingRule rule : cliRules) {
            if(rule.salesforceObject != null && rule.salesforceObject.equalsIgnoreCase(CASHFLOW_LINE_ITEM_SOBJECT_TYPE) && rule.dynamoDbId != null){
                String propertyName = '';
                Boolean ruleMatchesItem = false;
                
                if (isSubItemMappingLogic) {
                    if (rule.dynamoDbId.startsWith(itemPathPrefixForRules) &&
                        !rule.dynamoDbId.contains(itemPathPrefixForRules + '.payments[]') && // Ensure it's not a rule for the 'payments' sub-list itself
                        !rule.dynamoDbId.contains(itemPathPrefixForRules + '.subListName[]') // A more generic check for any other sub-list
                       ) {
                           String tempName = rule.dynamoDbId.substring(itemPathPrefixForRules.length());
                           if (tempName.startsWith('.')) { propertyName = tempName.substring(1).trim(); }
                           else { propertyName = tempName.trim(); }
                           ruleMatchesItem = String.isNotBlank(propertyName);
                       }
                } else {
                    if (rule.dynamoDbId.startsWith(itemPathPrefixForRules)) {
                        String tempName = rule.dynamoDbId.substring(itemPathPrefixForRules.length());
                        if (tempName.startsWith('.')) { propertyName = tempName.substring(1).trim(); }
                        else { propertyName = tempName.trim(); }
                        
                        if(String.isNotBlank(propertyName) &&
                           !'payments'.equalsIgnoreCase(propertyName) &&
                           !('amount'.equalsIgnoreCase(propertyName) && itemData.containsKey('payments')) &&
                           !('paymentDate'.equalsIgnoreCase(propertyName) && itemData.containsKey('payments'))) {
                               ruleMatchesItem = true;
                           }
                    }
                }
                
                if (ruleMatchesItem && itemData.containsKey(propertyName)) {
                    assignFieldValue(record, rule.salesforceFieldApiName, itemData.get(propertyName));
                }
            }
        }
    }
    
    private static void assignFieldValue(SObject record, String fieldApiName, Object rawValue) {
        final String METHOD_NAME = 'assignFieldValue';
        
        if (String.isBlank(fieldApiName) || rawValue == null ) {
            return;
        }
        
        try {
            Schema.SObjectType targetType = record.getSObjectType();
            Schema.DescribeSObjectResult describeSobj = targetType.getDescribe();
            Map<String, Schema.SObjectField> fieldMap = describeSobj.fields.getMap();
            
            if (!fieldMap.containsKey(fieldApiName.toLowerCase())) {
                DebugLogUtil.warn(METHOD_NAME + ': Field ' + fieldApiName + ' not found on SObject ' + targetType + '. Value: ' + String.valueOf(rawValue), LOG_TAGS);
                return;
            }
            Schema.DescribeFieldResult fieldDescribe = fieldMap.get(fieldApiName.toLowerCase()).getDescribe();
            Schema.DisplayType sfType = fieldDescribe.getType();
            String stringValue = String.valueOf(rawValue);
            
            if (rawValue instanceof String && String.isBlank((String)rawValue) && sfType != Schema.DisplayType.STRING && sfType != Schema.DisplayType.TEXTAREA && fieldDescribe.isNillable()) {
                record.put(fieldApiName, null);
                return;
            }
            if (rawValue instanceof String && String.isBlank((String)rawValue) && !fieldDescribe.isNillable() && (sfType == Schema.DisplayType.PICKLIST || sfType == Schema.DisplayType.MULTIPICKLIST)) {
                DebugLogUtil.warn(METHOD_NAME + ': Attempted to set blank string to non-nillable picklist field ' + fieldApiName + ' on ' + targetType + '. Skipping assignment.', LOG_TAGS);
                return;
            }
            
            if (fieldApiName.equalsIgnoreCase('RecordTypeId')) {
                if (String.isNotBlank(stringValue)) {
                    List<RecordType> rts = [SELECT Id FROM RecordType WHERE SobjectType = :String.valueOf(targetType) AND DeveloperName = :stringValue LIMIT 1];
                    if (!rts.isEmpty()) {
                        record.put(fieldApiName, rts[0].Id);
                    } else {
                        DebugLogUtil.warn(METHOD_NAME + ': RecordType not found with DeveloperName: ' + stringValue + ' for SObject ' + targetType + '.', LOG_TAGS);
                    }
                }
            } else if (sfType == Schema.DisplayType.STRING || sfType == Schema.DisplayType.TEXTAREA || sfType == Schema.DisplayType.PICKLIST || sfType == Schema.DisplayType.MULTIPICKLIST || sfType == Schema.DisplayType.EMAIL || sfType == Schema.DisplayType.PHONE || sfType == Schema.DisplayType.URL || sfType == Schema.DisplayType.ID || sfType == Schema.DisplayType.COMBOBOX) {
                record.put(fieldApiName, stringValue);
            } else if (sfType == Schema.DisplayType.CURRENCY || sfType == Schema.DisplayType.DOUBLE || sfType == Schema.DisplayType.PERCENT || sfType == Schema.DisplayType.INTEGER || sfType == Schema.DisplayType.LONG) {
                String cleanedStringValue = stringValue.replaceAll('[^0-9\\.\\-]','');
                if (String.isNotBlank(cleanedStringValue)) {
                    try { record.put(fieldApiName, Decimal.valueOf(cleanedStringValue)); }
                    catch (TypeException e) { DebugLogUtil.warn(METHOD_NAME + ': Invalid number format for field ' + fieldApiName + ' on ' + targetType + '. Value: ' + stringValue + ' (cleaned: ' + cleanedStringValue + '). Error: ' + e.getMessage(), LOG_TAGS);}
                } else if (!fieldDescribe.isNillable()){
                    record.put(fieldApiName, 0);
                    DebugLogUtil.info(METHOD_NAME + ': Blank value for non-nillable numeric field ' + fieldApiName + ' on ' + targetType + '. Set to 0.', LOG_TAGS);
                } else {
                    record.put(fieldApiName, null);
                }
            } else if (sfType == Schema.DisplayType.DATE) {
                if (String.isNotBlank(stringValue)) {
                    try { record.put(fieldApiName, Date.valueOf(stringValue)); }
                    catch (TypeException e) { DebugLogUtil.warn(METHOD_NAME + ': Invalid date format for field ' + fieldApiName + ' on ' + targetType + '. Value: ' + stringValue + '. Error: ' + e.getMessage(), LOG_TAGS); }
                } else if (fieldDescribe.isNillable()) {
                    record.put(fieldApiName, null);
                }
            } else if (sfType == Schema.DisplayType.DATETIME) {
                if (String.isNotBlank(stringValue)) {
                    try { record.put(fieldApiName, Datetime.valueOf(stringValue.replace(' ', 'T'))); }
                    catch (TypeException e) { DebugLogUtil.warn(METHOD_NAME + ': Invalid datetime format for field ' + fieldApiName + ' on ' + targetType + '. Value: ' + stringValue + '. Error: ' + e.getMessage(), LOG_TAGS); }
                } else if (fieldDescribe.isNillable()) {
                    record.put(fieldApiName, null);
                }
            } else if (sfType == Schema.DisplayType.BOOLEAN) {
                if ('yes'.equalsIgnoreCase(stringValue) || 'true'.equalsIgnoreCase(stringValue) || '1'.equals(stringValue)) {record.put(fieldApiName, true);}
                else if ('no'.equalsIgnoreCase(stringValue) || 'false'.equalsIgnoreCase(stringValue) || '0'.equals(stringValue)) {record.put(fieldApiName, false);}
                else if (String.isBlank(stringValue) && fieldDescribe.isNillable()) {record.put(fieldApiName, null);}
                else if (String.isBlank(stringValue) && !fieldDescribe.isNillable()) {
                    record.put(fieldApiName, false);
                    DebugLogUtil.info(METHOD_NAME + ': Blank value for non-nillable boolean field ' + fieldApiName + ' on ' + targetType + '. Set to false.', LOG_TAGS);
                }
            } else {
                DebugLogUtil.warn(METHOD_NAME + ': Unsupported field type for mapping: ' + sfType + ' for field ' + fieldApiName + ' on ' + targetType + '. Value: ' + stringValue, LOG_TAGS);
            }
        } catch (Exception e) {
            DebugLogUtil.error(METHOD_NAME + ': Error assigning value to field ' + fieldApiName + ' on SObject ' + record.getSObjectType() + ' with raw value ' + String.valueOf(rawValue), e, LOG_TAGS);
        }
    }
    
    private static Map<String, Object> getMapValue(Map<String, Object> data, String key) {
        if (data != null && data.containsKey(key) && data.get(key) instanceof Map<String, Object>) {
            return (Map<String, Object>) data.get(key);
        }
        return null;
    }
    
    private static Object getPathValue(Map<String, Object> data, String path) {
        if (data == null || String.isBlank(path)) {return null;}
        String[] keys = path.split('\\.');
        Object currentValue = data;
        for (String key : keys) {
            if (currentValue instanceof Map<String, Object>) {
                currentValue = ((Map<String, Object>) currentValue).get(key.trim());
                if (currentValue == null) {return null;}
            } else {
                return null;
            }
        }
        return currentValue;
    }
    
    private static String getPathValueAsString(Map<String, Object> data, String path) {
        Object val = getPathValue(data, path);
        return val != null ? String.valueOf(val) : null;
    }
    
    @TestVisible
    private static Decimal getPathValueAsDecimal(Map<String, Object> data, String path) {
        Object val = getPathValue(data, path);
        if (val == null || String.isBlank(String.valueOf(val))) {return null;}
        try { return Decimal.valueOf(String.valueOf(val).replaceAll('[^0-9\\.\\-]','')); }
        catch (TypeException e) {
            return null;
        }
    }
}
