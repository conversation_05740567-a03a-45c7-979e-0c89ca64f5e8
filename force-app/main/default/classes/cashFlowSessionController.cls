@SuppressWarnings('PMD.AvoidDeeplyNestedIfStmts, PMD.CognitiveComplexity, PMD.ExcessiveMethodLength, PMD.NcssMethodCount, PMD.StdCyclomaticComplexity, PMD')
public without sharing class cashFlowSessionController {

    private static final String CLASS_NAME = 'cashFlowSessionController';
    private static final List<String> LOG_TAGS = new List<String>{CLASS_NAME, 'CashflowProcessing'};
    private static final List<String> DML_TAGS = new List<String>{CLASS_NAME, 'DML_OPERATION'};
    private static final List<String> API_TAGS = new List<String>{CLASS_NAME, 'API_CALL'};
    private static final List<String> PARSING_TAGS = new List<String>{CLASS_NAME, 'PARSING'};

    // Wrapper class to hold prepared SObjects for DML
    private class PreparedObjects {
        List<Cashflow_Line_Item_Child__c> childrenToUpsert = new List<Cashflow_Line_Item_Child__c>();
        Map<String, Cashflow_Line_Item__c> parentsToUpsert = new Map<String, Cashflow_Line_Item__c>();
        List<Cashflow_Line_Item_Junction__c> junctionsToInsert = new List<Cashflow_Line_Item_Junction__c>();
    }

    // Restored MappingRule class to support dynamic field mapping
    public class MappingRule {
        @AuraEnabled public String dynamoDbId { get; set; }
        @AuraEnabled public String salesforceObject { get; set; }
        @AuraEnabled public String salesforceFieldApiName { get; set; }
        
        public String getDynamoSection() {
            return (String.isNotBlank(dynamoDbId) && dynamoDbId.contains(' - ')) ? dynamoDbId.substringBefore(' - ').trim() : null;
        }
        public String getDynamoFieldKey() {
            return (String.isNotBlank(dynamoDbId) && dynamoDbId.contains(' - ')) ? dynamoDbId.substringAfter(' - ').trim() : dynamoDbId;
        }
    }

    @AuraEnabled
    public static Map<String, Object> createRecordsFromDynamoData(String accountId, String dynamoJsonString, String projectId) {
        final String METHOD_NAME = 'createRecordsFromDynamoData';
        DebugLogUtil.entry(CLASS_NAME + '.' + METHOD_NAME, null, LOG_TAGS);

        Map<String, Object> result = new Map<String, Object>{'success' => false};
        if (String.isBlank(dynamoJsonString) || String.isBlank(projectId)) {
            result.put('errorMessage', 'JSON data and ProjectId are required.');
            DebugLogUtil.error(METHOD_NAME + ': JSON data or ProjectId was blank.', LOG_TAGS);
            return result;
        }

        Map<String, Object> dynamoData;
        List<MappingRule> mappingRules;
        try {
            dynamoData = (Map<String, Object>) JSON.deserializeUntyped(dynamoJsonString);
            // Fetch mapping rules dynamically, assuming service exists as in original class
            String mappingRulesJson = MFDynamoMappingService.getMappingRulesAsJson();
            mappingRules = (List<MappingRule>) JSON.deserialize(mappingRulesJson, List<MappingRule>.class);
        } catch (Exception e) {
            result.put('errorMessage', 'Error parsing inputs: ' + e.getMessage());
            DebugLogUtil.error(METHOD_NAME + ': Failed to parse JSON or get mapping rules.', e, PARSING_TAGS);
            return result;
        }

        SavePoint sp = Database.setSavepoint();
        DebugLogUtil.info(METHOD_NAME + ': Savepoint set.', DML_TAGS);

        try {
            // Step 1: Create and Insert Cashflow__c using dynamic mapping rules
            Cashflow__c cashflow = createCashflowRecord(dynamoData, projectId, mappingRules);
            insert cashflow;
            result.put('cashflowId', cashflow.Id);
            DebugLogUtil.info(METHOD_NAME + ': Cashflow__c inserted. Id: ' + cashflow.Id, DML_TAGS);

            // Step 2: Prepare all child, parent, and junction records in memory
            PreparedObjects preparedData = prepareLineItems(dynamoData);
            
            if (preparedData.childrenToUpsert.isEmpty()) {
                result.put('success', true);
                result.put('message', 'Cashflow created, but no line items were found in the JSON.');
                return result;
            }

            // Step 3: Link parent line items to the new Cashflow__c
            for (Cashflow_Line_Item__c parentItem : preparedData.parentsToUpsert.values()) {
                parentItem.Cashflow__c = cashflow.Id;
            }

            // Step 4: Perform bulk DML operations
            upsert preparedData.childrenToUpsert Cashflow_Line_Item_Child__c.Fields.External_Node_Id__c;
            upsert preparedData.parentsToUpsert.values() Cashflow_Line_Item__c.Fields.External_Week_Id__c;
            insert preparedData.junctionsToInsert;
            
            result.put('success', true);
            DebugLogUtil.info(METHOD_NAME + ': Record creation process completed successfully.', LOG_TAGS);

        } catch (Exception e) {
            Database.rollback(sp);
            DebugLogUtil.error(METHOD_NAME + ': Error during record creation. Transaction rolled back.', e, DML_TAGS);
            result.put('success', false);
            result.put('errorMessage', 'Error processing data: ' + e.getMessage() + ' Stacktrace: ' + e.getStackTraceString());
        }

        DebugLogUtil.exit(CLASS_NAME + '.' + METHOD_NAME, LOG_TAGS);
        return result;
    }

    /**
     * @description Prepares a Cashflow__c SObject by dynamically applying mapping rules.
     */
    private static Cashflow__c createCashflowRecord(Map<String, Object> dynamoData, String projectId, List<MappingRule> rules) {
        Cashflow__c cashflow = new Cashflow__c(Project__c = projectId);
        Map<String, Object> detailsMap = getMapValue(dynamoData, 'details');
        
        if (detailsMap == null) return cashflow;

        for (MappingRule rule : rules) {
            if (rule.salesforceObject == 'Cashflow__c' && rule.getDynamoSection() == 'details') {
                String fieldKey = rule.getDynamoFieldKey();
                if (detailsMap.containsKey(fieldKey)) {
                    Object rawValue = detailsMap.get(fieldKey);
                    assignFieldValue(cashflow, rule.salesforceFieldApiName, rawValue);
                }
            }
        }
        return cashflow;
    }
    
    private static PreparedObjects prepareLineItems(Map<String, Object> dynamoData) {
        PreparedObjects po = new PreparedObjects();
        Map<String, Object> incomeMap = getMapValue(dynamoData, 'income');
        if (incomeMap != null && incomeMap.get('lineItems') instanceof List<Object>) {
            for (Object item : (List<Object>)incomeMap.get('lineItems')) {
                Cashflow_Line_Item_Child__c child = createChildItem((Map<String, Object>)item, 'Project Revenue', getStringValue((Map<String, Object>)item, 'category'), null);
                if(child != null) {
                    po.childrenToUpsert.add(child);
                    aggregateAndLink(child, po.parentsToUpsert, po.junctionsToInsert);
                }
            }
        }

        if (dynamoData.get('expenses') instanceof List<Object>) {
            for (Object expenseGroupObj : (List<Object>)dynamoData.get('expenses')) {
                Map<String, Object> expenseGroupData = (Map<String, Object>)expenseGroupObj;
                if (expenseGroupData.get('lineItems') instanceof List<Object>) {
                    for (Object item : (List<Object>)expenseGroupData.get('lineItems')) {
                        Cashflow_Line_Item_Child__c child = createChildItem((Map<String, Object>)item, 'Project Cost', getStringValue(expenseGroupData, 'category'), getStringValue(expenseGroupData, 'name'));
                        if(child != null) {
                             po.childrenToUpsert.add(child);
                             aggregateAndLink(child, po.parentsToUpsert, po.junctionsToInsert);
                        }
                    }
                }
            }
        }
        return po;
    }

    private static Cashflow_Line_Item_Child__c createChildItem(Map<String, Object> itemData, String type, String category, String groupName) {
        String nodeId = getStringValue(itemData, 'id');
        if(String.isBlank(nodeId)) return null;

        Cashflow_Line_Item_Child__c child = new Cashflow_Line_Item_Child__c();
        child.External_Node_Id__c = nodeId;
        child.Type__c = type;
        child.Line_Item_Category__c = category;
        child.Planned_Amount__c = getDecimalValue(itemData, 'amount');
        Date weekDate = getDateValue(itemData, 'week');
        child.Week_Start_Date__c = weekDate;
        child.Planned_Date__c = getDateValue(itemData, 'weekDue') == null ? weekDate : getDateValue(itemData, 'weekDue');

        if(category == 'subContractors') child.Subcontractor_Name__c = groupName;
        else if (category == 'materialOrders') child.Material_Order_Name__c = groupName;
        return child;
    }

    private static void aggregateAndLink(Cashflow_Line_Item_Child__c child, Map<String, Cashflow_Line_Item__c> parents, List<Cashflow_Line_Item_Junction__c> junctions) {
        if(child.Week_Start_Date__c == null || String.isBlank(child.Line_Item_Category__c)) return;

        String externalWeekId = child.Line_Item_Category__c + '_' + child.Week_Start_Date__c.format();

        Cashflow_Line_Item__c parent = parents.get(externalWeekId);
        if (parent == null) {
            parent = new Cashflow_Line_Item__c(External_Week_Id__c = externalWeekId, Line_Item_Category__c = child.Line_Item_Category__c, Week_Start_Date__c = child.Week_Start_Date__c, Type__c = child.Type__c, Planned_Amount__c = child.Planned_Amount__c);
            parents.put(externalWeekId, parent);
        } else {
            parent.Planned_Amount__c = (parent.Planned_Amount__c == null ? 0 : parent.Planned_Amount__c) + (child.Planned_Amount__c == null ? 0 : child.Planned_Amount__c);
        }

        Cashflow_Line_Item_Junction__c junction = new Cashflow_Line_Item_Junction__c();
        junction.putSObject('Cashflow_Line_Item__r', new Cashflow_Line_Item__c(External_Week_Id__c = externalWeekId));
        junction.putSObject('Cashflow_Line_Item_Child__r', new Cashflow_Line_Item_Child__c(External_Node_Id__c = child.External_Node_Id__c));
        junctions.add(junction);
    }
    
    // --- GENERIC & HELPER METHODS ---

    /**
     * @description Assigns a value to an SObject field, converting types where possible.
     * This method is essential for dynamic mapping.
     */
    private static void assignFieldValue(SObject record, String fieldApiName, Object rawValue) {
        if (String.isBlank(fieldApiName) || rawValue == null) return;
        
        String stringValue = String.valueOf(rawValue);
        Schema.SObjectType targetType = record.getSObjectType();
        Map<String, Schema.SObjectField> fieldMap = targetType.getDescribe().fields.getMap();

        if (!fieldMap.containsKey(fieldApiName.toLowerCase())) return;

        Schema.DescribeFieldResult fieldDescribe = fieldMap.get(fieldApiName.toLowerCase()).getDescribe();
        Schema.DisplayType sfType = fieldDescribe.getType();

        try {
            if (sfType == Schema.DisplayType.BOOLEAN) {
                record.put(fieldApiName, 'yes'.equalsIgnoreCase(stringValue) || 'true'.equalsIgnoreCase(stringValue));
            } else if (sfType == Schema.DisplayType.DATE) {
                record.put(fieldApiName, Date.valueOf(stringValue));
            } else if (sfType == Schema.DisplayType.DATETIME) {
                record.put(fieldApiName, Datetime.valueOf(stringValue.replace(' ', 'T')));
            } else if (sfType == Schema.DisplayType.CURRENCY || sfType == Schema.DisplayType.DOUBLE || sfType == Schema.DisplayType.PERCENT || sfType == Schema.DisplayType.INTEGER) {
                record.put(fieldApiName, Decimal.valueOf(stringValue));
            } else {
                record.put(fieldApiName, stringValue);
            }
        } catch (Exception e) {
            DebugLogUtil.error('Failed to assign value "' + stringValue + '" to field ' + fieldApiName, e, LOG_TAGS);
        }
    }

    private static Map<String, Object> getMapValue(Map<String, Object> data, String key) {
        if (data != null && data.get(key) instanceof Map<String, Object>) {
            return (Map<String, Object>) data.get(key);
        }
        return null;
    }

    private static String getStringValue(Map<String, Object> data, String key) {
        return (data != null && data.containsKey(key) && data.get(key) != null) ? String.valueOf(data.get(key)) : null;
    }

    private static Decimal getDecimalValue(Map<String, Object> data, String key) {
        Object val = data.get(key);
        if (val instanceof Decimal || val instanceof Double || val instanceof Integer) {
             return (Decimal)val;
        }
        try {
            return String.isBlank(String.valueOf(val)) ? null : Decimal.valueOf(String.valueOf(val));
        } catch (Exception e) {
            return null;
        }
    }

    private static Date getDateValue(Map<String, Object> data, String key) {
        try {
            return String.isBlank(String.valueOf(data.get(key))) ? null : Date.valueOf(String.valueOf(data.get(key)));
        } catch (Exception e) {
            return null;
        }
    }
    
    // --- OTHER PUBLIC METHODS (UNCHANGED) ---
    @AuraEnabled(cacheable=true)
    public static List<Cash_Flow_Session__c> getRecords(){
        final String METHOD_NAME = 'getRecords';
        DebugLogUtil.entry(CLASS_NAME + '.' + METHOD_NAME, null, LOG_TAGS);
        
        List<Cash_Flow_Session__c> cfsrecords = new List<Cash_Flow_Session__c>();
        try {
            cfsrecords = [
                SELECT Id, Name, UpdatedAt__c, LatestStep__c, DeletedAt__c, AccountId__c, UserId__c, Configuration__c,
                CurrentStep__c, SessionId__c, VideosProgress__c, CompletedAt__c, CreatedAt__c, Processed_JSON__c
                FROM Cash_Flow_Session__c
                Order by CreatedAt__c DESC
                LIMIT 50000
            ];
            DebugLogUtil.info(METHOD_NAME + ': Fetched ' + cfsrecords.size() + ' Cash_Flow_Session__c records.', LOG_TAGS);
        } catch (Exception e) {
            DebugLogUtil.error(METHOD_NAME + ': Error fetching Cash_Flow_Session__c records.', e, LOG_TAGS);
            throw new AuraHandledException('Error fetching records: ' + e.getMessage());
        }
        
        DebugLogUtil.exit(CLASS_NAME + '.' + METHOD_NAME, LOG_TAGS);
        return cfsrecords;
    }
    
    @AuraEnabled
    public static Map<String,Object> shareSessionWithApex(String endpointUrl) {
        final String METHOD_NAME = 'shareSessionWithApex';
        Map<String, Object> params = new Map<String, Object>{'endpointUrl' => endpointUrl};
        DebugLogUtil.entry(CLASS_NAME + '.' + METHOD_NAME, params, API_TAGS);
        
        Map<String,Object> returnMap = new Map<String, Object>();
        try {
            HttpRequest req = new HttpRequest();
            req.setEndpoint(endpointUrl);
            req.setMethod('POST');
            Http http = new Http();
            
            DebugLogUtil.info(METHOD_NAME + ': Sending POST request to endpoint: ' + endpointUrl, API_TAGS);
            HttpResponse res = http.send(req);
            
            returnMap.put('statusCode', res.getStatusCode());
            returnMap.put('status', res.getStatus());
            returnMap.put('body', res.getBody());
            
            DebugLogUtil.info(METHOD_NAME + ': Response received. Status Code: ' + res.getStatusCode() + ', Status: ' + res.getStatus(), API_TAGS);
            
            if (res.getStatusCode() >= 200 && res.getStatusCode() < 300) {
                DebugLogUtil.info(METHOD_NAME + ': Callout successful.', API_TAGS);
            } else {
                DebugLogUtil.error(METHOD_NAME + ': Error from callout. Status Code: ' + res.getStatusCode() + ', Body: ' + res.getBody(), API_TAGS);
            }
            DebugLogUtil.exit(CLASS_NAME + '.' + METHOD_NAME, API_TAGS);
            return returnMap;
        } catch (Exception e) {
            DebugLogUtil.error(METHOD_NAME + ': Exception during callout.', e, API_TAGS);
            DebugLogUtil.exit(CLASS_NAME + '.' + METHOD_NAME, API_TAGS);
            throw new AuraHandledException('Error in sharing session: ' + e.getMessage());
        }
    }
}