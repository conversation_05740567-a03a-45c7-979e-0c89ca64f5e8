@SuppressWarnings('PMD')
public class UIT_Utility {

    /**
    * @description Logs the exception details into Custom_Exception__c object with the provided record ID, exception, and class name.
    * @param recId The record ID associated with the error.
    * @param ex The exception object containing error details.
    * @param className The name of the class where the exception occurred.
    **/
    public static void LogException(String recId, Exception ex, String className) {
        
        Custom_Exception__c ce = new Custom_Exception__c();
        ce.ClassName__c = className;
        ce.Exception_Message__c = ex.getMessage() + ' : ' + ex.getCause();
        ce.Line_Number__c = String.valueOf(ex.getLineNumber());
        // ce.MethodName__c = ex.getStackTraceString();
        // ce.Exception_Type__c = ex.getTypeName();
        // ce.StackTrace__c = ex.getStackTraceString();
        // ce.User__c = UserInfo.getUserId();
        ce.Error_RecordId__c = String.valueOf(recId);
        ce.Type__c = 'Error';
        ce = setRecordIdField(ce, recId);
        
        insert ce;
    }
    
    /**
    * @description Logs the exception details into Custom_Exception__c object with the provided record ID, exception, and class name.
    * @param recId The record ID associated with the error.
    * @param ex The exception object containing error details.
    * @param className The name of the class where the exception occurred.
    **/
    public static Custom_Exception__c LogExceptionStub(String recId, Exception ex, String className) {
        
        Custom_Exception__c ce = new Custom_Exception__c();
        ce.ClassName__c = className;
        ce.Exception_Message__c = ex.getMessage() + ' : ' + ex.getCause();
        ce.Line_Number__c = String.valueOf(ex.getLineNumber());
        // ce.MethodName__c = ex.getStackTraceString();
        // ce.Exception_Type__c = ex.getTypeName();
        // ce.StackTrace__c = ex.getStackTraceString();
        // ce.User__c = UserInfo.getUserId();
        ce.Error_RecordId__c = String.valueOf(recId);
        ce.Type__c = 'Error';
        ce = setRecordIdField(ce, recId);
        
        return ce;
    }

    /**
    * @description Logs the exception details into Custom_Exception__c object with the provided record ID and exception.
    * @param recId The record ID associated with the error.
    * @param ex The exception object containing error details.
    **/
    public static void LogException(ID recId, Exception ex) {
        
        Custom_Exception__c ce = new Custom_Exception__c();
        ce.Exception_Message__c = ex.getMessage() + ' : ' + ex.getCause();
        ce.Line_Number__c = String.valueOf(ex.getLineNumber());
        // ce.MethodName__c = ex.getStackTraceString();
        // ce.Exception_Type__c = ex.getTypeName();
        // ce.StackTrace__c = ex.getStackTraceString();
        // ce.User__c = UserInfo.getUserId();
        ce.Error_RecordId__c = String.valueOf(recId);
        ce.Type__c = 'Error';
        ce = setRecordIdField(ce, recId);
        
        insert ce;
    }

    /**
    * @description Logs the request and response details into Custom_Exception__c object with the provided record ID, status code, request JSON, and response JSON.
    * @param recId The record ID associated with the request.
    * @param StatusCode The status code of the response.
    * @param JsonReq The JSON request payload.
    * @param JsonRes The JSON response payload.
    **/
    public static void LogRequestAndResponse(String recId, Integer StatusCode, String JsonReq, String JsonRes) {
        
        Custom_Exception__c ce = new Custom_Exception__c();
        ce.Error_RecordId__c = String.valueOf(recId);
        ce.Type__c = 'Callout';
        ce.Status_Code__c = StatusCode;
        ce = setRecordIdField(ce, recId);
        
        ce.Request_Json__c = JsonReq;
        ce.Response_Json__c = JsonRes;
        
        insert ce;
    }

    public static Custom_Exception__c LogRequestAndResponseStub(String recId, Integer StatusCode, String JsonReq, String JsonRes) {
        return LogRequestAndResponseStub(recId, StatusCode, JsonReq, JsonRes, null, null);
    }

    
    /**
    * @description Logs the request and response details into Custom_Exception__c object with the provided record ID, status code, request JSON, and response JSON.
    * @param recId The record ID associated with the request.
    * @param StatusCode The status code of the response.
    * @param JsonReq The JSON request payload.
    * @param JsonRes The JSON response payload.
    **/
    public static Custom_Exception__c LogRequestAndResponseStub(String recId, Integer StatusCode, String JsonReq, String JsonRes, String dbAsyncJobId, String parentId) {
        
        Custom_Exception__c ce = new Custom_Exception__c();
        ce.Error_RecordId__c = String.valueOf(recId);
        ce.Type__c = 'Callout';
        ce.Status_Code__c = StatusCode;
        ce = setRecordIdField(ce, recId);
        
        ce.Request_Json__c = JsonReq;
        ce.Response_Json__c = JsonRes;
        ce.Dropbox_Async_Job_Id__c = dbAsyncJobId;
        ce.Related_To__c = parentId;
        if(parentId != null) {
            ce.Related_To_Link__c = '/'+parentId;
        }
        
		return ce;
    }

    /**
    * @description Logs the request and response details into Custom_Exception__c object with the provided list of record IDs, status code, request JSON, and response JSON.
    * @param recIds The list of record IDs associated with the request.
    * @param StatusCode The status code of the response.
    * @param JsonReq The JSON request payload.
    * @param JsonRes The JSON response payload.
    **/
    public static void LogRequestAndResponse(List<String> recIds, Integer StatusCode, String JsonReq, String JsonRes) {
        
        Custom_Exception__c ce = new Custom_Exception__c();
        ce.Error_RecordId__c = String.valueOf(recIds[0]);
        ce.Type__c = 'Callout';
        ce.Status_Code__c = StatusCode;
        
        for (ID id : recIds) {
            ce = setRecordIdField(ce, id);
        }
        
        ce.Request_Json__c = JsonReq;
        ce.Response_Json__c = JsonRes;
        
        insert ce;
    }

    /**
    * @description Logs the request and response details into Custom_Exception__c object with the provided record ID, request JSON, and response JSON.
    * @param recId The record ID associated with the request.
    * @param JsonReq The JSON request payload.
    * @param JsonRes The JSON response payload.
    **/
    public static void LogRequestAndResponse(String recId, String JsonReq, String JsonRes) {
        
        Custom_Exception__c ce = new Custom_Exception__c();
        ce.Error_RecordId__c = String.valueOf(recId);
        ce.Type__c = 'Callout';
        ce = setRecordIdField(ce, recId);
        ce.Request_Json__c = JsonReq;
        ce.Response_Json__c = JsonRes;
        
        insert ce;
    }

    /**
    * @description Sets the record ID field dynamically in the Custom_Exception__c object.
    * @param ce The Custom_Exception__c object to update.
    * @param recId The record ID to set in the appropriate field.
    * @return The updated Custom_Exception__c object with the record ID field set.
    **/
    private static Custom_Exception__c setRecordIdField(Custom_Exception__c ce, String recId) {
        if (recId != null && String.isNotBlank(recId)) {
            String objectName = ((Id)recId).getSObjectType().toString();
            String fieldName = objectName;
            if (!objectName.endsWith('__c')) {
                fieldName += '__c';
            }
            Map<String, Object> ceMap = (Map<String, Object>) JSON.deserializeUntyped(JSON.serialize(ce));
            ceMap.put(fieldName, recId);
            ce = (Custom_Exception__c) JSON.deserialize(JSON.serialize(ceMap), Custom_Exception__c.class);
        }

        return ce;
    }

        /**
    * @description Logs the request and response details into Custom_Exception__c object for flinks.
    * @param recId The record ID associated with the request.
    * @param StatusCode The status code of the response.
    * @param JsonReq The JSON request payload.
    * @param JsonRes The JSON response payload.
    * @param reqId The Request Id used for unique callout
    **/
    @AuraEnabled
    public static void LogFlinksCallout( String JsonReq, String JsonRes, String loginId, String flinksFc, String reqId, Integer statusCode, String contactId, Boolean isAdmin) {

        User loggedInUser = [SELECT Id,contactId FROM User WHERE Id =: UserInfo.getUserId()];
        
        Flinks_Logs__c fl = new Flinks_Logs__c();
        
        if(loggedInUser.contactId != null){
            fl.Contact__c = loggedInUser.contactId;
        }else if(contactId != null){
            fl.Contact__c = contactId;
        }else{
            fl.User__c = loggedInUser.Id;
        }

        fl.Login_Id__c = loginId;
        fl.Flinks_Function__c = flinksFc;
        fl.Request_Json__c = JsonReq;

        // Update logic for handling Response_Json__c, Is_Response_Body_In_Files__c,
        // and creating an Attachment for large responses.
        // Also incorporates setting Status_Code__c and Request_Id__c before insertion.
        fl.Status_Code__c = statusCode;
        fl.Request_Id__c = reqId;

        if (JsonRes != null && JsonRes.length() > 131010) {
            fl.Is_Response_Body_In_Files__c = true;
            fl.Response_Json__c = 'Response body is too large and has been stored as an Attachment. Please check the Files related to this Flinks Log record.';
            
            insert fl;

            if (fl.Id != null) { 
                try {
                    Attachment att = new Attachment();
                    att.ParentId = fl.Id;
                    att.Name = 'FlinksResponse_' + String.valueOf(fl.Id) + '.txt'; 
                    att.Body = Blob.valueOf(JsonRes);
                    att.ContentType = 'text/plain'; 
                    insert att;
                } catch (Exception e) {
                    System.debug(LoggingLevel.ERROR, 'UIT_Utility.LogFlinksCallout: Failed to create Attachment for Flinks_Logs__c ID ' + 
                                 fl.Id + '. Error: ' + e.getMessage() + '. StackTrace: ' + e.getStackTraceString());
                    
                }
            }
        } else {
            fl.Response_Json__c = JsonRes;
            fl.Is_Response_Body_In_Files__c = false;
            insert fl; 
        }
    }
    
}