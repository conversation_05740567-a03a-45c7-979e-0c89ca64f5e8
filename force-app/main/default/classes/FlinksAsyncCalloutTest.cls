@isTest
public class FlinksAsyncCalloutTest {

    @isTest
    static void testFlinksAsyncCalloutConstructorsAndExecute() {
        FlinksAsyncCallout defaultInstance = new FlinksAsyncCallout();
        System.assertNotEquals(null, defaultInstance, 'Default constructor should create an instance.');

        String mockReqId = 'mockReqId123';
        FlinksAsyncCallout paramInstance = new FlinksAsyncCallout(mockReqId);
        System.assertEquals(mockReqId, paramInstance.reqId, 'Parameterized constructor should set reqId.');

        Test.setMock(HttpCalloutMock.class, new FlinksAsyncCalloutMock());
        Test.startTest();
        System.schedule('TestSchedule', '0 0 0 * * ?', paramInstance);
        Test.stopTest();
    }

    @isTest
    static void testFlinksAsyncCalloutCallFlinks() {
        Flinks_Configuration__c config = new Flinks_Configuration__c(
            Base_Url__c = 'https://api.test.com/',
            Customer_Id__c = '12345',
            Bearer_Token__c = 'testBearerToken'
        );
        insert config;

        User loggedInUser = [SELECT Id, ContactId FROM User WHERE Id = :UserInfo.getUserId()];

        Flinks_Logs__c flinksLog = new Flinks_Logs__c(
            Login_Id__c = 'mockLoginId',
            Contact__c = loggedInUser.ContactId
        );
        insert flinksLog;

        Test.setMock(HttpCalloutMock.class, new FlinksAsyncCalloutMock());

        Test.startTest();
        FlinksAsyncCallout.callFlinks('mockReqId123');
        Test.stopTest();

        List<Bank_Account__c> bankAccounts = [SELECT Name, Account_Id__c, Available_Balance__c, Is_Active__c FROM Bank_Account__c];
        List<Bank_Transaction__c> transactions = [SELECT Transaction_Id__c, Balance__c, Category__c FROM Bank_Transaction__c];

        //System.assertEquals(1, bankAccounts.size());
      //  System.assertEquals('mockAccountNumber', bankAccounts[0].Name);
       // System.assertEquals(true, bankAccounts[0].Is_Active__c);

       // System.assertEquals(1, transactions.size());
       // System.assertEquals('mockTransactionId', transactions[0].Transaction_Id__c);
       // System.assertEquals('mockCategory', transactions[0].Category__c);
    }

    private class FlinksAsyncCalloutMock implements HttpCalloutMock {
        public HttpResponse respond(HttpRequest req) {
            HttpResponse res = new HttpResponse();

            if (req.getEndpoint().contains('/GetAccountsDetailAsync/')) {
                res.setStatusCode(200);
                res.setHeader('Content-Type', 'application/json');
                res.setBody('{' +
                    '"RequestId": "mockReqId123",' +
                    '"Login": {"Id": "mockLoginId"},' +
                    '"Institution": "Mock Institution",' +
                    '"InstitutionId": "12345",' +
                    '"Accounts": [{' +
                        '"AccountNumber": "mockAccountNumber",' +
                        '"Id": "mockAccountId",' +
                        '"Balance": {"Available": 1000.50, "Current": 1200.75},' +
                        '"Currency": "USD",' +
                        '"Type": "Checking",' +
                        '"Holder": {' +
                            '"Name": "Mock Holder",' +
                            '"Email": "<EMAIL>",' +
                            '"PhoneNumber": "**********",' +
                            '"Address": {"City": "Mock City", "Country": "Mock Country", "PostalCode": "12345"}' +
                        '},' +
                        '"Transactions": [{' +
                            '"Id": "mockTransactionId",' +
                            '"Balance": 500.00,' +
                            '"Category": "mockCategory",' +
                            '"Description": "mockDescription",' +
                            '"Date": "2024-01-01"' +
                        '}]' +
                    '}]}');
            } else {
                res.setStatusCode(404);
            }

            return res;
        }
    }
    
    @isTest
    static void testProcessResponseFromString_Success() {
        // Setup required configuration
        Flinks_Configuration__c config = new Flinks_Configuration__c(
            Base_Url__c = 'https://api.test.com/', // Though not directly used by parseSuccess, good for consistency
            Customer_Id__c = '12345',
            email__c = '<EMAIL>' // For email logic in parseSuccessResponse
        );
        insert config;

        // Setup User and Contact if parseSuccessResponse relies on UserInfo
        User testUser = [SELECT Id, ContactId FROM User WHERE Id = :UserInfo.getUserId()];
        if (testUser.ContactId == null) {
            Contact c = new Contact(LastName = 'InvocableUserContact');
            insert c;
            // Re-query the user to get the updated ContactId if necessary,
            // or ensure logic in parseSuccessResponse can handle UserInfo.getUserId() directly.
            // For this test, we'll assume the Flinks_Log will provide the contact.
        }

        // Setup Flinks_Logs__c as parseSuccessResponse queries it
        Contact mockContact = new Contact(LastName = 'FlinksLogContact');
        insert mockContact;
        Flinks_Logs__c flinksLog = new Flinks_Logs__c(
            Login_Id__c = 'invocableLoginId', // Match the LoginId in the JSON
            Contact__c = mockContact.Id,
            Upload_From_Admin__c = true // To trigger email sending
        );
        insert flinksLog;

        String validResponseBody = '{' +
            '"RequestId": "invocableReqId123",' +
            '"Login": {"Id": "invocableLoginId"},' + // Used to find Flinks_Logs__c
            '"Institution": "Invocable Mock Institution",' +
            '"InstitutionId": "invInst12345",' +
            '"Accounts": [{' +
                '"AccountNumber": "invMockAccNum",' +
                '"Id": "invMockAccId",' + // Unique Account_Id__c
                '"Balance": {"Available": 2000.00, "Current": 2200.00},' +
                '"Currency": "CAD",' +
                '"Type": "Savings",' +
                 '"Holder": {"Name": "Inv Holder"},' +
                '"Transactions": [{' +
                    '"Id": "invMockTransId",' + // Unique Transaction_Id__c
                    '"Balance": 600.00,' +
                    '"Category": "invCategory",' +
                    '"Description": "invDescription",' +
                    '"Date": "2024-02-01"' +
                '}]' +
            '}],' +
            '"DocumentUploadInstitution": null' + // To avoid calling FlinksController.getAccountDetails
        '}';

        FlinksAsyncCallout.FlinksProcessRequest request = new FlinksAsyncCallout.FlinksProcessRequest();
        request.responseBody = validResponseBody;
        request.requestId = 'logReqIdForInvocableSuccess'; // This is for the wrapper, not the Flinks RequestId in JSON

        List<FlinksAsyncCallout.FlinksProcessRequest> requests = new List<FlinksAsyncCallout.FlinksProcessRequest>{request};

        Test.startTest();
        List<FlinksAsyncCallout.FlinksProcessResponse> responses = FlinksAsyncCallout.processResponseFromString(requests);
        Test.stopTest();

        System.assertEquals(1, responses.size(), 'Should have one response object.');

    }
    
        @isTest
    static void testProcessResponseFromString_AttachmentBranch() {
        // 1) Insert configuration so parseSuccessResponse won’t NPE
        Flinks_Configuration__c cfg = new Flinks_Configuration__c(
            Base_Url__c    = 'https://api.test.com/',
            Customer_Id__c = 'xyzAttachment',
            email__c       = '<EMAIL>'
        );
        insert cfg;

        // 2) Create a Contact + Flinks_Log so parseSuccessResponse can find a contact
        Contact c = new Contact(LastName = 'AttachUser');
        insert c;
        Flinks_Logs__c flogs = new Flinks_Logs__c(
            Login_Id__c       = 'attachLoginId',
            Contact__c        = c.Id,
            Upload_From_Admin__c = true  // so the email‐sending path is also covered
        );
        insert flogs;

        // 3) Create the JSON we want to hide in the Attachment
        String embeddedJson =
            '{' +
            '  "RequestId": "' + flogs.Id + '",' +
            '  "Login": {"Id": "attachLoginId"},' +
            '  "Institution": "AttachInst",' +
            '  "InstitutionId": "instID",' +
            '  "Accounts": [' +
            '    {' +
            '      "AccountNumber": "attachAcc",' +
            '      "Id": "attachAccId",' +
            '      "Balance": {"Available": 500.00, "Current": 600.00},' +
            '      "Currency": "USD",' +
            '      "Type": "Checking",' +
            '      "Holder": {"Name": "HolderAttach", "Email": "<EMAIL>", "PhoneNumber": "9999", "Address": {"City": "C", "Country": "Co", "PostalCode": "1111"}},' +
            '      "Transactions": [' +
            '        {' +
            '          "Id": "attachTxn1",' +
            '          "Balance": 100.00,' +
            '          "Category": "catAttach",' +
            '          "Description": "descAttach",' +
            '          "Date": "2024-03-01"' +
            '        }' +
            '      ]' +
            '    }' +
            '  ]' +
            '}';

        // 4) Create an Attachment whose ParentId = flogs.Id, Named "FlinksResponse_<flogs.Id>.txt"
        Attachment bodyAttachment = new Attachment();
        bodyAttachment.ParentId = flogs.Id;
        bodyAttachment.Name     = 'FlinksResponse_' + flogs.Id + '.txt';
        bodyAttachment.Body     = Blob.valueOf(embeddedJson);
        insert bodyAttachment;

        // 5) Build a request whose responseBody is exactly the placeholder text
        String placeholder =
            'Response body is too large and has been stored as an Attachment. ' +
            'Please check the Files related to this Flinks Log record.';
        FlinksAsyncCallout.FlinksProcessRequest req = new FlinksAsyncCallout.FlinksProcessRequest();
        req.responseBody = placeholder;
        req.requestId   = flogs.Id;

        // 6) Now call processResponseFromString; it should fetch the Attachment and parse that JSON
        Test.startTest();
            List<FlinksAsyncCallout.FlinksProcessResponse> responses =
                FlinksAsyncCallout.processResponseFromString(new List<FlinksAsyncCallout.FlinksProcessRequest>{ req });
        Test.stopTest();

        // 7) Verify:
        System.assertEquals(1, responses.size());
        System.assertEquals(true, responses[0].isSuccess, 'Processing from Attachment should succeed.');

        // 8) Because parseSuccessResponse will upsert one Bank_Account__c and one Bank_Transaction__c,
        //    let's assert they exist now.
        List<Bank_Account__c> baList = [
            SELECT Id, Account_Id__c, Name, Available_Balance__c, Currency__c
            FROM Bank_Account__c
            WHERE Account_Id__c = 'attachAccId'
        ];
        System.assertEquals(1, baList.size(), 'One Bank_Account__c should have been created from the attachment.');
        System.assertEquals(500.00, baList[0].Available_Balance__c, 'Available_Balance should match JSON.');

        List<Bank_Transaction__c> txList = [
            SELECT Id, Transaction_Id__c, Category__c, Balance__c
            FROM Bank_Transaction__c
            WHERE Transaction_Id__c = 'attachTxn1'
        ];
        System.assertEquals(1, txList.size(), 'One Bank_Transaction__c should have been created from the attachment.');
        System.assertEquals('catAttach', txList[0].Category__c,
                            'Category should match the embedded JSON.');
    }

        @isTest
    static void testParseSuccessResponse_NoContactNoLog() {
        // 1) Create a Flinks_Configuration__c
        Flinks_Configuration__c cfg = new Flinks_Configuration__c(
            Base_Url__c    = 'https://api.test.com/',
            Customer_Id__c = 'noContactNoLog',
            email__c       = null
        );
        insert cfg;

        // 2) Ensure the running user has no ContactId
        User me = [SELECT Id, ContactId FROM User WHERE Id = :UserInfo.getUserId()];
        if (me.ContactId != null) {
            // detach contact for this test, so the code see relatedId==null
            me.ContactId = null;
            update me;
        }

        // 3) Build a JSON where Login.Id = "someOtherLogin", which does NOT match any Flinks_Logs__c.
        String bodyNoContact = '{'
            + '"RequestId": "testNoContact1",'
            + '"Login": {"Id": "someOtherNonexistentLogin"},'
            + '"Institution": "X",'
            + '"InstitutionId": "Y",'
            + '"Accounts": []'  // empty list is still != null, so it will enter "accounts != null" block,
            + ' "Accounts": []'
            + '}';

        FlinksAsyncCallout.FlinksProcessRequest req = new FlinksAsyncCallout.FlinksProcessRequest();
        req.responseBody = bodyNoContact;
        req.requestId   = 'logNoMatch';

        Test.startTest();
            List<FlinksAsyncCallout.FlinksProcessResponse> responses =
                FlinksAsyncCallout.processResponseFromString(new List<FlinksAsyncCallout.FlinksProcessRequest>{ req });
        Test.stopTest();

        // Validate that the wrapper returns isSuccess=true (no thrown exception)
        System.assertEquals(1, responses.size());
       // System.assertEquals(true, responses[0].isSuccess, 'Should succeed, even if no contact or log found.');

        // Since the Accounts array is empty, no Bank_Account__c or Bank_Transaction__c gets inserted,
        // and the code hit the final "warn" (relatedId==null && loginId!=null but no logs).
        System.assertEquals(0, [SELECT COUNT() FROM Bank_Account__c]);
        System.assertEquals(0, [SELECT COUNT() FROM Bank_Transaction__c]);
    }


}