<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Payment_Terms__c</fullName>
    <description>Stores the terms value from the parent expense group</description>
    <externalId>false</externalId>
    <inlineHelpText>Payment terms for this expense (e.g., COD, Net 30, etc.)</inlineHelpText>
    <label>Payment Terms</label>
    <required>false</required>
    <trackHistory>false</trackHistory>
    <trackTrending>false</trackTrending>
    <type>Picklist</type>
    <valueSet>
        <restricted>true</restricted>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>cod</fullName>
                <default>false</default>
                <label>COD</label>
            </value>
            <value>
                <fullName>net_30</fullName>
                <default>false</default>
                <label>Net 30</label>
            </value>
            <value>
                <fullName>net_45</fullName>
                <default>false</default>
                <label>Net 45</label>
            </value>
            <value>
                <fullName>net_60</fullName>
                <default>false</default>
                <label>Net 60</label>
            </value>
            <value>
                <fullName>net_90</fullName>
                <default>false</default>
                <label>Net 90</label>
            </value>
        </valueSetDefinition>
    </valueSet>
</CustomField>
